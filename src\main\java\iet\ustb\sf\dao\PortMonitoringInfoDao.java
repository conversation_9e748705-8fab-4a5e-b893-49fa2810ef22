package iet.ustb.sf.dao;

import iet.ustb.sf.vo.PortMonitoringInfo;
import iet.ustb.sf.vo.domain.Dictionary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;


/**
 * <AUTHOR>
 */
public interface PortMonitoringInfoDao extends JpaSpecificationExecutor<PortMonitoringInfo>, JpaRepository<PortMonitoringInfo, String> {


}
