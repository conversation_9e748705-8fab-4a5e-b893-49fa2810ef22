package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.ExpandMenuDao;
import iet.ustb.sf.dao.ResourceDao;
import iet.ustb.sf.vo.domain.ExpandMenu;
import iet.ustb.sf.vo.domain.Resource;
import iet.ustb.sf.service.ExpandMenuService;
import iet.ustb.sf.utils.ToolsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.service.impl
 * @title: ExpandMenuServiceImpl
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/2919:11
 */

@Service
public class ExpandMenuServiceImpl implements ExpandMenuService {

    @Autowired
    ExpandMenuDao expandMenuDao;

    @Autowired
    ResourceDao resourceDao;

    @Override
    public String doSaveExpandMenu(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return "";
        } else {
            return doSave(jsonObject);
        }
    }


    public String doSave(JSONObject jsonObject) {
        String id = jsonObject.getString("id");
        String rscID = jsonObject.getString("rscID");
        String userNo = jsonObject.getString("userNo");
        List<String> ids = ToolsUtil.jsonObjectToEntityList(jsonObject.get("rscIDs"), String.class);
        List<Resource> resourceList = resourceDao.findAllById(ids);
        ExpandMenu expandMenu = null;
        if (ToolsUtil.isEmpty(id)) {
            expandMenu = new ExpandMenu();
        } else {
            expandMenu = expandMenuDao.getById(id);
        }
        expandMenu.setRscID(rscID);
        expandMenu.setUserNo(userNo);
        expandMenu.setResourceList(resourceList);
        return expandMenuDao.save(expandMenu).getId();
    }

    @Override
    public String doUpdateExpandMenu(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return "";
        } else {
            return doSave(jsonObject);
        }
    }

    @Override
    public ExpandMenu findExpandMenuByID(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return null;
        } else {
            return expandMenuDao.getById(jsonObject.getString("id"));
        }

    }

    @Override
    public ExpandMenu findExpandMenuByUserNoAndRscID(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return null;
        } else {
            return expandMenuDao.findExpandMenuByUserNoAndRscID(jsonObject.getString("rscID"), jsonObject.getString("userNo"));
        }
    }
}
