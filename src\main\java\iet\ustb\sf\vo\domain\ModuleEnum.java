package iet.ustb.sf.vo.domain;

/**
 * 模块枚举
 *
 * <AUTHOR>
 * @create 2023-01-06
 * @see Enum
 */
public enum ModuleEnum {

    DataNode("数据节点", 1),
    FactoryServiceEngine("工厂服务引擎", 2),
    MaterialTracking("物料跟踪", 3),
    WisdomProduction("智慧生产", 4),
    WisdomQuality("智慧质量", 5),
    WisdomEnergy("智慧能源", 6),
    WisdomCost("智慧成本", 7),
    WisdomOperation("智慧运维", 8),
    CooperativeTransportation("协同运管", 9),
    DigitalFactory("数字工厂", 10),
    OneSteelMakingEquipment("一炼钢设备监测", 11),
    WideSteelMakingEquipment("宽厚板设备监测", 12),
    WideSteelMakingMonitor("宽厚板振动监测", 13);

    private final String name;
    private final Integer code;

    ModuleEnum(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }
}
