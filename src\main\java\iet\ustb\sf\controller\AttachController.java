package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.Attach;
import iet.ustb.sf.service.AttachService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 附件
 *
 * <AUTHOR>
 * @create 2022-10-26
 */
@RestController
@RequestMapping("/attach")
@Api(value = "附件", tags = "附件")
public class AttachController {

    @Autowired
    private AttachService attachService;

    @PostMapping("/findAll")
    @ApiOperation(value = "查询所有", notes = "查询所有")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<Attach> attachList = attachService.findAll();
            ajaxJson.setData(attachList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findAllByMultiCondition")
    @ApiOperation(value = "按多条件查找所有", notes = "入参："
            + "{\"ids\": [\"e9f3452e-8c68-4d90-8e60-6d986c2d0353\",\"cfae33ba-404f-45fb-ad93-e7f8454a4dd9\"], "
            + "\"relatedId\": \"630c56f3-f960-4278-ad02-fc1acfcee98e\"}")
    public AjaxJson findAllByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<Attach> attachList = attachService.findAllByMultiCondition(jsonObject);
            ajaxJson.setData(attachList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findPageByMultiCondition")
    @ApiOperation(value = "按多条件查找分页", notes = "入参："
            + "{\"ids\": [\"e9f3452e-8c68-4d90-8e60-6d986c2d0353\",\"cfae33ba-404f-45fb-ad93-e7f8454a4dd9\"], "
            + "\"relatedId\": \"630c56f3-f960-4278-ad02-fc1acfcee98e\"}")
    public AjaxJson findPageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<Attach> attachPage = attachService.findPageByMultiCondition(jsonObject);
            ajaxJson.setData(attachPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @Deprecated
    @PostMapping("/save")
    @ApiOperation(value = "保存", notes = "保存")
    public AjaxJson save(@RequestBody Attach attach) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            attachService.save(attach);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除附件", notes = "入参：{\"ids\":[\"f356e461-0565-4ed4-bed1-29fb810bb160\",\"f356e461-0565-4ed4-bed1-29fb810bb165\"]}")
    public AjaxJson delete(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            attachService.deleteByIds(jsonObject);
            ajaxJson.setData("删除成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping(value = "/uploadFile", headers = "content-type=multipart/form-data")
    @ApiOperation(value = "上传附件", notes = "上传附件")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", name = "files", value = "多个附件", allowMultiple = true, dataType = "__file"),
            @ApiImplicitParam(paramType = "query", name = "relatedId", dataType = "String", required = false, value = "关联 ID", defaultValue = ""),
            @ApiImplicitParam(paramType = "query", name = "serviceNo", dataType = "String", required = false, value = "业务编号", defaultValue = "")
    })
    public AjaxJson uploadFile(@RequestPart @ApiParam(value = "files") MultipartFile[] files,
                               @RequestParam(value = "relatedId", required = false) String relatedId,
                               @RequestParam(value = "serviceNo", required = false) String serviceNo) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<Attach> attachList = attachService.uploadFile(files, relatedId, serviceNo);
            ajaxJson.setData(attachList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    @GetMapping("/downloadFileById/{id}")
    @ApiOperation(value = "按id下载附件")
    public AjaxJson downloadById(@PathVariable String id, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");// 解决跨域问题
            attachService.downloadFileById(id, response);
            ajaxJson.setData("下载成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

}
