//package iet.ustb.sf.service.impl;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import iet.ustb.sf.dao.OrgDao;
//import iet.ustb.sf.dao.RoleDao;
//import iet.ustb.sf.dao.UserDao;
//import iet.ustb.sf.domain.Role;
//import iet.ustb.sf.domain.User;
//import iet.ustb.sf.service.UserService;
//import iet.ustb.sf.service.WeComMessagePushService;
//import iet.ustb.sf.utils.DateUtils;
//import iet.ustb.sf.utils.ToolsUtil;
//import lombok.extern.apachecommons.CommonsLog;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.http.ResponseEntity;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.ArrayList;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//
///**
// * @Author: Dr.Monster
// * @Title: WeComMessagePushServiceImpl
// * @Date: 23/08/21 14:3218
// * @Slogan: The never-compromising angry youth
// * @Remark:$
// */
//
////https://developer.work.weixin.qq.com/document/path/90236
//
//    //1：获取token
//    //2：通过用户查询手机号
//    //3：通过手机号，调用企业微信查询接口，获取企业微信对应用户ID
//    //4：拼装消息结构体
//    //5：发送消息
//@Service
//@EnableScheduling
//@CommonsLog
//public class WeComMessagePushServiceImpl implements WeComMessagePushService {
//
//
//    @Value("${wecom.corpid}")
//    String corpid;
//
//    @Value("${wecom.secret}")
//    String secret;
//
//    @Value("${wecom.agentid}")
//    int agentid;
//
//    @Autowired
//    UserDao userDao;
//
//    @Autowired
//    RoleDao roleDao;
//
//    @Autowired
//    OrgDao orgDao;
//
//    @Autowired
//    RestTemplate restTemplate;
//
//    @Autowired
//    RedisTemplate redisTemplate;
//
//    @Autowired
//    UserService userService;
//
//
//    @Override
//    public String getAccessToken(){
//        String accessToken = null;
//        //从redis获取token
//        accessToken = getTokenFromRedis();
//        if(ToolsUtil.isEmpty(accessToken)){
//            String getTokenUrl = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + corpid + "&corpsecret=" + secret;
//            ResponseEntity responseEntity = restTemplate.getForEntity(getTokenUrl , JSONObject.class);
//            JSONObject resBody = (JSONObject) responseEntity.getBody();
//            accessToken = (String) resBody.get("access_token");
//            saveTokenToRedis(accessToken);
//        }
//        System.out.println("accessToken:" + accessToken);
//        return accessToken;
//    }
//
//    /**
//     * 两小时过期
//     * @param token
//     */
//    public void saveTokenToRedis(String token){
//        redisTemplate.opsForValue().set("wecomMsgToken" , token, 2, TimeUnit.HOURS);
//    }
//
//    public String getTokenFromRedis(){
//        try{
//            return (String) redisTemplate.opsForValue().get("wecomMsgToken");
//        }catch (Exception e){
//            return null;
//        }
//    }
//
//    @Override
//    public void sendMessage(JSONObject content) {
//        //获取token
//        String accessToken = getAccessToken();
//        if(ToolsUtil.isEmpty(accessToken)){
//            return;
//        }
//        //推送消息url
//        String pushUrl = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + accessToken;
//        log.warn("content:" + JSON.toJSONString(content));
//        JSONObject res = restTemplate.postForObject(pushUrl, content, JSONObject.class);
//        log.warn("resReturn:" + res.toJSONString());
//    }
//
//    public List<String> findUserIDByUserNo(List<String> userNos){
//        List<String> userIDs = new ArrayList<>();
//        try{
//            Set<String> userNoSet = new HashSet<>();
//            userNoSet.addAll(userNos);
//            List<User> userList = userDao.findUsersByUserNos(userNoSet);
//            String token = getAccessToken();
//            String getUserIDUrl = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserid?access_token=" + token;
//            JSONObject phone;
//            for(User user : userList){
//                if(ToolsUtil.isEmpty(user.getMobPhone())){
//                    continue;
//                }
//                String userID = user.getUserID();
//                if(ToolsUtil.isEmpty(userID)){
//                    phone = new JSONObject();
//                    phone.put("mobile" , user.getMobPhone());
//                    //（userNo -> phoneNumber） -> userId（企业微信）
//                    JSONObject res = restTemplate.postForObject(getUserIDUrl, phone, JSONObject.class);
//                    System.out.println("findUserIDByUserNo:" + res.toJSONString());
//                    userID = (String) res.get("userid");
//                    if(!ToolsUtil.isEmpty(userID)){
//                        user.setUserID(userID);
//                    }
//                }
//                if(!ToolsUtil.isEmpty(userID)){
//                    userIDs.add(userID);
//                }
//            }
//            saveUsers(userList);
//        }catch (Exception e){
//            log.warn(e.getMessage());
//        }
//        return userIDs;
//    }
//
//
//    @Async
//    public void saveUsers(List<User> userList){
//        userDao.saveAllAndFlush(userList);
//    }
//
//
//
//    //创建消息体
//    JSONObject buildContentByTextMessage(List<String> userNos , String message){
//        List<String> userIDs = findUserIDByUserNo(userNos);
//        StringBuilder sb = new StringBuilder();
//        for(String userID : userIDs){
//            sb.append(userID);
//            //分隔符
//            sb.append("|");
//        }
//        JSONObject content = new JSONObject();
//        JSONObject textContent  = new JSONObject();
//        textContent.put("content" , message);
//        content.put("touser" , ToolsUtil.replaceLast(sb.toString() , "|" , ""));
////        content.put("touser" , "18611511455");
//        content.put("toparty" , "");
//        content.put("totag", "");
//        content.put("msgtype", "text");
//        content.put("agentid", agentid);
//        content.put("text", textContent);
//        content.put("safe", 0);
//        content.put("enable_id_trans", 0);
//        content.put("enable_duplicate_check", 1);
//        content.put("duplicate_check_interval", 3);
//        return content;
//    }
//
//
//    JSONObject buildTextCardContent(List<String> userNos) {
//        List<String> userIDs = findUserIDByUserNo(userNos);
//        StringBuilder sb = new StringBuilder();
//        for (String userID : userIDs) {
//            sb.append(userID);
//            //分隔符
//            sb.append("|");
//        }
//        JSONObject content = new JSONObject();
//        JSONObject textcard = new JSONObject();
//        textcard.put("title", "报警统计");
//        textcard.put("description", "报警统计,点击跟多展示详情");
//        textcard.put("url", "http://bcbjts.nisco.cn/noLogin/WidgetWarningSituation");
//        textcard.put("btntxt", "更多");
//
//
//        content.put("touser", ToolsUtil.replaceLast(sb.toString(), "|", ""));
////        content.put("touser" , "18611511455");
//        content.put("toparty", "");
//        content.put("totag", "");
//        content.put("msgtype", "textcard");
//        content.put("agentid", agentid);
//        content.put("textcard", textcard);
//        content.put("enable_id_trans", 0);
//        content.put("enable_duplicate_check", 0);
//        content.put("duplicate_check_interval", 1800);
//        return content;
//    }
//
//
//    @Override
//    public void sendToUsersByTextMessage(JSONObject jsonObject) {
//        List<String> userNos = ToolsUtil.jsonObjectToEntityList(jsonObject.get("userNos"), String.class);
//        String message = jsonObject.getString("message");
//        JSONObject content = buildContentByTextMessage(userNos, message);
//        sendMessage(content);
//    }
//
//    @Override
//    public void sendToRolesByTextMessage(JSONObject jsonObject) {
//
//        if(!ToolsUtil.isEmpty(jsonObject.get("roleIDs"))){
//            List<String> roleIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("roleIDs"), String.class);
//            List<String> userNos = new ArrayList<>();
//            Role role;
//            for (String roleID : roleIDs){
//                role = roleDao.getById(roleID);
//                if(ToolsUtil.isEmpty(role)){
//                    continue;
//                }
//                if(ToolsUtil.isEmpty(role.getUserList())){
//                    continue;
//                }
//                userNos.addAll(role.getUserList().stream().map(User::getUserNo).distinct().collect(Collectors.toList()));
//            }
//            JSONArray jsonArray = jsonObject.getJSONArray("extraUserNos");
//            if (jsonArray != null || jsonArray.size() == 0) {
//                List<String> extraUserNos = jsonArray.toJavaList(String.class);
//                userNos.addAll(extraUserNos);
//                userNos = userNos.stream().distinct().collect(Collectors.toList());
//            }
//
//            String message = jsonObject.getString("message");
//            JSONObject content = buildContentByTextMessage(userNos, message);
//            sendMessage(content);
//        }
//    }
//
//
//    @Async
//    @Override
//    public void sendToUsers(List<User> userList , String message) {
//        if(ToolsUtil.isEmpty(userList)){
//            return;
//        }
//        List<String> userNos = userList.stream().map(x -> x.getUserNo()).collect(Collectors.toList());
//        JSONObject content = buildContentByTextMessage(userNos , message);
//        sendMessage(content);
//    }
//
//
//    @Async
//    @Override
//    public void sendToUsersByUserNosByTextMessage(List<String> userNos , String message) {
//        if(ToolsUtil.isEmpty(userNos)){
//            return;
//        }
//        JSONObject content = buildContentByTextMessage(userNos , message);
//        sendMessage(content);
//    }
//
//    @Async
//    @Override
//    public void sendToUsersByUserNosByTextCard(List<String> userNos) {
//        if(ToolsUtil.isEmpty(userNos)){
//            return;
//        }
//        JSONObject content = buildTextCardContent(userNos);
//        sendMessage(content);
//    }
//
//    @Override
//    public void sendToRoles(JSONObject jsonObject) {
//        List<String> roleIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("roleIDs"), String.class);
//        String message = jsonObject.getString("message");
//        List<String> userNos = new ArrayList<>();
//        for (String roleID : roleIDs) {
//            Role role = roleDao.getById(roleID);
//            if (!ToolsUtil.isEmpty(role) && !ToolsUtil.isEmpty(role.getUserList())) {
//                userNos.addAll(role.getUserList().stream().map(x -> x.getUserNo()).collect(Collectors.toList()));
//            }
//        }
//        JSONObject content = buildContentByTextMessage(userNos , message);
//        sendMessage(content);
//    }
//
//    @Override
//    public void sendToTags(JSONObject jsonObject) {
//        List<String> orgCodes = ToolsUtil.jsonObjectToEntityList(jsonObject.get("orgCodes"), String.class);
//        List<String> userNos = userDao.findUsersByOrgCodes(orgCodes).stream().map(x -> x.getUserNo()).collect(Collectors.toList());
//        String message = jsonObject.getString("message");
//        JSONObject content = buildContentByTextMessage(userNos , message);
//        sendMessage(content);
//    }
//
//    @Transactional
//    public void sendKbToUsers(){
//        List<String> roleIDs = new ArrayList();
//        roleIDs.add("5f394a15-f309-4ef6-9bd8-37bd2a8d5881");
////        roleIDs.add("13ba6147-bc6b-40ca-9329-c5002d88a8a7");
////        Map<String, List<UserVo>> userListByRoleIDs = userService.findUserListByRoleIDs(obj);
//        JSONObject obj = new JSONObject();
//        obj.put("setDate", DateUtils.getStringToDay());
//        obj.put("output","1");
//        JSONObject result = restTemplate.postForObject("http://172.25.63.67:9800/mesAPI/productionQuality/findAllBySetDate", obj, JSONObject.class);
//        String message = result.getJSONArray("data").getJSONObject(0).getString("remark");
//        List<String> userNos = new ArrayList<>();
//        for (String roleID : roleIDs) {
//            Role role = roleDao.getById(roleID);
//            if (!ToolsUtil.isEmpty(role) && !ToolsUtil.isEmpty(role.getUserList())) {
//                userNos.addAll(role.getUserList().stream().map(x -> x.getUserNo()).collect(Collectors.toList()));
//            }
//        }
//        JSONObject content = buildContentByTextMessage(userNos , message);
//        sendMessage(content);
//    }
//}
