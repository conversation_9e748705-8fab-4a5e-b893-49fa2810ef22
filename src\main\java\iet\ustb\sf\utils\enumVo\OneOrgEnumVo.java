package iet.ustb.sf.utils.enumVo;


/**
 * 厂处枚举
 *
 * <AUTHOR>
 * @create 2024-01-30
 * @see Enum
 */
public enum OneOrgEnumVo {

    C1("X32" , "板卷厂"),
    C2("X38" , "宽板厂"),
    C3("X66" , "中板厂"),
    C4("X73" , "一炼钢厂"),
    D1("X5001" , "综合处"),
    D2("X5002" , "生产处"),
    D3("X5003" , "质量处"),
    D4("X5004" , "设备处"),
    D5("X5007" , "技术研发处");
    OneOrgEnumVo(String code, String name) {
        this.code = code;
        this.name = name;
    }
    String code;
    String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
