package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.service.WarningEventService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: Dr.Monster
 * @Slogan: The never-compromising angry youth.
 * @PackageName: iet.ustb.sf.controller
 * @Date: 2022/08/15/16:45
 * @Description:
 */
@RestController
@RequestMapping("/warningEvent")
@Api(value = "报警、信息推送", tags = "报警、信息推送")
public class WarningEventController {

    @Autowired
    WarningEventService warningEventService;

    @PostMapping("/saveAlertInfo")
    @ApiOperation(value = "保存离线报警、信息", notes = "保存离线报警、信息,{}", produces = "application/json")
    public void saveAlertInfo(@RequestBody JSONObject jsonObject) {
        try {
            warningEventService.saveAlertInfo(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @PostMapping("/updateAlertInfo")
    @ApiOperation(value = "更新离线报警、信息", notes = "更新离线报警、信息,{}", produces = "application/json")
    public void updateAlertInfo(@RequestBody JSONObject jsonObject) {
        try {
            warningEventService.updateAlertInfo(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @PostMapping("/findAlertInfoByMultipleChoices")
    @ApiOperation(value = "查询报警信息", notes = "查询报警信息,{\n" +
            "    \"alertLevel\": \"1-一般,2-严重,3-紧急\",\n" +
            "    \"alertType\": \"info-消息, alert-报警\",\n" +
            "    \"isOffline\": \"\",\n" +
            "    \"isPush\": \"\",\n" +
            "    \"serviceName\": \"res\",\n" +
            "    \"status\": \"0-未处理，1-已处理\",\n" +
            "    \"userNo\": \"\",\n" +
            "    \"areaName\": \"\",\n" +
            "    \"pointName\": \"\",\n" +
            "    \"deviceName\": \"\",\n" +
            "    \"warningType\": \"0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测\",\n" +
            "    \"startTime\":\"\",\n" +
            "    \"endTime\":\"\",\n" +
            "    \"pageIndex\": 1,\n" +
            "    \"pageSize\": 10\n" +
            "}", produces = "application/json")
    public AjaxJson findAlertInfoByMultipleChoices(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setData(warningEventService.findAlertInfoByMultipleChoices(jsonObject));
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }



    @PostMapping("/getUndoneRateByUserID")
    @ApiOperation(value = "查询当前用户所在角色未处理报警情况", notes = "查询当前用户所在角色未处理报警情况,{" +
            "}", produces = "application/json")
    public AjaxJson getUndoneRateByUserID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setData(warningEventService.getUndoneRateByUserID(jsonObject));
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @PostMapping("/findModuleInfoList")
    @ApiOperation(value = "获取模块列表", notes = "{}")
    public AjaxJson findModuleInfoList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningEventService.findModuleInfoList());
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping("/findAreaNameList")
    @ApiOperation(value = "获取区域名称列表", notes = "{}")
    public AjaxJson findAreaNameList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningEventService.findAreaNameList());
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findDeviceNameList")
    @ApiOperation(value = "获取设备名称列表", notes = "{}")
    public AjaxJson findDeviceNameList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningEventService.findDeviceNameList());
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findPointNameList")
    @ApiOperation(value = "获取设备名称列表", notes = "{}")
    public AjaxJson findPointNameList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningEventService.findPointNameList());
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

}
