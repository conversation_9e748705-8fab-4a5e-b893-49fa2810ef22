package iet.ustb.sf.vo.domain;

import lombok.Data;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.Proxy;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.domain
 * @title: 角色
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/1008:56
 */

@Entity
@Table(name = "DS_ROLE")
@Data
@Proxy(lazy = false)
public class Role extends BaseEntity {

    //角色编码
    @Column
    String roleCode;

    //角色名称
    @Column
    String roleName;

    //描述
    @Column
    String description;

    //类型
    @Column
    String type;

    // 是否是默认应用角色,1-是,2-否
    @Column
    String isDef;

    //状态,0-禁用,1-启用
    @Column
    int status;

    //角色类型，1-页面访问角色，2-报警推送角色，3-页面编辑角色,4-消息推送角色
    @Column
    Integer roleType;

    //组织架构ID，宽版，一炼钢等
    @Column
    String orgID;

    //模块名称,能源，设备
    @Column
    String moduleName;

    //模块code
    @Column
    String moduleCode;

    //是否新角色，0：否，1：是
    @Column
    Integer isNewRole;

    @ManyToMany
    @Cascade(value = org.hibernate.annotations.CascadeType.SAVE_UPDATE)
    List<User> userList;

    @ManyToMany
    @Cascade(value = org.hibernate.annotations.CascadeType.SAVE_UPDATE)
    List<Resource> resourceList;

    //菜单ID
    @Transient
    String resourceId;

    //用户ID
    @Transient
    String userId;
}
