<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="iet.ustb.sf.mapper.SysNoticeUserMapper">

    <resultMap type="iet.ustb.sf.vo.domain.SysNoticeUser" id="SysNoticeUserMap">
        <result property="noticeId" column="notice_id" jdbcType="VARCHAR"/>
        <result property="userNo" column="user_no" jdbcType="VARCHAR"/>
    </resultMap>


    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="SysNoticeUserMap">
        select
notice_id, user_no
        from sys_notice_user
        <where>
            <if test="noticeId != null and noticeId != ''">
                and notice_id = #{noticeId}
            </if>
            <if test="userNo != null and userNo != ''">
                and user_no = #{userNo}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from sys_notice_user
        <where>
            <if test="noticeId != null and noticeId != ''">
                and notice_id = #{noticeId}
            </if>
            <if test="userNo != null and userNo != ''">
                and user_no = #{userNo}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert"  useGeneratedKeys="true">
        insert into sys_notice_user(notice_id, user_no)
        values (#{noticeId}, #{userNo})
    </insert>

    <insert id="insertBatch" useGeneratedKeys="true">
        insert into sys_notice_user(notice_id, user_no)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.noticeId}, #{entity.userNo})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" useGeneratedKeys="true">
        insert into sys_notice_user(notice_id, user_no)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.noticeId}, #{entity.userNo})
        </foreach>
        on duplicate key update
        notice_id = values(notice_id),
        user_no = values(user_no)
    </insert>

    <select id="getNoticeByUserNo" resultType="iet.ustb.sf.vo.domain.SysNotice">
        select
            *
        from
            sys_notice sn
        where
            sn.id not in(
                select
                    snu.notice_id
                from
                    sys_notice_user snu
                where
                    snu.user_no = #{userNo}
            )
        and NOW() BETWEEN startTime AND endTime
        <if test="noticeCategory != null">
            and noticeCategory = #{noticeCategory}

        </if>

        <if test="status != null ">
            and status = #{status}
        </if>
    </select>


    <select id="getNoticeByPage" resultType="iet.ustb.sf.vo.domain.SysNotice">
        select
            *
        from
            sys_notice sn
        where
            status = 2
        <if test="noticeCategory != null">
            and noticeCategory = #{noticeCategory}
        </if>
        <if test="title != null and title != ''">
            and title like CONCAT('%',#{title},'%')
        </if>
        order by createdatetime desc
    </select>

    <delete id="deleteByNoticeId">
        delete from
            sys_notice_user sn
        where
            notice_id = #{noticeId}
    </delete>


</mapper>

