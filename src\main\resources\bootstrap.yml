server:
  port: 8081

config:
  #server: ******** # 配置服务器IP地址，默认端口号 8888
  loginMode: 2 # 登录模式 1:统一身份认证 2:本地登录
spring:
  application:
    name: iet-resource-service  # 应用名称
  cloud:
    config:
      enabled: false  # 显式禁用 Spring Cloud Config
    nacos:
      discovery:
        enabled: false
#        server-addr: localhost:8848 # Nacos服务地址
#        namespace: dw # Nacos命名空间ID
#        group: dwops
#        enabled: true
#        username: nacos
#        password: nacos
      config:
        enabled: false
#        server-addr: localhost:8848 # Nacos作为配置中心地址
#        namespace: dw # Nacos命名空间ID
#        group: iet # 配置分组名称
#        file-extension: yaml # 配置文件格式，默认为properties
#        enabled: true
#        username: nacos
#        password: nacos
#      auto-refresh: true
  jpa:
    hibernate:
      ddl-auto: none
      max_fetch_depth: 2
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5Dialect
        max_fetch_depth: 2
        format_sql: true
    show-sql: false
  mvc:
    date-format: yyyy-MM-dd HH:mm:ss  # 日期格式化
    contentnegotiation:
      favor-parameter: true
      media-types:
        json: application/json
    pathmatch:
      matching-strategy: ant_path_matcher
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      maxFileSize: 500MB # maxFileSize 单个数据大小
      maxRequestSize: 1024MB # maxRequestSize 总数据大小
  jackson:
    time-zone: GMT+8  # 指定日期格式化时区
    date-format: yyyy-MM-dd HH:mm:ss  # 日期格式化
management:
  endpoints:
    web:
      exposure:
        include: '*'

#配置feign
feign:
  httpclient:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 5000   # 连接超时时间（毫秒）
        readTimeout: 5000      # 读取超时时间（毫秒）
        loggerLevel: FULL      # 日志级别（NONE、BASIC、HEADERS、FULL）
        requestInterceptors:
          - iet.ustb.sf.config.interceptor.CustomFeignInterceptor # 自定义拦截器

#配置mybatis相关属性
mybatis:
  configuration:
    call-setters-on-nulls: true  # null 或空值时，是否调用 setter 方法
    map-underscore-to-camel-case: false  # 是否开启驼峰命名
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 开启 SQL 日志打印
  type-aliases-package: iet.ustb.sf.vo  # 通过包设置别名
  mapper-locations: classpath:mapper/*.xml  # 设置 mapper 映射文件的位置


knife4j:
  enable: true
  setting:
    language: zh-CN
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /doc.html

logging:
  level:
    root: info
    org.hibernate.SQL: debug
    org.hibernate.type.descriptor.sql.BasicBinder: trace
    org.springframework.orm.jpa: debug
    org.springframework.transaction: debug

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


