package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.AppEval;
import iet.ustb.sf.service.AppEvalService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 应用评价
 *
 * <AUTHOR>
 * @create 2023-12-14
 */
@RestController
@RequestMapping("/appEval")
@Api(value = "应用评价", tags = "应用评价")
public class AppEvalController {

    @Autowired
    private AppEvalService appEvalService;

    @PostMapping("/findAll")
    @ApiOperation(value = "查询所有", notes = "查询所有")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<AppEval> appEvalList = appEvalService.findAll();
            ajaxJson.setData(appEvalList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findPageByMultiCondition")
    @ApiOperation(value = "按多条件查找分页", notes = "入参：{\"serviceNo\": \"dap\", \"modelNo\": \"1\", \"valueType\":\"2\", \"inputUnit\": \"1\", \"startDate\":\"2023-12-15\", \"endDate\":\"2023-12-15\"}")
    public AjaxJson findPageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<AppEval> appEvalPage = appEvalService.findPageByMultiCondition(jsonObject);
            ajaxJson.setData(appEvalPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findWeekAppEval")
    @ApiOperation(value = "查询周应用评价", notes = "入参：{\"startDate\":\"2023-12-15\", \"endDate\":\"2023-12-31\", \"serviceNo\": \"ems\"}")
    public AjaxJson findWeekAppEval(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<Map<String, Object>> mapPage = appEvalService.findWeekAppEval(jsonObject);
            ajaxJson.setData(mapPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findGroupByServerAndModel")
    @ApiOperation(value = "查询应用评价统计", notes = "入参：{\"startDate\":\"2023-12-15\", \"endDate\":\"2023-12-31\", \"serviceNo\": \"ems\"}")
    public AjaxJson findGroupByServerAndModel(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<Map<String, Object>> mapList = appEvalService.findGroupByServerAndModel(jsonObject);
            ajaxJson.setData(mapList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存", notes = "保存")
    public AjaxJson save(@RequestBody AppEval appEval) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            appEvalService.save(appEval);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/saveAll")
    @ApiOperation(value = "批量保存", notes = "批量保存")
    public AjaxJson saveAll(@RequestBody List<AppEval> appEvals) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            appEvalService.saveAll(appEvals);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "删除")
    public AjaxJson delete(@RequestBody AppEval appEval) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            appEvalService.delete(appEval);
            ajaxJson.setData("删除成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/deleteByMultiKey")
    @ApiOperation(value = "按多个键删除", notes = "入参：{\"weekDate\":\"2023-12-22\", \"serviceNo\":\"kpi\"}")
    public AjaxJson deleteByMultiKey(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            appEvalService.deleteByMultiKey(jsonObject);
            ajaxJson.setData("删除成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportPdf")
    @ApiOperation(value = "导出Pdf",
            notes = "入参：{\"week\":\"一\", \"weekPeriod\":\"01.01-01.07\", \"startDate\": \"2024-01-01\", \"endDate\": \"2024-01-31\"}")
    public AjaxJson exportPdf(@RequestBody JSONObject jsonObject, HttpServletRequest request, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            String fileUrl = appEvalService.exportPdf(jsonObject, request, response);
            ajaxJson.setData(fileUrl);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportExcel")
    @ApiOperation(value = "导出Excel",
            notes = "入参：{\"loginTimeStart\":\"2022-10-31\",\"loginTimeEnd\":\"2022-11-04\"}")
    public AjaxJson exportExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            appEvalService.exportExcel(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

}
