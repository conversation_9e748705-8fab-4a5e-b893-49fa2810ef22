package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.ProgressMgt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ProgressMgtDao extends JpaSpecificationExecutor<ProgressMgt>, JpaRepository<ProgressMgt, String> {

    @Query("from ProgressMgt where parentId = ?1")
    public List<ProgressMgt> findByParentId(String parentId);
}
