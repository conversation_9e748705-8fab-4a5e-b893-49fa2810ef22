package iet.ustb.sf.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import iet.ustb.sf.config.NacosConfig;
import org.apache.poi.ss.formula.functions.Na;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 */
@Component
public class AuthUtil {
    @Autowired
    private NacosConfig nacosConfig;

    /**
     * 获取x-token
     * 接口鉴权的核心思想是：通过AK(appId)/SK(appSecret)生成x-token放到HTTP的请求头。
     * appId	zhihuizhuomian
     * appSecret	71E4DCD66BDEF2C77C53D2D66DE0B710
     * x-token生成算法：
     * x-token=base64(appId:timestamp:sha256(appSecret+timestamp))，其中,timestamp是当前时间戳（毫秒），鉴权时候会与数据中台服务器时间进行比较，误差不能超过5分钟。base64是base64编码，sha256是指sha256散列算法。
     * 示例：
     * timestamp:=1741229013000
     * x-token
     * :=base64(zhihuizhuomian:1741229013000:sha256(71E4DCD66BDEF2C77C53D2D66DE0B7101741229013000)
     * :=base64(zhihuizhuomian:1741229013000:28f36dc3b1662f623c5a9558ac1861660d8338c33fe349d3a4627c3e2f86b6c1)
     * :=emhpaHVpemh1b21pYW46MTc0MTIyOTAxMzAwMDoyOGYzNmRjM2IxNjYyZjYyM2M1YTk1NThhYzE4NjE2NjBkODMzOGMzM2ZlMzQ5ZDNhNDYyN2MzZTJmODZiNmMx
     * @return
     */
    public String generateXToken() {
        try {
            long timestamp = System.currentTimeMillis();
            // 1. 计算 SHA-256(APP_SECRET + timestamp)
            String secretString = nacosConfig.getAuthAppSecret() + timestamp;
            String sha256Hash = SecureUtil.sha256(secretString);

            // 2. 生成 "appId:timestamp:sha256Hash" 格式字符串
            String tokenString = Base64.encode(nacosConfig.getAuthAppId() + ":" + timestamp + ":" + sha256Hash);

            // 3. 进行 Base64 编码
            return tokenString;
        } catch (Exception e) {
            throw new RuntimeException("生成 x-token 失败", e);
        }
    }

    /**
     * sha256散列算法
     * @param data
     * @return
     * @throws NoSuchAlgorithmException
     */
    private static String sha256(String data) throws NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));

        // 转换为十六进制字符串
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}

