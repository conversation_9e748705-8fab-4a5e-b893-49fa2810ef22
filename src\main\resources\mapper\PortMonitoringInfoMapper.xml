<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="iet.ustb.sf.mapper.PortMonitoringInfoMapper">

    <resultMap type="iet.ustb.sf.vo.PortMonitoringInfo" id="PortMonitoringInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="dataFrom" column="data_from" jdbcType="VARCHAR"/>
        <result property="portName" column="port_name" jdbcType="VARCHAR"/>
        <result property="portMethod" column="port_method" jdbcType="VARCHAR"/>
        <result property="ip" column="ip" jdbcType="VARCHAR"/>
        <result property="requestDataType" column="request_data_type" jdbcType="VARCHAR"/>
        <result property="responseDataType" column="response_data_type" jdbcType="VARCHAR"/>
        <result property="requestData" column="request_data" jdbcType="VARCHAR"/>
        <result property="responseData" column="response_data" jdbcType="VARCHAR"/>
        <result property="responseDataSize" column="response_data_size" jdbcType="VARCHAR"/>
        <result property="portTime" column="port_time" jdbcType="VARCHAR"/>
        <result property="errorMessage" column="error_message" jdbcType="VARCHAR"/>
        <result property="portUrl" column="port_url" jdbcType="VARCHAR"/>
        <result property="callTime" column="call_time" jdbcType="VARCHAR"/>
        <result property="portCategory" column="port_category" jdbcType="VARCHAR"/>
        <result property="portType" column="port_type" jdbcType="INTEGER"/>
        <result property="portStatus" column="port_status" jdbcType="INTEGER"/>
        <result property="createDateTime" column="createdatetime" jdbcType="TIMESTAMP"/>
        <result property="createUserNo" column="createuserno" jdbcType="VARCHAR"/>
        <result property="updateDateTime" column="updatedatetime" jdbcType="TIMESTAMP"/>
        <result property="updateUserNo" column="updateuserno" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getPortMonitoringInfoByPage" resultMap="PortMonitoringInfoMap">
        select
        id,data_from,port_name,port_method,ip,request_data_type,response_data_type,port_time,port_url,call_time,port_category,port_type,port_status,createuserno
        from resource.port_monitoring_info
        <where>
            <if test="portMonitoringInfo.portStatus != null and portMonitoringInfo.portStatus != ''">
                and port_status = #{portMonitoringInfo.portStatus}
            </if>
            <if test="portMonitoringInfo.portName != null and portMonitoringInfo.portName != ''">
                and port_name like CONCAT('%', #{portMonitoringInfo.portName},'%')
            </if>
            <if test="portMonitoringInfo.callTime != null and portMonitoringInfo.callTime != ''">
                and call_time like CONCAT( #{portMonitoringInfo.callTime},'%')
            </if>
            <if test="portMonitoringInfo.portMethod != null and portMonitoringInfo.portMethod != ''">
                and port_method like CONCAT('%',#{portMonitoringInfo.portMethod},'%')
            </if>
            <if test="portMonitoringInfo.createUserNo != null and portMonitoringInfo.createUserNo != ''">
                and createuserno like CONCAT('%', #{portMonitoringInfo.createUserNo},'%')
            </if>

        </where>
        order by createdatetime desc

    </select>

    <select id="getRequestData" resultMap="PortMonitoringInfoMap">
        select
        id,request_data
        from resource.port_monitoring_info
        <where>
            id = #{id}
        </where>

    </select>

    <select id="getResponseData" resultMap="PortMonitoringInfoMap">
        select
        id,response_data
        from resource.port_monitoring_info
        <where>
            id = #{id}
        </where>

    </select>

    <select id="getErrorData" resultMap="PortMonitoringInfoMap">
        select
        id,error_message
        from resource.port_monitoring_info
        <where>
            id = #{id}
        </where>

    </select>
</mapper>

