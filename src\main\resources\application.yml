spring:
  profiles:
    active: test
  cloud:
    consul:
      enabled: false

#token的过期时间和刷新时间(毫秒)
expire-time: 6000000
refresh-time: 600000

#大数据中台的鉴权信息
auth:
  appId: z<PERSON><PERSON>zhuomian
  appSecret: 71E4DCD66BDEF2C77C53D2D66DE0B710

#第三方系统的API开关和配置
api:
  bd: "0"
  bd-name: "idm"
  bd-menu: "RES_293202842620137472_001"

#权限系统配置
permission:
  system:
    base:
      url: http://localhost:8285  # 权限系统基础URL
    timeout: 5000  # 接口调用超时时间(毫秒)
    cache:
      expire-minutes: 30  # 缓存过期时间(分钟)
