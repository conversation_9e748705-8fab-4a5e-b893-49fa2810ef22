package iet.ustb.sf.vo;

import lombok.Data;

import java.util.Objects;

/**
 * @Author: Dr.<PERSON>
 * @Title: DoneRateVo
 * @Date: 23/11/29 17:0350
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */
@Data
public class DoneRateVo {
    public String roleName;
    public String roleID;

    //完成
    public Integer done = 0;

    //未完成
    public Integer unDone = 0;

    //总计
    public Integer total = 0;

    //误报
    public Integer isFalse = 0;

    //完成率
    public String rate;

    //时间轴,曲线用
    public String dateTime;

    public String moduleCode;
    public String moduleName;

    //一级报警
    public Integer firstLevel = 0;

    //二级报警
    public Integer secondLevel = 0;

    //三级报警
    public Integer thirdLevel = 0;

    //不标准建议数（为空）
    public Integer adviceAtypia = 0;

    //不标准分析数（为空）
    public Integer analysisAtypia = 0;

    //填写处理分析的数量
    public Integer alertAdviceCount = 0;

    //2小时内处理完
    public Integer twoHourDone = 0;

    //4小时内处理完
    public Integer fourHourDone = 0;

    //8小时内处理完
    public Integer eightHourDone = 0;

    //8小时内未处理完
    public Integer gtEightHourDone = 0;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DoneRateVo that = (DoneRateVo) o;
        return Objects.equals(roleName, that.roleName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(roleName);
    }
}


