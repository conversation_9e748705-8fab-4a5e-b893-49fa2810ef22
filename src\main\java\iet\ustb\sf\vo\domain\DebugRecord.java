package iet.ustb.sf.vo.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 调试记录
 *
 * <AUTHOR>
 * @create 2023-04-19
 * @see BaseEntity
 */
@Data
@Entity
@Table(name = "debug_record")
@ApiModel(value = "调试记录")
public class DebugRecord extends BaseEntity{
    /**
     * 应用编号
     */
    @Column(nullable = false)
    @ApiModelProperty(value = "应用编号")
    private String serviceNo;

    /**
     * 调试内容
     */
    @Column(nullable = false, length = 1000)
    @ApiModelProperty(value = "调试内容")
    private String debugContent;

    /**
     * 解决方案
     */
    @Column(length = 1000)
    @ApiModelProperty(value = "解决方案")
    private String solution;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 类别
     */
    @ApiModelProperty(value = "类别 ：1-时间延误 2-问题整改 3-功能优化 4-计划调试 5-模型规则")
    private Integer category;

    /**
     * 状态
     */
    @ApiModelProperty(value = "处理状态 ：1-是 2-否 3-待处理 4-进行中")
    private Integer status;

    /**
     * 填表人
     */
    @ApiModelProperty(value = "填表人")
    private String fillUser;

}
