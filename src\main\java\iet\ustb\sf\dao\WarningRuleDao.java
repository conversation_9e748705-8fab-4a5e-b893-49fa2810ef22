package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.WarningRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: Dr.Monster
 * @Slogan: The never-compromising angry youth.
 * @PackageName: iet.ustb.sf.dao
 * @Date: 2022/08/15/18:40
 * @Description:
 */
public interface WarningRuleDao extends JpaSpecificationExecutor<WarningRule>, JpaRepository<WarningRule, Serializable> {

    @Query(value = "select distinct (areaID) , areaName from warning_rule" , nativeQuery = true)
    List<Map<String , String>> findAreaInfoList();

    @Query(value = "select distinct (productionLineID) , productionLineName from warning_rule" , nativeQuery = true)
    List<Map<String , String>> findProductionLineInfoList();

    @Query(value = "select distinct (deviceID) , deviceName from warning_rule" , nativeQuery = true)
    List<Map<String , String>> findDeviceInfoList();

    @Query(value = "select distinct (pointID) , pointName from warning_rule" , nativeQuery = true)
    List<Map<String , String>> findPointInfoList();

    @Query(value = "select distinct (moduleCode) , moduleName from warning_rule" , nativeQuery = true)
    List<Map<String , String>> findModuleInfoList();

    @Query(value = "select * from warning_rule where moduleCode in ?1 and status = 1" , nativeQuery = true)
    List<WarningRule> findWarningRulesByModuleCodes(List<String> moduleCodes);
}
