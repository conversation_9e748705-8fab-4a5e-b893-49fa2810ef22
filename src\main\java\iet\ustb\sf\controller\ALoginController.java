package iet.ustb.sf.controller;


import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import iet.ustb.sf.vo.domain.LoginLog;
import iet.ustb.sf.vo.domain.User;
import iet.ustb.sf.service.LoginLogService;
import iet.ustb.sf.service.UserService;
import iet.ustb.sf.utils.HttpRequestUtil;
import iet.ustb.sf.utils.JwtUtil;
import iet.ustb.sf.utils.SsoParamUtil;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 统一认证接口调用
 * @author: qixu
 * @date: 2022年06月08日 8:42
 */
@RestController
@RequestMapping("/login")
@Slf4j
@Api(value = "登录认证", tags = "登录认证")
public class ALoginController {

    /**
     * 登录模式
     */
    @Value("${config.loginMode}")
    private String loginMode;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private UserService userService;
    @Autowired
    private LoginLogService loginLogService;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RestTemplate restTemplate;

    private static final String CLIENT_ID = "NSG67oEBdi";
    private static final String CLIENT_SECRET = "aa4d6ff8-7839-4c5f-86af-2867fca82f79";
    // 如果是本地开发环境
    private static final String REDIRECT_URI = "http://127.0.0.1:8081/login/oauth2Callback";
    // 如果是部署到服务器的环境，使用实际的IP和端口
//    private static final String REDIRECT_URI = "http://服务器IP:8081/login/oauth2Callback";
    private static final String TOKEN_URL = "https://sso.nisco.cn/profile/oauth2/accessToken";
    private static final String PROFILE_URL = "https://sso.nisco.cn/profile/oauth2/profile";
    private static final String APP_KEY = "93b599864ef9d02dd79c1a6cd0255cd8";

    /**
     * OAuth2.0单点登录回调
     */
   @ResponseBody
    @PostMapping("/certify")
    @ApiOperation(value = "单点登录获取Token", notes = "传入code获取用户token信息")
    public AjaxJson certify(@RequestBody JSONObject requestBody, HttpServletRequest request) {
        AjaxJson ajaxJson = new AjaxJson();
        
        try {
            String code = requestBody.getString("code");
            if (code == null || code.isEmpty()) {
                throw new Exception("code不能为空");
            }
            
            // 获取 Access Token
            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            String timestamp = String.valueOf(System.currentTimeMillis());
            String nonceStr = UUID.randomUUID().toString().replace("-", "");

            params.add("client_id", CLIENT_ID);
            params.add("client_secret", CLIENT_SECRET);
            params.add("redirect_uri", REDIRECT_URI);
            params.add("code", code);
            params.add("grant_type", "authorization_code");
            params.add("oauth_timestamp", timestamp);
            params.add("nonce_str", nonceStr);

            String sign = getSign(params.toSingleValueMap(), APP_KEY + CLIENT_SECRET);
            params.add("sign", sign);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(TOKEN_URL, requestEntity, String.class);
            JSONObject jsonObject = JSONObject.parseObject(response.getBody());
            
            if (!"SUCCESS".equals(jsonObject.getString("msg"))) {
                throw new Exception("请求token失败：" + jsonObject.getString("msg"));
            }

            String accessToken = jsonObject.getString("access_token");
            
            // 获取用户信息
            MultiValueMap<String, Object> userParams = new LinkedMultiValueMap<>();
            String userTimestamp = String.valueOf(System.currentTimeMillis());
            String userNonce = UUID.randomUUID().toString().replace("-", "");
            
            userParams.add("client_secret", CLIENT_SECRET);
            userParams.add("client_id", CLIENT_ID);
            userParams.add("nonce_str", userNonce);
            userParams.add("access_token", accessToken);
            userParams.add("oauth_timestamp", userTimestamp);
            
            String userSign = getSign(userParams.toSingleValueMap(), APP_KEY + CLIENT_SECRET);
            userParams.add("sign", userSign);
            
            HttpEntity<MultiValueMap<String, Object>> userRequest = new HttpEntity<>(userParams, headers);
            ResponseEntity<String> userResponse = restTemplate.postForEntity(PROFILE_URL, userRequest, String.class);
            
            JSONObject userJson = JSONObject.parseObject(userResponse.getBody());
            if (!"SUCCESS".equals(userJson.getString("msg"))) {
                throw new Exception("请求用户信息失败");
            }
            
            // 获取用户信息
            String userId = userJson.getString("id");  // IAM返回的用户ID
            
            // 查询本地用户
            JSONObject queryParam = new JSONObject();
            queryParam.put("userNo", userId);
            User user = userService.findOneUserByUserNo(queryParam);
            
            if (user == null) {
                // 打印详细的调试日志
                log.info("=== 单点登录调试信息 ===");
                log.info("工号不存在，开始调试分析...");
                log.info("IAM返回的完整用户信息: {}", userJson.toJSONString());
                log.info("解析出的用户ID: {}", userId);
                log.info("用来查询本地用户的参数: {}", queryParam.toJSONString());
                log.info("用户属性信息: {}", userJson.get("attributes"));
                log.info("建议检查: IAM返回的用户标识字段是否与本地数据库userNo字段匹配");
                log.info("========================");
                JSONObject debugResult = new JSONObject();
                debugResult.put("error", "工号不存在");
                debugResult.put("iamUserInfo", userJson);  // IAM返回的完整用户信息
                debugResult.put("searchedUserNo", userId); // 我们用来查询的用户编号
                debugResult.put("message", "请查看iamUserInfo中的用户信息，确认正确的用户标识字段");

                ajaxJson.setSuccess(false);
                ajaxJson.setMessage("工号不存在，请查看返回的IAM用户信息");
                ajaxJson.setData(debugResult);
                return ajaxJson;
//                throw new Exception("工号不存在");
            }
            
            // 生成本地TOKEN
            String token = JwtUtil.token(user.getUserNo(), user.getUserName());
            
            // 将用户放入redis
            this.putUserToRedis(user);
            
            // 保存登录日志
            // 保存登录日志 - 手动设置用户信息
//            LoginLog loginLog = new LoginLog();
//            loginLog.setUserNo(user.getUserNo());
//            loginLog.setOrgCode(user.getOrgCode());
//            loginLogService.save(loginLog, request);
//            loginLogService.save(new LoginLog(), request);
            
            // 封装返回数据
            JSONObject result = new JSONObject();
            result.put("isModifiedPwd", user.getIsModifiedPwd() != null ? user.getIsModifiedPwd() : "Y");
            result.put("userNo", user.getUserNo());
            result.put("loginName", user.getUserNo());  // 使用userNo作为loginName
            result.put("loginPwd", "");  // 密码不返回，保持空字符串
            result.put("token", token);
            
            ajaxJson.setData(result);
            ajaxJson.setSuccess(true);
            
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
            e.printStackTrace();
        }
        
        return ajaxJson;
    }

    /**
     * 签名方法
     */
    private static String getSign(Map<String, Object> params, String secret) {
        String sign = "";
        StringBuilder sb = new StringBuilder();
        // 排序
        Set<String> keyset = params.keySet();
        TreeSet<String> sortSet = new TreeSet<String>();
        sortSet.addAll(keyset);
        Iterator<String> it = sortSet.iterator();
        // 加密字符串
        while (it.hasNext()) {
            String key = it.next();
            String value = params.get(key).toString();
            sb.append(key).append(value);
        }
        sb.append("appkey").append(secret);
        try {
            sign = md5(sb.toString()).toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sign;
    }

    /**
     * MD5加密
     */
    public static String md5(String s) {
        return new MD5().digestHex(s);
    }
//        /**
//     * 将用户放入redis
//     */
//    private void putUserToRedis(User user) {
//        if (user != null) {
//            String userJson = JSONObject.toJSONString(user);
//            Map map = JSONObject.parseObject(userJson, Map.class);
//
//            String key = "loginUser_" + user.getUserNo();
//            redisTemplate.opsForHash().putAll(key, map);
//            redisTemplate.opsForHash().getOperations().expire(key, 24, TimeUnit.HOURS);
//        }
//    }

    @RequestMapping(value = "/ssoLogin")
    public void ssoLogin(HttpServletRequest request, HttpServletResponse response) {

        try {
            String reqCon = request.getContextPath();
            String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + reqCon + "/";
            String userId = request.getParameter("userId");
            String url = request.getParameter("url");
            //如果userId不为空将userId、url以key和value形式保存到redis中
            if (null != userId && null != url) {
                stringRedisTemplate.opsForHash().put("ssoLogin", userId + "_ssoLogin", url);
            }
            //重定向到统一认证服务登录接口
            String redirectUrl = String.format("%s?client_id=%s&response_type=code&redirect_uri=%s&oauth_timestamp=%s",
                    SsoParamUtil.SSOLOGIN, SsoParamUtil.client_id, URLEncoder.encode(basePath + "login/ssoLoginCallBack", "utf-8"), Calendar.getInstance().getTimeInMillis());
            response.sendRedirect(redirectUrl);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "/ssoLoginCallBack")
    public void ssoLoginCallBack(HttpServletRequest request, HttpServletResponse response) {
        try {
            String userName = "";
            String accessToken = "";
            String userInfoParam = "";
            String strUserInfo = "";
            String userId = "";
            String reqCon = request.getContextPath();
            String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + reqCon + "/";
            //获取code
            String code = request.getParameter("code");
            //根据code去获取token
            String param = HttpRequestUtil.getAccessTokenParam(SsoParamUtil.client_id, SsoParamUtil.client_secert, basePath + "index", code);
            String backtoken = HttpRequestUtil.getResultByHttp(SsoParamUtil.SSOGETTOKEN, param);
            if (backtoken != null && !backtoken.isEmpty()) {
                JSONObject jsonToken = JSONObject.parseObject(backtoken);
                if (jsonToken.getString("msg").equals("SUCCESS")) {
                    accessToken = jsonToken.getString("access_token");
                    //根据token去获取用户信息
                    userInfoParam = HttpRequestUtil.getUserParam(SsoParamUtil.client_id, SsoParamUtil.client_secert, accessToken);
                    strUserInfo = HttpRequestUtil.getResultByHttp(SsoParamUtil.SSOGETINFO, userInfoParam);
                    JSONObject jsonUserInfo = JSONObject.parseObject(strUserInfo);
                    if (jsonUserInfo.getString("msg").equals("SUCCESS")) {
                        userId = jsonUserInfo.getString("id");
                        String attributes = jsonUserInfo.getString("attributes");
                        String replace = attributes.replace("[", "")
                                .replace("]", "")
                                .replace("},{", ",");
                        JSONObject parseObject = JSONObject.parseObject(replace);
                        userName = parseObject.getString("UserName");

                        JSONObject jsonObj = new JSONObject();
                        jsonObj.put("userNo", userId);
                        User user = userService.findOneUserByUserNo(jsonObj);
                        // 将用户放入redis
                        this.putUserToRedis(user);

                        // 保存登陆日志
                        loginLogService.save(new LoginLog(), request);
                    }
                }
            }
            //生成自己的token
            String myToken = JwtUtil.token(userId, userName);
            //从redis中获取登录用户的url
            String url = (String) stringRedisTemplate.opsForHash().get("ssoLogin", userId + "_ssoLogin");

            if (null != url) {
                response.sendRedirect(url + "?org=redirect&token=" + myToken + "&userId=" + userId);
                stringRedisTemplate.delete("ssoLogin");
            } else {
//                response.sendRedirect("http://www.baidu.com");
                response.sendRedirect("http://10.0.11.181:9730?org=redirect&token=" + myToken + "&userId=" + userId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping(value = "/getToken")
    public String getToken(@RequestBody JSONObject content) {
        String userInfoParam = "";
        String strUserInfo = "";
        String userId="";
        String userName="";
        String token="用户不存在";
        try {
            String accessToken = content.getString("accessToken");
            //根据token去获取用户信息
            userInfoParam = HttpRequestUtil.getUserParam(SsoParamUtil.client_id, SsoParamUtil.client_secert,accessToken);
            strUserInfo = HttpRequestUtil.getResultByHttp(SsoParamUtil.SSOGETINFO, userInfoParam);
            JSONObject jsonUserInfo = JSONObject.parseObject(strUserInfo);
            if (jsonUserInfo.getString("msg").equals("SUCCESS")) {
                userId = jsonUserInfo.getString("id");
                String attributes = jsonUserInfo.getString("attributes");
                String replace = attributes.replace("[", "")
                        .replace("]", "")
                        .replace("},{", ",");
                JSONObject parseObject = JSONObject.parseObject(replace);
                userName = parseObject.getString("UserName");
                token = JwtUtil.token(userId, userName);
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return token;
    }

    @ResponseBody
    @ApiOperation(value = "登陆页认证校验", notes = "入参：{\"userNo\":\"023958\",\"password\":\"123456\"}", produces = "application/json")
    @PostMapping("/loginPageAuthVerify")
    public AjaxJson loginPageAuthVerify(@RequestBody JSONObject jsonObj) {

        AjaxJson ajaxJson = new AjaxJson();
        try {
            User user = userService.findOneUserByUserNo(jsonObj);
            Assert.notNull(user, "用户名不存在");

            String userNo = user.getUserNo();
            String userName = user.getUserName();
            String password = user.getPassword();
            String isModifiedPwd = user.getIsModifiedPwd();
            // 校验密码
            if (!password.equals(jsonObj.getString("password"))) {
                throw new Exception("密码不正确");
            }
            // 获取token
            String token = JwtUtil.token(userNo, userName);

            JSONObject json = new JSONObject();
            json.put("token", token);
            json.put("userNo", userNo);
//            json.put("Authorization", "Bearer " + token);
            json.put("isModifiedPwd", isModifiedPwd);

            // 将用户放入redis
            this.putUserToRedis(user);

            ajaxJson.setData(json);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    /**
     * 将用户放入redis
     *
     * @param user 用户
     * <AUTHOR>
     * @create 2023-04-11
     */
    private void putUserToRedis(User user) {
        if (user != null) {
            String userJson = JSONObject.toJSONString(user);
            Map map = JSONObject.parseObject(userJson, Map.class);

            String key = "loginUser_" + user.getUserNo();
            redisTemplate.opsForHash().putAll(key, map);
            redisTemplate.opsForHash().getOperations().expire(key,24, TimeUnit.HOURS);
        }
    }

    @ApiOperation(value = "获取登录模式", notes = "登录模式 1:统一身份认证 2:本地登录")
    @PostMapping("/getLoginMode")
    public AjaxJson getLoginMode() {
        AjaxJson ajaxJson = new AjaxJson();

        JSONObject json = new JSONObject();
        json.put("loginMode", loginMode);
        ajaxJson.setData(json);

        ajaxJson.setSuccess(true);
        return ajaxJson;
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public AjaxJson logout(HttpServletRequest request) {
        AjaxJson ajaxJson = new AjaxJson();
        String authorization = request.getHeader("authorization");
        String userNo = JwtUtil.getUserNoByToken(authorization.replace("Bearer ",""));
//        redisTemplate.delete(Constant.IET_TOKEN + userNo);
        redisTemplate.delete("loginUser_" + userNo);
        ajaxJson.setSuccess(true);

        return ajaxJson;
    }
}

