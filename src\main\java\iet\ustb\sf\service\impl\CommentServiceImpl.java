package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.CommentDao;
import iet.ustb.sf.vo.domain.Comment;
import iet.ustb.sf.service.CommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 问题反馈评论
 * <AUTHOR>
 * @create 2022-11-03
 */
@Service
@Transactional
public class CommentServiceImpl implements CommentService {

    @Autowired
    private CommentDao commentDao;

    @Override
    public List<Comment> findAll() {
        return commentDao.findAll();
    }

    @Override
    public Page<Comment> findPageByMultiCondition(JSONObject jsonObject) {
        return null;
    }

    @Override
    public List<Comment> findTreeByFeedBackId(JSONObject jsonObject) {
        String feedBackId = jsonObject.getString("feedBackId");
        // 按问题反馈id查找
        List<Comment> treeList = commentDao.findByFeedBackId(feedBackId);

        List<Comment> commentList = treeList.stream()
                .filter(item -> "0".equals(item.getParentId()))
                .map(item -> {
                    item.setChildren(getChildren(item, treeList));
                    return item;
                }).collect(Collectors.toList());
        return commentList;
    }

    private static List<Comment> getChildren(Comment treeEntity, List<Comment> treeEntityList) {
        List<Comment> collect = treeEntityList.stream()
                .filter(item -> item.getParentId().equals(treeEntity.getId()))
                .map(item -> {
                    item.setChildren(getChildren(item, treeEntityList));
                    return item;
                })
                .collect(Collectors.toList());
        return collect;
    }

    @Override
    public Comment save(Comment comment) {
        return commentDao.save(comment);
    }


}
