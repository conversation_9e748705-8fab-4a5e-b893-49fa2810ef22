package iet.ustb.sf.vo.domain;

import lombok.Data;
import org.hibernate.annotations.Proxy;

import javax.persistence.*;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: Dr.Monster
 * @Slogan: The never-compromising angry youth.
 * @PackageName: iet.ustb.sf.domain
 * @Date: 2022/08/11/17:19
 * @Description:
 */
@Data
@Entity
@Table(name = "Warning_Event_Body")
@Proxy(lazy = false)
public class WarningEventBody extends BaseEntity {

    //标题
    @Column
    String title;

    //报警推送的消息（推送在智能桌面及企业微信上展示的信息）
    @Column(length = 2048)
    String msg;

    //处理方案
    @Column
    String treatment;

    //具体报警信息（点开报警详情显示的信息）
    @Column(columnDefinition = "MediumText")
    String detailInfo;

    //报警页面url
    @Column
    String alertUrl;

    //推送角色ID,多个用,(英文)号隔开
    @Column(columnDefinition = "MediumText")
    String pushRoleIDs;

    //其他信息
    @Column(columnDefinition = "MediumText")
    String otherInfo;

    //备注
    @Column(columnDefinition = "MediumText")
    String remark;
}
