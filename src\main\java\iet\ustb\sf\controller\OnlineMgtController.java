package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.OnlineMgt;
import iet.ustb.sf.service.OnlineMgtService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 上线进度管理
 *
 * <AUTHOR>
 * @create 2023-02-13
 */
@RestController
@RequestMapping("/onlineMgt")
@Api(value = "上线进度管理", tags = "上线进度管理")
public class OnlineMgtController {

    @Autowired
    private OnlineMgtService onlineMgtService;

    @PostMapping("/findAll")
    @ApiOperation(value = "查询所有", notes = "查询所有")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<OnlineMgt> onlineMgtList = onlineMgtService.findAll();
            ajaxJson.setData(onlineMgtList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findPageByMultiCondition")
    @ApiOperation(value = "按多条件查找分页", notes = "入参：{\"name\": \"时空变换\",\"module\": \"1\",\"parentId\": \"0\"}")
    public AjaxJson findPageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<OnlineMgt> onlineMgtPage = onlineMgtService.findPageByMultiCondition(jsonObject);
            ajaxJson.setData(onlineMgtPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存", notes = "保存")
    public AjaxJson save(@RequestBody OnlineMgt onlineMgt) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            onlineMgtService.save(onlineMgt);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "入参：{\"id\":\"f356e461-0565-4ed4-bed1-29fb810bb160\"}")
    public AjaxJson delete(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            onlineMgtService.delete(jsonObject);
            ajaxJson.setData("删除成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

}
