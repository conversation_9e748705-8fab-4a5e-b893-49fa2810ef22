package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.OnlineMgtDao;
import iet.ustb.sf.vo.domain.DictionaryDtl;
import iet.ustb.sf.vo.domain.OnlineMgt;
import iet.ustb.sf.service.DictionaryDtlService;
import iet.ustb.sf.service.OnlineMgtService;
import iet.ustb.sf.utils.ToolsUtil;
import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 上线进度管理
 *
 * <AUTHOR>
 * @create 2023-02-13
 * @see OnlineMgtService
 */
@Service
public class OnlineMgtServiceImpl implements OnlineMgtService {
    @Autowired
    private OnlineMgtDao onlineMgtDao;
    @Autowired
    private DictionaryDtlService dictionaryDtlService;

    @Override
    public List<OnlineMgt> findAll() {
        return onlineMgtDao.findAll();
    }

    @Override
    public Page<OnlineMgt> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<OnlineMgt> onlineMgtPage = onlineMgtDao.findAll(createSpecs(jsonObject), pageable);

        for (OnlineMgt onlineMgt : onlineMgtPage) {
            if (!"0".equals(onlineMgt.getParentId())) {// 非一级菜单综合评分
                Integer comprehensiveScore = this.getComprehensiveScore(onlineMgt);
                onlineMgt.setComprehensiveScore(comprehensiveScore);
            }
        }
        for (OnlineMgt onlineMgt : onlineMgtPage) {
            if ("0".equals(onlineMgt.getParentId())) {// 一级菜单综合评分：子集菜单综合评分的平均值
                Double collect = onlineMgtDao.findByParentId(onlineMgt.getId())
                        .stream()
                        .filter(o -> o.getComprehensiveScore() != null)
                        .map(OnlineMgt::getComprehensiveScore)
                        .collect(Collectors.averagingInt(Integer::intValue));
                Integer comprehensiveScore = new BigDecimal(collect).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
                onlineMgt.setComprehensiveScore(comprehensiveScore);
            }
        }
        return onlineMgtPage;
    }

    private Specification<OnlineMgt> createSpecs(JSONObject json) {
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            String name = json.getString("name");// 名称
            Integer module = json.getInteger("module");// 模块
            String parentId = json.getString("parentId");// 父id
            if (StringUtils.isNotBlank(name)) {
                list.add(cb.like(root.get("name"), "%" + name + "%"));
            }
            if (module != null) {
                list.add(cb.equal(root.get("module"), module));
            }
            if (StringUtils.isNotBlank(parentId)) {
                list.add(cb.equal(root.get("parentId"), parentId));
            }
            query.where(list.toArray(new Predicate[list.size()]));
            query.orderBy(cb.asc(root.get("createDateTime")));
            return query.getRestriction();
        };
    }

    /**
     * 获取二级菜单的综合得分
     *
     * @param onlineMgt 进度管理
     * @return {@link Integer }
     * <AUTHOR>
     * @create 2023-02-09
     */
    private Integer getComprehensiveScore(OnlineMgt onlineMgt) {
        Integer functionDev = onlineMgt.getFunctionDev() != null ? onlineMgt.getFunctionDev() : 0;// 功能开发
        Integer InternalTest = onlineMgt.getInternalTest() != null ? onlineMgt.getInternalTest() : 0;// 内部测试
        Integer externalTrial = onlineMgt.getExternalTrial() != null ? onlineMgt.getExternalTrial(): 0;// 外部试用
        Integer functionTrain = onlineMgt.getFunctionTrain() != null ? onlineMgt.getFunctionTrain() : 0;// 功能培训
        Integer functionOnline = onlineMgt.getFunctionOnline() != null ? onlineMgt.getFunctionOnline() : 0;// 功能上线

        List<DictionaryDtl> dictDtlList = dictionaryDtlService.findByDictCode("onlineScoreRate");
        double comprehensiveScore = 0;
        for (DictionaryDtl dictDtl : dictDtlList) {
            String code = dictDtl.getCode();
            String value = dictDtl.getValue();

            boolean numeric = ToolsUtil.isNumeric(value);
            if (!numeric) continue;

            // 公式计算综合评分
            /*double comprehensiveScore = functionDev * 0.2 + internalTest * 0.2 + externalTrial * 0.1
                    + functionTrain * 0.2 + functionOnline * 0.3;*/
            if ("functionDev".equals(code)) {
                comprehensiveScore += functionDev * Double.parseDouble(value);
            }
            if ("internalTest".equals(code)) {
                comprehensiveScore += InternalTest * Double.parseDouble(value);
            }
            if ("externalTrial".equals(code)) {
                comprehensiveScore += externalTrial * Double.parseDouble(value);
            }
            if ("functionTrain".equals(code)) {
                comprehensiveScore += functionTrain * Double.parseDouble(value);
            }
            if ("functionOnline".equals(code)) {
                comprehensiveScore += functionOnline * Double.parseDouble(value);
            }
        }
        // double转BigDecimal 四舍五入
        Integer cs = new BigDecimal(comprehensiveScore).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
        return cs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OnlineMgt save(OnlineMgt onlineMgt) {
        return onlineMgtDao.save(onlineMgt);
    }

    @Override
    public void delete(JSONObject jsonObject) throws Exception {
        String ids = jsonObject.getString("id");
        // 删除该问题反馈单
        onlineMgtDao.deleteById(ids);
    }

}
