package iet.ustb.sf.config;

import iet.ustb.sf.dao.DesktopDao;
import iet.ustb.sf.vo.domain.Desktop;
import iet.ustb.sf.utils.ToolsUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Configuration
public class DesktopConfig {

    @Autowired
    private DesktopDao desktopDao;

    @Bean(name = "defaultDesktop")
    public Desktop defaultDesktop() {
        Desktop mainDeskTop = desktopDao.findDesktopByName("主页面");
        if (ToolsUtil.isEmpty(mainDeskTop)) {
            mainDeskTop = new Desktop();
            mainDeskTop.setId(UUID.randomUUID().toString());
            mainDeskTop.setType("1");
            mainDeskTop.setName("主页面");
            mainDeskTop.setDpCheck(1);
            mainDeskTop.setDeleteTag(0);
            desktopDao.save(mainDeskTop);
        }
        return mainDeskTop;
    }

    @Bean(name = "defaultDesktopList")
    public List<Desktop> defaultDesktopList() {
        List<Desktop> desktopList = new ArrayList<>();

        Desktop mainDeskTop = desktopDao.findDesktopByName("主页面");
        if (ToolsUtil.isEmpty(mainDeskTop)) {
            mainDeskTop = new Desktop();
            mainDeskTop.setId(UUID.randomUUID().toString());
            mainDeskTop.setType("1");
            mainDeskTop.setName("主页面");
            mainDeskTop.setDpCheck(1);
            mainDeskTop.setDeleteTag(0);
            desktopDao.save(mainDeskTop);
        }
        desktopList.add(mainDeskTop);

        Desktop meterDeskTop = desktopDao.findDesktopByName("仪表板");
        if (ToolsUtil.isEmpty(meterDeskTop)) {
            meterDeskTop = new Desktop();
            meterDeskTop.setId(UUID.randomUUID().toString());
            meterDeskTop.setType("1");
            meterDeskTop.setName("仪表板");
            meterDeskTop.setDpCheck(0);
            meterDeskTop.setDeleteTag(0);
            desktopDao.save(meterDeskTop);
        }
        desktopList.add(meterDeskTop);

        Desktop workDeskTop = desktopDao.findDesktopByName("工作台");
        if (ToolsUtil.isEmpty(workDeskTop)) {
            workDeskTop = new Desktop();
            workDeskTop.setId(UUID.randomUUID().toString());
            workDeskTop.setType("1");
            workDeskTop.setName("工作台");
            workDeskTop.setDpCheck(2);
            workDeskTop.setDeleteTag(0);
            desktopDao.save(workDeskTop);
        }
        desktopList.add(workDeskTop);

        return desktopList;
    }
}

