package iet.ustb.sf.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.RoleDao;
import iet.ustb.sf.dao.WarningEventDao;
import iet.ustb.sf.dao.WarningInfoDao;
import iet.ustb.sf.dao.WarningRuleDao;
import iet.ustb.sf.vo.domain.Role;
import iet.ustb.sf.vo.domain.WarningInfo;
import iet.ustb.sf.vo.eo.DoneRateEO;
import iet.ustb.sf.vo.eo.WarningInfoEO;
import iet.ustb.sf.service.ClassQueryService;
import iet.ustb.sf.service.WarningInfoService;
import iet.ustb.sf.service.WarningRuleService;
import iet.ustb.sf.utils.DateUtils;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.vo.DoneRateVo;
import iet.ustb.sf.vo.PageVo;
import iet.ustb.sf.vo.WarningInfoVo;
import lombok.Cleanup;
import org.apache.poi.ss.usermodel.Workbook;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.*;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Dr.Monster
 * @Title: WaringInfoServiceImpl
 * @Date: 23/10/31 11:4549
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */

@Service
public class WarningInfoServiceImpl implements WarningInfoService {


    @Autowired
    WarningInfoDao warningInfoDao;
    @Autowired
    WarningEventDao warningEventDao;
    @Autowired
    RoleDao roleDao;
    @Autowired
    WarningRuleDao warningRuleDao;
    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    WarningRuleService warningRuleService;
    @Autowired
    ClassQueryService classQueryService;

    @Override
    public Page<WarningInfo> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);
        Page<WarningInfo> WarningInfoPage = warningInfoDao.findAll(new Specification<WarningInfo>() {
            @Override
            public Predicate toPredicate(Root<WarningInfo> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<Predicate>();
                Predicate[] arr = new Predicate[list.size()];

                String moduleCode = jsonObject.getString("moduleCode");
                String alertContent = jsonObject.getString("alertContent");
                String deviceName = jsonObject.getString("deviceName");
                String areaName = jsonObject.getString("areaName");

                Integer warningType = jsonObject.getInteger("warningType");
                Integer alertLevel = jsonObject.getInteger("alertLevel");
                Integer isConfirm = jsonObject.getInteger("isConfirm");
                Integer status = jsonObject.getInteger("status");
                Integer isFalse = jsonObject.getInteger("isFalse");


                String startTime = jsonObject.getString("startTime");
                String endTime = jsonObject.getString("endTime");

                if (!ToolsUtil.isEmpty(moduleCode)) {
                    list.add(cb.equal(root.get("moduleCode"), moduleCode));
                }

                if (!ToolsUtil.isEmpty(alertContent)) {
                    list.add(cb.like(root.get("alertContent"), "%" + alertContent + "%"));
                }

                if (!ToolsUtil.isEmpty(deviceName)) {
                    list.add(cb.like(root.get("deviceName"), "%" + deviceName + "%"));
                }

                if (!ToolsUtil.isEmpty(areaName)) {
                    list.add(cb.equal(root.get("areaName"), areaName));
                }

                if (!ToolsUtil.isEmpty(warningType)) {
                    list.add(cb.equal(root.get("warningType"), warningType));
                }

                if (!ToolsUtil.isEmpty(alertLevel)) {
                    list.add(cb.equal(root.get("alertLevel"), alertLevel));
                }

                if (!ToolsUtil.isEmpty(isConfirm)) {
                    list.add(cb.equal(root.get("isConfirm"), isConfirm));
                }

                if (!ToolsUtil.isEmpty(status)) {
                    list.add(cb.equal(root.get("status"), status));
                }

                if (!ToolsUtil.isEmpty(isFalse)) {
                    list.add(cb.equal(root.get("isFalse"), isFalse));
                }

                if (!ToolsUtil.isEmpty(jsonObject.get("roleIDs"))) {
                    List<String> roleIDs = (List<String>) jsonObject.get("roleIDs");
                    if (!ToolsUtil.isEmpty(roleIDs)) {
                        Path<String> path = root.get("roleID");
                        CriteriaBuilder.In<Object> in = cb.in(path);
                        for (int i = 0; i < roleIDs.size(); i++) {
                            in.value(roleIDs.get(i));//存入值
                        }
                        list.add(cb.and(cb.and(in)));
                    }
                }

                if (!ToolsUtil.isEmpty(startTime)) {
                    list.add(cb.greaterThanOrEqualTo(root.get("createDateTime").as(String.class), startTime + ":00"));
                }
                if (!ToolsUtil.isEmpty(endTime)) {
                    list.add(cb.lessThanOrEqualTo(root.get("createDateTime").as(String.class), endTime + ":59"));
                }

                cq.where(list.toArray(arr));
                cq.orderBy(cb.desc(root.get("createDateTime")));
                return null;
            }
        }, pageable);
        return WarningInfoPage;
    }

    @Override
    public Page<WarningInfo> findFalsePageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);
        Page<WarningInfo> WarningInfoPage = warningInfoDao.findAll(new Specification<WarningInfo>() {
            @Override
            public Predicate toPredicate(Root<WarningInfo> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<Predicate>();
                Predicate[] arr = new Predicate[list.size()];

                String moduleCode = jsonObject.getString("moduleCode");
                String alertContent = jsonObject.getString("alertContent");
                String deviceName = jsonObject.getString("deviceName");
                String areaName = jsonObject.getString("areaName");

                Integer warningType = jsonObject.getInteger("warningType");
                Integer alertLevel = jsonObject.getInteger("alertLevel");
                Integer isConfirm = jsonObject.getInteger("isConfirm");
                Integer status = jsonObject.getInteger("status");
                Integer isFalse = jsonObject.getInteger("isFalse");


                String startTime = jsonObject.getString("startTime");
                String endTime = jsonObject.getString("endTime");

                if (!ToolsUtil.isEmpty(moduleCode)) {
                    list.add(cb.equal(root.get("moduleCode"), moduleCode));
                }

                if (!ToolsUtil.isEmpty(alertContent)) {
                    list.add(cb.like(root.get("alertContent"), "%" + alertContent + "%"));
                }

                if (!ToolsUtil.isEmpty(deviceName)) {
                    list.add(cb.like(root.get("deviceName"), "%" + deviceName + "%"));
                }

                if (!ToolsUtil.isEmpty(areaName)) {
                    list.add(cb.equal(root.get("areaName"), areaName));
                }

                if (!ToolsUtil.isEmpty(warningType)) {
                    list.add(cb.equal(root.get("warningType"), warningType));
                }

                if (!ToolsUtil.isEmpty(alertLevel)) {
                    list.add(cb.equal(root.get("alertLevel"), alertLevel));
                }

                if (!ToolsUtil.isEmpty(isConfirm)) {
                    list.add(cb.equal(root.get("isConfirm"), isConfirm));
                }

                if (!ToolsUtil.isEmpty(status)) {
                    list.add(cb.equal(root.get("status"), status));
                }

                list.add(
                        cb.or(
                                cb.equal(root.get("isFalse") , 1)
                        ,
                                cb.and(
                                        cb.equal(root.get("isFalse") , 0) ,
                                        cb.isNotNull(root.get("falseFeedBack"))
                                )
                        )
                );


                if (!ToolsUtil.isEmpty(jsonObject.get("roleIDs"))) {
                    List<String> roleIDs = (List<String>) jsonObject.get("roleIDs");
                    if (!ToolsUtil.isEmpty(roleIDs)) {
                        Path<String> path = root.get("roleID");
                        CriteriaBuilder.In<Object> in = cb.in(path);
                        for (int i = 0; i < roleIDs.size(); i++) {
                            in.value(roleIDs.get(i));//存入值
                        }
                        list.add(cb.and(cb.and(in)));
                    }
                }

                if (!ToolsUtil.isEmpty(startTime)) {
                    list.add(cb.greaterThanOrEqualTo(root.get("createDateTime").as(String.class), startTime + ":00"));
                }
                if (!ToolsUtil.isEmpty(endTime)) {
                    list.add(cb.lessThanOrEqualTo(root.get("createDateTime").as(String.class), endTime + ":59"));
                }

                cq.where(list.toArray(arr));
                cq.orderBy(cb.desc(root.get("createDateTime")));
                return null;
            }
        }, pageable);
        return WarningInfoPage;
    }

    @Override
    public PageVo<WarningInfoVo> findPageByMultiConditionNew(JSONObject jsonObject) {
        String moduleCode = jsonObject.getString("moduleCode");
        String alertContent = jsonObject.getString("alertContent");
        String deviceName = jsonObject.getString("deviceName");
        String areaName = jsonObject.getString("areaName");

        Integer warningType = jsonObject.getInteger("warningType");
        Integer alertLevel = jsonObject.getInteger("alertLevel");
        Integer isConfirm = jsonObject.getInteger("isConfirm");
        Integer status = jsonObject.getInteger("status");

        String roleID = jsonObject.getString("roleID");


        String startTime = jsonObject.getString("startTime");
        String endTime = jsonObject.getString("endTime");

        Session session = entityManager.unwrap(Session.class);

        PageVo<WarningInfoVo> pageVo = new PageVo();

        int pageSize = ToolsUtil.isEmpty(jsonObject.get("pageSize")) ? 10 : (int) jsonObject.get("pageSize");
        int pageIndex = ToolsUtil.isEmpty(jsonObject.get("pageIndex")) ? 1 : (int) jsonObject.get("pageIndex");


        StringBuilder selectSql = new StringBuilder("select wi.*\n" +
                "from warning_info wi\n" +
                "         join du_user du on wi.createUserNo = du.userno\n" +
                "         join ds_role dr\n" +
                "         join ds_role_userlist dru on dr.id = dru.role_id and dru.userlist_id = du.id\n" +
                "where 1 = 1");
        StringBuilder countSql = new StringBuilder("select count(wi.id) as total\n" +
                "from warning_info wi\n" +
                "         join du_user du on wi.createUserNo = du.userno\n" +
                "         join ds_role dr\n" +
                "         join ds_role_userlist dru on dr.id = dru.role_id and dru.userlist_id = du.id\n" +
                "where 1 = 1");


        if (!ToolsUtil.isEmpty(jsonObject.get("moduleCode"))) {
            selectSql.append(" and wi.moduleCode = '" + jsonObject.get("moduleCode") + "' ");
            countSql.append(" and wi.moduleCode = '" + jsonObject.get("moduleCode") + "' ");
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("alertContent"))) {
            selectSql.append(" and wi.alertContent = '" + jsonObject.get("alertContent") + "' ");
            countSql.append(" and wi.alertContent = '" + jsonObject.get("alertContent") + "' ");
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("deviceName"))) {
            selectSql.append(" and wi.deviceName = '" + jsonObject.get("deviceName") + "' ");
            countSql.append(" and wi.deviceName = '" + jsonObject.get("deviceName") + "' ");
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("areaName"))) {
            selectSql.append(" and wi.areaName = '" + jsonObject.get("areaName") + "' ");
            countSql.append(" and wi.areaName = '" + jsonObject.get("areaName") + "' ");
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("warningType"))) {
            selectSql.append(" and wi.warningType = " + jsonObject.get("warningType") + " ");
            countSql.append(" and wi.warningType = " + jsonObject.get("warningType") + " ");
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("alertLevel"))) {
            selectSql.append(" and wi.alertLevel = " + jsonObject.get("alertLevel") + " ");
            countSql.append(" and wi.alertLevel = " + jsonObject.get("alertLevel") + " ");
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("isConfirm"))) {
            selectSql.append(" and wi.isConfirm = " + jsonObject.get("isConfirm") + " ");
            countSql.append(" and wi.isConfirm = " + jsonObject.get("isConfirm") + " ");
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("roleID"))) {
            selectSql.append(" and dru.role_id = '" + jsonObject.get("roleID") + "' ");
            countSql.append(" and dru.role_id = '" + jsonObject.get("roleID") + "' ");
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("startTime")) && !ToolsUtil.isEmpty(jsonObject.get("endTime"))) {
            selectSql.append(" and wi.createDateTime between '" + jsonObject.getString("startTime") + ":00' and '" + jsonObject.getString("endTime") + ":59' ");
            countSql.append(" and wi.createDateTime between '" + jsonObject.getString("startTime") + ":00' and '" + jsonObject.getString("endTime") + ":59' ");
        }

        selectSql.append(" order by wi.createDateTime desc ");

        selectSql.append(" limit " + ((pageIndex - 1) * pageSize + 1) + "," + pageIndex * pageSize);

        SQLQuery dataSqlQuery = session.createSQLQuery(selectSql.toString());
        dataSqlQuery.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        SQLQuery countSqlQuery = session.createSQLQuery(countSql.toString());

        List<WarningInfoVo> warningInfoVoList = JSONArray.parseArray(JSON.toJSONString(dataSqlQuery.list()), WarningInfoVo.class);
        int count = ((BigInteger) countSqlQuery.getSingleResult()).intValue();


        Map<String, Integer> map = new HashMap<>();
        map.put("pageIndex", pageIndex);
        map.put("pageSize", pageSize);
        pageVo.setPageable(map);
        pageVo.setContent(warningInfoVoList);
        pageVo.setTotalElements(count);

        BigDecimal v1 = new BigDecimal(pageSize);
        BigDecimal v2 = new BigDecimal(count);

        pageVo.setTotalPages(v2.divide(v1, 0, RoundingMode.CEILING).intValue());

        return pageVo;
    }

    @Override
    public void exportInfos(JSONObject jsonObject, HttpServletResponse response) {
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("报警清单");
        // 设置sheet表头名称
        exportParams.setSheetName("报警清单");
        jsonObject.put("pageIndex", 1);
        jsonObject.put("pageSize", Integer.MAX_VALUE);

        Page<WarningInfo> warningInfoPage = findPageByMultiCondition(jsonObject);
        List<WarningInfo> warningInfoList = warningInfoPage.get().collect(Collectors.toList());
        List<WarningInfoEO> warningRuleEOList = JSONArray.parseArray(JSON.toJSONString(warningInfoList), WarningInfoEO.class);

        warningRuleEOList = warningRuleEOList.stream().map(x -> {
            if (0 == x.getStatus()) {
                x.setStatusName("未处理");
            } else {
                x.setStatusName("已处理");
            }

            if (!ToolsUtil.isEmpty(x.getIsFalse())) {
                if (0 == x.getIsFalse()) {
                    x.setIsFalseName("否");
                } else {
                    x.setIsFalseName("是");
                }
            }
            if(!ToolsUtil.isEmpty(x.getDealTime()) && !ToolsUtil.isEmpty(x.getCreateDateTime())){
                x.setDiffTime(DateUtils.calTimeDiffString(x.getDealTime() , x.getCreateDateTime()));
            }
            return x;
        }).collect(Collectors.toList());

        try {
            @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(exportParams, WarningInfoEO.class, warningRuleEOList);
            ToolsUtil.downLoadExcel("报警清单", response, workBook);
        } catch (Exception e) {

        }
    }

    @Override
    public void exportFalseInfos(JSONObject jsonObject, HttpServletResponse response) {
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("报警清单");
        // 设置sheet表头名称
        exportParams.setSheetName("报警清单");
        jsonObject.put("pageIndex", 1);
        jsonObject.put("pageSize", Integer.MAX_VALUE);

        Page<WarningInfo> warningInfoPage = findFalsePageByMultiCondition(jsonObject);
        List<WarningInfo> warningInfoList = warningInfoPage.get().collect(Collectors.toList());
        List<WarningInfoEO> warningRuleEOList = JSONArray.parseArray(JSON.toJSONString(warningInfoList), WarningInfoEO.class);

        warningRuleEOList = warningRuleEOList.stream().map(x -> {
            if (0 == x.getStatus()) {
                x.setStatusName("未处理");
            } else {
                x.setStatusName("已处理");
            }

            if (!ToolsUtil.isEmpty(x.getIsFalse())) {
                if (0 == x.getIsFalse()) {
                    x.setIsFalseName("否");
                } else {
                    x.setIsFalseName("是");
                }
            }
            return x;
        }).collect(Collectors.toList());

        try {
            @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(exportParams, WarningInfoEO.class, warningRuleEOList);
            ToolsUtil.downLoadExcel("报误清单", response, workBook);
        } catch (Exception e) {

        }
    }

    @Override
    public void batchSaveInfos(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject) || ToolsUtil.isEmpty(jsonObject.get("list"))) {
        } else {
            List<WarningInfo> infoList = ToolsUtil.jsonObjectToEntityList(jsonObject.get("list"), WarningInfo.class);

            for(WarningInfo info : infoList){
                String warningRuleID = info.getWarningRuleID();
                Map<String , String> result = classQueryService.queryClasses(info.getCreateDateTime());


                info.setClasses(result.get("classes"));
                info.setTeam(result.get("team"));

                if(0 == warningRuleService.checkRuleExist(warningRuleID)){
                    continue;
                }

                if(0 == warningRuleService.checkPushMode(warningRuleID)){
                    continue;
                }
                warningInfoDao.save(info);
            }
        }
    }

    @Override
    public void saveFalseInfo(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject) || ToolsUtil.isEmpty(jsonObject.get("list"))) {
        } else {
            List<WarningInfo> infoList = ToolsUtil.jsonObjectToEntityList(jsonObject.get("list"), WarningInfo.class);
            for(WarningInfo warningInfo : infoList){
                if(1 == warningInfo.getIsConfirmFalse()){
                    warningInfo.setAlertAdvice(warningInfo.getFalseFeedBack());
                }
            }
            warningInfoDao.saveAllAndFlush(infoList);
        }
    }

    @Override
    public List<Map<String, String>> findModuleInfoList() {
        String sql = "select distinct (moduleCode) as ID , moduleName as Name from warning_info";
        return findIDNameMap(sql);
    }

    @Override
    public List<Map<String, String>> findAreaNameList() {
        String sql = "select distinct (areaID) as ID , areaName as Name from warning_info";
        return findIDNameMap(sql);
    }

    @Override
    public List<DoneRateVo> getDoneRateByRoleIDs(JSONObject jsonObject) {

        List<String> roleIDs = new ArrayList<>();
        if (ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && !ToolsUtil.isEmpty(jsonObject.get("productionLineName")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            List<String> productionLineNameList = ToolsUtil.jsonObjectToEntityList(jsonObject.get("productionLineName"), String.class);
            for (String plName : productionLineNameList) {
                roleIDs.addAll(roleDao.findRolesByProductionLineName(plName).stream().map(x -> x.getId()).collect(Collectors.toList()));
            }
            roleIDs = roleIDs.stream().distinct().collect(Collectors.toList());
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            roleIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("roleIDs"), String.class);
        }

        if (ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && ToolsUtil.isEmpty(jsonObject.get("productionLineName")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            List<String> productionLineNameList = Arrays.asList("第一炼钢厂", "宽厚板厂");
            for (String plName : productionLineNameList) {
                roleIDs.addAll(roleDao.findRolesByProductionLineName(plName).stream().map(x -> x.getId()).collect(Collectors.toList()));
            }
            roleIDs = roleIDs.stream().distinct().collect(Collectors.toList());
        }

        if (!ToolsUtil.isEmpty(roleIDs)) {
            List<DoneRateVo> result = new ArrayList<>();

            Map<String, Object> tempResult;
            try {
                Date startTime = DateUtils.parseDate(jsonObject.getString("startTime") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
                Date endTime = DateUtils.parseDate(jsonObject.getString("endTime") + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
                Role role;
                JSONObject jo;
                DoneRateVo vo;

                for (String roleID : roleIDs) {

                    vo = new DoneRateVo();
                    role = roleDao.getById(roleID);
//                    tempResult = warningEventDao.getUndoneRateByRoleID(roleID , startTime , endTime);
//                    tempResult = warningEventDao.getUndoneRateByRoleIDNew(roleID , startTime , endTime);
                    tempResult = warningInfoDao.getUndoneRateByRoleID(roleID, startTime, endTime);

                    if (ToolsUtil.isEmpty(tempResult.get("DONE"))) {
                        vo.setDone(0);
                    } else {
                        vo.setDone(((BigDecimal) tempResult.get("DONE")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("UNDONE"))) {
                        vo.setUnDone(0);
                    } else {
                        vo.setUnDone(((BigDecimal) tempResult.get("UNDONE")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("TOTAL"))) {
                        vo.setTotal(0);
                    } else {
                        vo.setTotal(((BigInteger) tempResult.get("TOTAL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("FIRSTLEVEL"))) {
                        vo.setFirstLevel(0);
                    } else {
                        vo.setFirstLevel(((BigDecimal) tempResult.get("FIRSTLEVEL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("SECONDLEVEL"))) {
                        vo.setSecondLevel(0);
                    } else {
                        vo.setSecondLevel(((BigDecimal) tempResult.get("SECONDLEVEL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("THIRDLEVEL"))) {
                        vo.setThirdLevel(0);
                    } else {
                        vo.setThirdLevel(((BigDecimal) tempResult.get("THIRDLEVEL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("ISFALSE"))) {
                        vo.setIsFalse(0);
                    } else {
//                        vo.setIsFalse(((BigInteger)tempResult.get("ISFALSE")).intValue());
                        vo.setIsFalse(((BigDecimal) tempResult.get("ISFALSE")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("ADVICEATYPIA"))) {
                        vo.setAdviceAtypia(0);
                    } else {
                        vo.setAdviceAtypia(((BigDecimal) tempResult.get("ADVICEATYPIA")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("ANALYSISATYPIA"))) {
                        vo.setAnalysisAtypia(0);
                    } else {
                        vo.setAnalysisAtypia(((BigDecimal) tempResult.get("ANALYSISATYPIA")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("twoHourDone".toUpperCase()))) {
                        vo.setTwoHourDone(0);
                    } else {
                        vo.setTwoHourDone(((BigDecimal) tempResult.get("twoHourDone".toUpperCase())).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("fourHourDone".toUpperCase()))) {
                        vo.setFourHourDone(0);
                    } else {
                        vo.setFourHourDone(((BigDecimal) tempResult.get("fourHourDone".toUpperCase())).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("eightHourDone".toUpperCase()))) {
                        vo.setEightHourDone(0);
                    } else {
                        vo.setEightHourDone(((BigDecimal) tempResult.get("eightHourDone".toUpperCase())).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("gtEightHourDone".toUpperCase()))) {
                        vo.setGtEightHourDone(0);
                    } else {
                        vo.setGtEightHourDone(((BigDecimal) tempResult.get("gtEightHourDone".toUpperCase())).intValue());
                    }


                    vo.setRoleName(role.getRoleName());
                    vo.setRoleID(role.getId());
                    if (vo.getTotal() != 0 && vo.getDone() != 0) {
                        BigDecimal dv1 = new BigDecimal(vo.getDone());
                        BigDecimal dv2 = new BigDecimal(vo.getTotal());
                        double rate = dv1.divide(dv2, 2, RoundingMode.HALF_UP).doubleValue() * 100;
                        vo.setRate(String.format("%.2f", rate));
                    } else if (vo.getTotal() == 0 && vo.getDone() != 0) {
                        vo.setRate("0");
                    } else if (vo.getTotal() == 0 && vo.getDone() == 0) {
                        vo.setRate("100");
                    } else {
                        vo.setRate("0");
                    }
                    result.add(vo);
                }
                return result.stream().sorted(Comparator.comparing(DoneRateVo::getRate).reversed()).collect(Collectors.toList());
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }


    @Override
    public List<DoneRateVo> getDoneRateGroupRoles(JSONObject jsonObject) {

        List<String> roleIDs = new ArrayList<>();
        if (ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && !ToolsUtil.isEmpty(jsonObject.get("productionLineName")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            List<String> productionLineNameList = ToolsUtil.jsonObjectToEntityList(jsonObject.get("productionLineName"), String.class);
            for (String plName : productionLineNameList) {
                roleIDs.addAll(roleDao.findRolesByProductionLineName(plName).stream().map(x -> x.getId()).collect(Collectors.toList()));
            }
            roleIDs = roleIDs.stream().distinct().collect(Collectors.toList());
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            roleIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("roleIDs"), String.class);
        }

        if (ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && ToolsUtil.isEmpty(jsonObject.get("productionLineName")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            List<String> productionLineNameList = Arrays.asList("第一炼钢厂", "宽厚板厂");
            for (String plName : productionLineNameList) {
                roleIDs.addAll(roleDao.findRolesByProductionLineName(plName).stream().map(x -> x.getId()).collect(Collectors.toList()));
            }
            roleIDs = roleIDs.stream().distinct().collect(Collectors.toList());
        }

        if (!ToolsUtil.isEmpty(roleIDs)) {
            Set<DoneRateVo> result = new HashSet<>();

            Map<String, Object> tempResult;
            try {
                Date startTime = DateUtils.parseDate(jsonObject.getString("startTime") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
                Date endTime = DateUtils.parseDate(jsonObject.getString("endTime") + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
                Role role;
                JSONObject jo;
                DoneRateVo vo;

                Map<String , DoneRateVo> tmpMap = new HashMap<>();

                DoneRateVo zlVo = new DoneRateVo();
                zlVo.setRoleName("第一炼钢厂转炉岗位");
                tmpMap.put(zlVo.getRoleName() , zlVo);

                DoneRateVo lfVo = new DoneRateVo();
                lfVo.setRoleName("第一炼钢厂LF岗位");
                tmpMap.put(lfVo.getRoleName() , lfVo);

                DoneRateVo rhVo = new DoneRateVo();
                rhVo.setRoleName("第一炼钢厂RH岗位");
                tmpMap.put(rhVo.getRoleName() , rhVo);

                DoneRateVo lzVo = new DoneRateVo();
                lzVo.setRoleName("第一炼钢厂连铸岗位");
                tmpMap.put(lzVo.getRoleName() , lzVo);

                DoneRateVo jzVo = new DoneRateVo();
                jzVo.setRoleName("宽厚板厂精整岗位");
                tmpMap.put(jzVo.getRoleName() , jzVo);



                for (String roleID : roleIDs) {

                    vo = new DoneRateVo();
                    role = roleDao.getById(roleID);
                    tempResult = warningInfoDao.getUndoneRateByRoleID(roleID, startTime, endTime);


                    if (ToolsUtil.isEmpty(tempResult.get("DONE"))) {
                        vo.setDone(0);
                    } else {
                        vo.setDone(((BigDecimal) tempResult.get("DONE")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("UNDONE"))) {
                        vo.setUnDone(0);
                    } else {
                        vo.setUnDone(((BigDecimal) tempResult.get("UNDONE")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("TOTAL"))) {
                        vo.setTotal(0);
                    } else {
                        vo.setTotal(((BigInteger) tempResult.get("TOTAL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("FIRSTLEVEL"))) {
                        vo.setFirstLevel(0);
                    } else {
                        vo.setFirstLevel(((BigDecimal) tempResult.get("FIRSTLEVEL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("SECONDLEVEL"))) {
                        vo.setSecondLevel(0);
                    } else {
                        vo.setSecondLevel(((BigDecimal) tempResult.get("SECONDLEVEL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("THIRDLEVEL"))) {
                        vo.setThirdLevel(0);
                    } else {
                        vo.setThirdLevel(((BigDecimal) tempResult.get("THIRDLEVEL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("ISFALSE"))) {
                        vo.setIsFalse(0);
                    } else {
//                        vo.setIsFalse(((BigInteger)tempResult.get("ISFALSE")).intValue());
                        vo.setIsFalse(((BigDecimal) tempResult.get("ISFALSE")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("ADVICEATYPIA"))) {
                        vo.setAdviceAtypia(0);
                    } else {
                        vo.setAdviceAtypia(((BigDecimal) tempResult.get("ADVICEATYPIA")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("ANALYSISATYPIA"))) {
                        vo.setAnalysisAtypia(0);
                    } else {
                        vo.setAnalysisAtypia(((BigDecimal) tempResult.get("ANALYSISATYPIA")).intValue());
                    }

                    vo.setRoleName(role.getRoleName());

                    if("第一炼钢厂1#LF岗位".equals(vo.getRoleName()) ||
                            "第一炼钢厂2#LF岗位".equals(vo.getRoleName()) ||
                            "第一炼钢厂3#LF岗位".equals(vo.getRoleName()) ||
                            "第一炼钢厂4#LF岗位".equals(vo.getRoleName())){

                        int done = lfVo.getDone();
                        done += vo.getDone();
                        lfVo.setDone(done);

                        int undone = lfVo.getUnDone();
                        undone += vo.getUnDone();
                        lfVo.setUnDone(undone);

                        int total = lfVo.getTotal();
                        total += vo.getTotal();
                        lfVo.setTotal(total);

                        int isFalse = lfVo.getIsFalse();
                        isFalse += vo.getIsFalse();
                        lfVo.setIsFalse(isFalse);

                        int firstLevel = lfVo.getFirstLevel();
                        firstLevel += vo.getFirstLevel();
                        lfVo.setFirstLevel(firstLevel);

                        int secondLevel = lfVo.getSecondLevel();
                        secondLevel += vo.getSecondLevel();
                        lfVo.setSecondLevel(firstLevel);

                        int thirdLevel = lfVo.getThirdLevel();
                        thirdLevel += vo.getThirdLevel();
                        lfVo.setThirdLevel(thirdLevel);

                        int adviceAtypia = lfVo.getAdviceAtypia();
                        adviceAtypia += vo.getAdviceAtypia();
                        lfVo.setAdviceAtypia(adviceAtypia);

                        int analysisAtypia = lfVo.getAnalysisAtypia();
                        analysisAtypia += vo.getAnalysisAtypia();
                        lfVo.setAnalysisAtypia(analysisAtypia);

                        result.add(lfVo);
                        continue;
                    }


                    if("第一炼钢厂0#连铸岗位".equals(vo.getRoleName()) ||
                            "第一炼钢厂1#连铸岗位".equals(vo.getRoleName()) ||
                            "第一炼钢厂2#连铸岗位".equals(vo.getRoleName()) ||
                            "第一炼钢厂3#连铸岗位".equals(vo.getRoleName())){


                        int done = lzVo.getDone();
                        done += vo.getDone();
                        lzVo.setDone(done);

                        int undone = lzVo.getUnDone();
                        undone += vo.getUnDone();
                        lzVo.setUnDone(undone);

                        int total = lzVo.getTotal();
                        total += vo.getTotal();
                        lzVo.setTotal(total);

                        int isFalse = lzVo.getIsFalse();
                        isFalse += vo.getIsFalse();
                        lzVo.setIsFalse(isFalse);

                        int firstLevel = lzVo.getFirstLevel();
                        firstLevel += vo.getFirstLevel();
                        lzVo.setFirstLevel(firstLevel);

                        int secondLevel = lzVo.getSecondLevel();
                        secondLevel += vo.getSecondLevel();
                        lzVo.setSecondLevel(firstLevel);

                        int thirdLevel = lzVo.getThirdLevel();
                        thirdLevel += vo.getThirdLevel();
                        lzVo.setThirdLevel(thirdLevel);

                        int adviceAtypia = lzVo.getAdviceAtypia();
                        adviceAtypia += vo.getAdviceAtypia();
                        lzVo.setAdviceAtypia(adviceAtypia);

                        int analysisAtypia = lzVo.getAnalysisAtypia();
                        analysisAtypia += vo.getAnalysisAtypia();
                        lzVo.setAnalysisAtypia(analysisAtypia);

                        result.add(lzVo);
                        continue;

                    }

                    if("第一炼钢厂1#转炉岗位".equals(vo.getRoleName()) ||
                            "第一炼钢厂2#转炉岗位".equals(vo.getRoleName()) ||
                            "第一炼钢厂3#转炉岗位".equals(vo.getRoleName())){

                        int done = zlVo.getDone();
                        done += vo.getDone();
                        zlVo.setDone(done);

                        int undone = zlVo.getUnDone();
                        undone += vo.getUnDone();
                        zlVo.setUnDone(undone);

                        int total = zlVo.getTotal();
                        total += vo.getTotal();
                        zlVo.setTotal(total);

                        int isFalse = zlVo.getIsFalse();
                        isFalse += vo.getIsFalse();
                        zlVo.setIsFalse(isFalse);

                        int firstLevel = zlVo.getFirstLevel();
                        firstLevel += vo.getFirstLevel();
                        zlVo.setFirstLevel(firstLevel);

                        int secondLevel = zlVo.getSecondLevel();
                        secondLevel += vo.getSecondLevel();
                        zlVo.setSecondLevel(firstLevel);

                        int thirdLevel = zlVo.getThirdLevel();
                        thirdLevel += vo.getThirdLevel();
                        zlVo.setThirdLevel(thirdLevel);

                        int adviceAtypia = zlVo.getAdviceAtypia();
                        adviceAtypia += vo.getAdviceAtypia();
                        zlVo.setAdviceAtypia(adviceAtypia);

                        int analysisAtypia = zlVo.getAnalysisAtypia();
                        analysisAtypia += vo.getAnalysisAtypia();
                        zlVo.setAnalysisAtypia(analysisAtypia);

                        result.add(zlVo);
                        continue;
                    }

                    if("第一炼钢厂1#RH岗位".equals(vo.getRoleName()) ||
                            "第一炼钢厂2#RH岗位".equals(vo.getRoleName()) ||
                            "第一炼钢厂3#RH岗位".equals(vo.getRoleName())){

                        int done = rhVo.getDone();
                        done += vo.getDone();
                        rhVo.setDone(done);

                        int undone = rhVo.getUnDone();
                        undone += vo.getUnDone();
                        rhVo.setUnDone(undone);

                        int total = rhVo.getTotal();
                        total += vo.getTotal();
                        rhVo.setTotal(total);

                        int isFalse = rhVo.getIsFalse();
                        isFalse += vo.getIsFalse();
                        rhVo.setIsFalse(isFalse);

                        int firstLevel = rhVo.getFirstLevel();
                        firstLevel += vo.getFirstLevel();
                        rhVo.setFirstLevel(firstLevel);

                        int secondLevel = rhVo.getSecondLevel();
                        secondLevel += vo.getSecondLevel();
                        rhVo.setSecondLevel(firstLevel);

                        int thirdLevel = rhVo.getThirdLevel();
                        thirdLevel += vo.getThirdLevel();
                        rhVo.setThirdLevel(thirdLevel);

                        int adviceAtypia = rhVo.getAdviceAtypia();
                        adviceAtypia += vo.getAdviceAtypia();
                        rhVo.setAdviceAtypia(adviceAtypia);

                        int analysisAtypia = rhVo.getAnalysisAtypia();
                        analysisAtypia += vo.getAnalysisAtypia();
                        rhVo.setAnalysisAtypia(analysisAtypia);

                        result.add(rhVo);
                        continue;
                    }


                    if("宽厚板厂定尺剪岗位".equals(vo.getRoleName()) ||
                            "宽厚板厂精整岗位".equals(vo.getRoleName()) ||
                            "宽厚板厂双边剪岗位".equals(vo.getRoleName()) ||
                            "宽厚板厂切头剪岗位".equals(vo.getRoleName())){

                        int done = jzVo.getDone();
                        done += vo.getDone();
                        jzVo.setDone(done);

                        int undone = jzVo.getUnDone();
                        undone += vo.getUnDone();
                        jzVo.setUnDone(undone);

                        int total = jzVo.getTotal();
                        total += vo.getTotal();
                        jzVo.setTotal(total);

                        int isFalse = jzVo.getIsFalse();
                        isFalse += vo.getIsFalse();
                        jzVo.setIsFalse(isFalse);

                        int firstLevel = jzVo.getFirstLevel();
                        firstLevel += vo.getFirstLevel();
                        jzVo.setFirstLevel(firstLevel);

                        int secondLevel = jzVo.getSecondLevel();
                        secondLevel += vo.getSecondLevel();
                        jzVo.setSecondLevel(firstLevel);

                        int thirdLevel = jzVo.getThirdLevel();
                        thirdLevel += vo.getThirdLevel();
                        jzVo.setThirdLevel(thirdLevel);

                        int adviceAtypia = jzVo.getAdviceAtypia();
                        adviceAtypia += vo.getAdviceAtypia();
                        jzVo.setAdviceAtypia(adviceAtypia);

                        int analysisAtypia = jzVo.getAnalysisAtypia();
                        analysisAtypia += vo.getAnalysisAtypia();
                        jzVo.setAnalysisAtypia(analysisAtypia);

                        result.add(jzVo);
                        continue;

                    }
                    result.add(vo);
                }


                for(DoneRateVo item : result){
                    if (item.getTotal() != 0 && item.getDone() != 0) {
                        BigDecimal dv1 = new BigDecimal(item.getDone());
                        BigDecimal dv2 = new BigDecimal(item.getTotal());
                        double rate = dv1.divide(dv2, 2, RoundingMode.HALF_UP).doubleValue() * 100;
                        item.setRate(String.format("%.2f", rate));
                    } else if (item.getTotal() == 0 && item.getDone() != 0) {
                        item.setRate("0");
                    } else if (item.getTotal() == 0 && item.getDone() == 0) {
                        item.setRate("100");
                    } else {
                        item.setRate("0");
                    }
                }

                return result.stream().sorted(Comparator.comparing(DoneRateVo::getRate).reversed()).collect(Collectors.toList());
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }




    @Override
    public List<DoneRateVo> getUndoneListByRoleIDsWithTime(JSONObject jsonObject) {

        List<String> roleIDs = new ArrayList<>();
        if (!ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            roleIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("roleIDs"), String.class);
        }
        if (!ToolsUtil.isEmpty(roleIDs)) {
            List<DoneRateVo> result = new ArrayList<>();
            try {
                Date startTime = DateUtils.parseDate(jsonObject.getString("startTime") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
                Date endTime = DateUtils.parseDate(jsonObject.getString("endTime") + " 23:59:59", "yyyy-MM-dd HH:mm:ss");

                DoneRateVo vo;

                List<Map<String, Object>> mapList = warningInfoDao.getUndoneListByRoleIDsWithTime(roleIDs, startTime, endTime);
                for (Map<String, Object> item : mapList) {
                    vo = new DoneRateVo();
                    if (ToolsUtil.isEmpty(item.get("MODULENAME")) || ToolsUtil.isEmpty(item.get("MODULECODE"))) {
                        continue;
                    }

                    vo.setModuleName(String.valueOf(item.get("MODULENAME")));
                    vo.setModuleCode(String.valueOf(item.get("MODULECODE")));

                    if (ToolsUtil.isEmpty(item.get("TOTAL"))) {
                        vo.setTotal(0);
                    } else {
                        vo.setTotal(((BigInteger) item.get("TOTAL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(item.get("DONE"))) {
                        vo.setDone(0);
                    } else {
                        vo.setDone(((BigDecimal) item.get("DONE")).intValue());
                    }

                    if (ToolsUtil.isEmpty(item.get("UNDONE"))) {
                        vo.setUnDone(0);
                    } else {
                        vo.setUnDone(((BigDecimal) item.get("UNDONE")).intValue());
                    }

                    if (ToolsUtil.isEmpty(item.get("ISFALSE"))) {
                        vo.setIsFalse(0);
                    } else {
//                        vo.setIsFalse(((BigInteger)tempResult.get("ISFALSE")).intValue());
                        vo.setIsFalse(((BigDecimal) item.get("ISFALSE")).intValue());
                    }

                    result.add(vo);
                }
                return result;
            } catch (Exception e) {
                return result;
            }
        }
        return new ArrayList<>();
    }

//    @Override
//    public List<JSONObject> findRuleAlertCount(JSONObject jsonObject) {
//        List<Map<String , Object>> mapList = warningInfoDao.findRuleAlertCount();
//        JSONObject jb;
//        List<JSONObject> jsonObjectList = new ArrayList<>();
//        for(Map<String , Object> item : mapList){
//            jb = new JSONObject();
//            jb.put("ruleName" , item.get("ruleName"));
//            jb.put("total" , item.get("total"));
//            jsonObjectList.add(jb);
//        }
//        return jsonObjectList;
//    }

    @Override
    public void exportDoneRate(JSONObject jsonObject, HttpServletResponse response) {
        //功能描述：把同一个表格多个sheet测试结果重新输出
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("报警完成情况统计");
        // 设置sheet表头名称
        exportParams.setSheetName("报警完成情况统计");
        jsonObject.put("pageIndex", 1);
        jsonObject.put("pageSize", Integer.MAX_VALUE);

        List<DoneRateVo> voList = getDoneRateByRoleIDs(jsonObject);
        List<DoneRateEO> eoList = JSONArray.parseArray(JSON.toJSONString(voList), DoneRateEO.class);

        try {
            @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(exportParams, DoneRateEO.class, eoList);
            ToolsUtil.downLoadExcel("报警完成情况统计", response, workBook);
        } catch (Exception e) {

        }
    }


    @Override
    public PageVo<JSONObject> findRuleAlertCount(JSONObject jsonObject) {
        Session session = entityManager.unwrap(Session.class);

        int pageSize = ToolsUtil.isEmpty(jsonObject.get("pageSize")) ? 10 : (int) jsonObject.get("pageSize");
        int pageIndex = ToolsUtil.isEmpty(jsonObject.get("pageIndex")) ? 1 : (int) jsonObject.get("pageIndex");

        String startTime = jsonObject.getString("startTime");
        String endTime = jsonObject.getString("endTime");

        StringBuilder dataSB = new StringBuilder();
        StringBuilder countSB = new StringBuilder();

        countSB.append("select count(distinct wr.ruleName) as total\n" +
                "from warning_info wi\n" +
                "         join warning_rule wr\n" +
                "              on wi.warningRuleID = wr.id\n" +
                "where 1 = 1");

        if (!ToolsUtil.isEmpty(startTime) && !ToolsUtil.isEmpty(endTime)) {
            countSB.append(" and wi.createDateTime between '" + startTime + "' and '" + endTime + "' ");
        }


        dataSB.append("select a.id ,  a.ruleName, a.total\n" +
                "from (select wr.id, wr.ruleName, count(wi.id) as total\n" +
                "      from warning_info wi\n" +
                "               join warning_rule wr\n" +
                "                    on wi.warningRuleID = wr.id\n" +
                "      where 1 = 1 ");

        if (!ToolsUtil.isEmpty(startTime) && !ToolsUtil.isEmpty(endTime)) {
            dataSB.append(" and wi.createDateTime between '" + startTime + "' and '" + endTime + "' ");
        }
        dataSB.append("      group by wr.id, wr.ruleName\n" +
                "      order by total desc\n");
        dataSB.append(" limit " + ((pageIndex - 1) * pageSize + 1) + "," + pageIndex * pageSize);
        dataSB.append(" ) a");


        SQLQuery dataSqlQuery = session.createSQLQuery(dataSB.toString());
        dataSqlQuery.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        SQLQuery countSqlQuery = session.createSQLQuery(countSB.toString());

        List<Map<String, Object>> mapList = dataSqlQuery.list();
        int count = ((BigInteger) countSqlQuery.getSingleResult()).intValue();

        JSONObject jb;
        List<JSONObject> jsonObjectList = new ArrayList<>();
        for (Map<String, Object> item : mapList) {
            jb = new JSONObject();
            jb.put("id", item.get("id"));
            jb.put("ruleName", item.get("ruleName"));
            jb.put("total", item.get("total"));
            jsonObjectList.add(jb);
        }
        Map<String, Integer> map = new HashMap<>();
        map.put("pageIndex", pageIndex);
        map.put("pageSize", pageSize);

        PageVo<JSONObject> pageVo = new PageVo();
        pageVo.setPageable(map);
        pageVo.setContent(jsonObjectList);
        pageVo.setTotalElements(count);

        BigDecimal v1 = new BigDecimal(pageSize);
        BigDecimal v2 = new BigDecimal(count);

        pageVo.setTotalPages(v2.divide(v1, 0, RoundingMode.CEILING).intValue());

        return pageVo;
    }

    @Override
    public Page<WarningInfo> findHistoryByAlertID(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        if (!ToolsUtil.isEmpty(jsonObject.get("alertInfoID"))) {
            WarningInfo warningInfo = warningInfoDao.getById(jsonObject.getString("alertInfoID"));
            String warningRuleID = warningInfo.getWarningRuleID();
            String alertAdvice = jsonObject.getString("alertAdvice");
            if (!ToolsUtil.isEmpty(warningRuleID)) {
                Page<WarningInfo> WarningInfoPage = warningInfoDao.findAll(new Specification<WarningInfo>() {
                    @Override
                    public Predicate toPredicate(Root<WarningInfo> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                        List<Predicate> list = new ArrayList<Predicate>();
                        Predicate[] arr = new Predicate[list.size()];

                        String startTime = jsonObject.getString("startTime");
                        String endTime = jsonObject.getString("endTime");

                        if (!ToolsUtil.isEmpty(warningRuleID)) {
                            list.add(cb.equal(root.get("warningRuleID"), warningRuleID));
                        }

                        if (!ToolsUtil.isEmpty(alertAdvice)) {
                            list.add(cb.like(root.get("alertAdvice"), "%" + alertAdvice + "%"));
                        }

                        if (!ToolsUtil.isEmpty(startTime)) {
                            list.add(cb.greaterThanOrEqualTo(root.get("createDateTime").as(String.class), startTime + ":00"));
                        }
                        if (!ToolsUtil.isEmpty(endTime)) {
                            list.add(cb.lessThanOrEqualTo(root.get("createDateTime").as(String.class), endTime + ":59"));
                        }

                        cq.where(list.toArray(arr));
                        cq.orderBy(cb.desc(root.get("createDateTime")));
                        return null;
                    }
                }, pageable);
                return WarningInfoPage;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public Page<WarningInfo> findHistoryByWarningRuleID(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        if (!ToolsUtil.isEmpty(jsonObject.get("warningRuleID"))) {
            String warningRuleID = jsonObject.getString("warningRuleID");
            if (!ToolsUtil.isEmpty(warningRuleID)) {
                Page<WarningInfo> WarningInfoPage = warningInfoDao.findAll(new Specification<WarningInfo>() {
                    @Override
                    public Predicate toPredicate(Root<WarningInfo> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                        List<Predicate> list = new ArrayList<Predicate>();
                        Predicate[] arr = new Predicate[list.size()];

                        String startTime = jsonObject.getString("startTime");
                        String endTime = jsonObject.getString("endTime");

                        if (!ToolsUtil.isEmpty(warningRuleID)) {
                            list.add(cb.equal(root.get("warningRuleID"), warningRuleID));
                        }

                        if (!ToolsUtil.isEmpty(startTime)) {
                            list.add(cb.greaterThanOrEqualTo(root.get("createDateTime").as(String.class), startTime + " 00:00:00"));
                        }
                        if (!ToolsUtil.isEmpty(endTime)) {
                            list.add(cb.lessThanOrEqualTo(root.get("createDateTime").as(String.class), endTime + " 23:59:59"));
                        }

                        cq.where(list.toArray(arr));
                        cq.orderBy(cb.desc(root.get("createDateTime")));
                        return null;
                    }
                }, pageable);
                return WarningInfoPage;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public void exportHistory(JSONObject jsonObject, HttpServletResponse response) {
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("历史记录");
        // 设置sheet表头名称
        exportParams.setSheetName("历史记录");
        jsonObject.put("pageIndex", 1);
        jsonObject.put("pageSize", Integer.MAX_VALUE);

        Page<WarningInfo> warningInfoPage = findHistoryByAlertID(jsonObject);
        List<WarningInfo> warningInfoList = warningInfoPage.get().collect(Collectors.toList());
        List<WarningInfoEO> warningRuleEOList = JSONArray.parseArray(JSON.toJSONString(warningInfoList), WarningInfoEO.class);
        warningRuleEOList = warningRuleEOList.stream().map(x -> {
            if (!ToolsUtil.isEmpty(x.getStatus())) {
                if (0 == x.getStatus()) {
                    x.setStatusName("否");
                }

                if (2 == x.getStatus()) {
                    x.setStatusName("是");
                }
            }
            if (!ToolsUtil.isEmpty(x.getIsFalse())) {
                if (0 == x.getIsFalse()) {
                    x.setIsFalseName("否");
                }

                if (1 == x.getIsFalse()) {
                    x.setIsFalseName("是");
                }
            }
            if(!ToolsUtil.isEmpty(x.getDealTime()) && !ToolsUtil.isEmpty(x.getCreateDateTime())){
                x.setDiffTime(DateUtils.calTimeDiffString(x.getDealTime() , x.getCreateDateTime()));
            }
            return x;
        }).collect(Collectors.toList());
        try {
            @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(exportParams, WarningInfoEO.class, warningRuleEOList);
            ToolsUtil.downLoadExcel("历史记录", response, workBook);
        } catch (Exception e) {

        }
    }

    @Override
    public void exportHistoryByWarningRuleID(JSONObject jsonObject, HttpServletResponse response) {
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("历史记录");
        // 设置sheet表头名称
        exportParams.setSheetName("历史记录");
        jsonObject.put("pageIndex", 1);
        jsonObject.put("pageSize", Integer.MAX_VALUE);

        Page<WarningInfo> warningInfoPage = findHistoryByWarningRuleID(jsonObject);
        List<WarningInfo> warningInfoList = warningInfoPage.get().collect(Collectors.toList());
        List<WarningInfoEO> warningRuleEOList = JSONArray.parseArray(JSON.toJSONString(warningInfoList), WarningInfoEO.class);
        warningRuleEOList = warningRuleEOList.stream().map(x -> {
            if (!ToolsUtil.isEmpty(x.getStatus())) {
                if (0 == x.getStatus()) {
                    x.setStatusName("否");
                }

                if (2 == x.getStatus()) {
                    x.setStatusName("是");
                }
            }
            if (!ToolsUtil.isEmpty(x.getIsFalse())) {
                if (0 == x.getIsFalse()) {
                    x.setIsFalseName("否");
                }

                if (1 == x.getIsFalse()) {
                    x.setIsFalseName("是");
                }
            }
            if(!ToolsUtil.isEmpty(x.getDealTime()) && !ToolsUtil.isEmpty(x.getCreateDateTime())){
                x.setDiffTime(DateUtils.calTimeDiffString(x.getDealTime() , x.getCreateDateTime()));
            }
            return x;
        }).collect(Collectors.toList());
        try {
            @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(exportParams, WarningInfoEO.class, warningRuleEOList);
            ToolsUtil.downLoadExcel("历史记录", response, workBook);
        } catch (Exception e) {

        }
    }

    @Override
    public void exportAlertInfoByRoleIDs(JSONObject jsonObject, HttpServletResponse response) {
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("角色KPI统计详情");
        // 设置sheet表头名称
        exportParams.setSheetName("角色KPI统计详情");
        jsonObject.put("pageIndex", 1);
        jsonObject.put("pageSize", Integer.MAX_VALUE);

        List<WarningInfo> warningInfoList = findAlertListByRoleIDAndTime(jsonObject);
        List<WarningInfoEO> warningRuleEOList = JSONArray.parseArray(JSON.toJSONString(warningInfoList), WarningInfoEO.class);

        warningRuleEOList = warningRuleEOList.stream().map(x -> {
            if (!ToolsUtil.isEmpty(x.getStatus())) {
                if (0 == x.getStatus()) {
                    x.setStatusName("否");
                }

                if (2 == x.getStatus()) {
                    x.setStatusName("是");
                }
            }
            if (!ToolsUtil.isEmpty(x.getIsFalse())) {
                if (0 == x.getIsFalse()) {
                    x.setIsFalseName("否");
                }

                if (1 == x.getIsFalse()) {
                    x.setIsFalseName("是");
                }
            }

            if(!ToolsUtil.isEmpty(x.getDealTime()) && !ToolsUtil.isEmpty(x.getCreateDateTime())){
                x.setDiffTime(DateUtils.calTimeDiffString(x.getDealTime() , x.getCreateDateTime()));
            }

            return x;
        }).collect(Collectors.toList());
        try {
            @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(exportParams, WarningInfoEO.class, warningRuleEOList);
            ToolsUtil.downLoadExcel("角色KPI统计详情", response, workBook);
        } catch (Exception e) {

        }
    }

    @Override
    public List<Map<String, Object>> findHistoryByWaringEventID(JSONObject jsonObject) {
        if (!ToolsUtil.isEmpty(jsonObject.get("warningEventID"))) {
            return warningInfoDao.findHistoryByWaringEventID(jsonObject.getString("warningEventID"));
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> findSummaryByWarningRuleID(JSONObject jsonObject) {
        try{
            if (!ToolsUtil.isEmpty(jsonObject.get("warningRuleID"))) {
                List<Map<String, Object>> mapList = new ArrayList<>();
                if(!ToolsUtil.isEmpty(jsonObject.get("startTime")) && !ToolsUtil.isEmpty(jsonObject.get("endTime"))){
                    String startTime = jsonObject.getString("startTime") + " 00:00:00";
                    String endTime = jsonObject.getString("endTime") + " 23:59:59";
                    Date start = DateUtils.parseDate(startTime , "yyyy-MM-dd HH:mm:ss");
                    Date end = DateUtils.parseDate(endTime , "yyyy-MM-dd HH:mm:ss");
                    mapList.addAll(warningInfoDao.findSummaryByWarningRuleIDWithTimeRange(jsonObject.getString("warningRuleID") , start , end));
                }else {
                    mapList.addAll(warningInfoDao.findSummaryByWarningRuleID(jsonObject.getString("warningRuleID")));
                }
                return mapList;
            }
        }catch (Exception e){
            return null;
        }
        return null;
    }

    @Override
    public List<DoneRateVo> getDoneRateByModuleCodes(JSONObject jsonObject) {
        List<String> moduleCodes = new ArrayList<>();

        if (!ToolsUtil.isEmpty(jsonObject.get("moduleCodes"))) {
            moduleCodes = ToolsUtil.jsonObjectToEntityList(jsonObject.get("moduleCodes"), String.class);
        } else {
            moduleCodes = warningInfoDao.findModuleCodes();
        }

        if (!ToolsUtil.isEmpty(moduleCodes)) {
            List<DoneRateVo> result = new ArrayList<>();

            Map<String, Object> tempResult;
            try {
                Date startTime = DateUtils.parseDate(jsonObject.getString("startTime") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
                Date endTime = DateUtils.parseDate(jsonObject.getString("endTime") + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
                DoneRateVo vo;
                for (String moduleCode : moduleCodes) {
                    vo = new DoneRateVo();
                    tempResult = warningInfoDao.getUndoneRateByModuleCode(moduleCode, startTime, endTime);

                    if (ToolsUtil.isEmpty(tempResult.get("MODULENAME")) || ToolsUtil.isEmpty(tempResult.get("MODULECODE"))) {
                        continue;
                    }

                    vo.setRoleName(String.valueOf(tempResult.get("MODULENAME")));
                    vo.setModuleCode(String.valueOf(tempResult.get("MODULECODE")));

                    if (ToolsUtil.isEmpty(tempResult.get("FIRSTLEVEL"))) {
                        vo.setFirstLevel(0);
                    } else {
                        vo.setFirstLevel(((BigDecimal) tempResult.get("FIRSTLEVEL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("SECONDLEVEL"))) {
                        vo.setSecondLevel(0);
                    } else {
                        vo.setSecondLevel(((BigDecimal) tempResult.get("SECONDLEVEL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("THIRDLEVEL"))) {
                        vo.setThirdLevel(0);
                    } else {
                        vo.setThirdLevel(((BigDecimal) tempResult.get("THIRDLEVEL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("DONE"))) {
                        vo.setDone(0);
                    } else {
                        vo.setDone(((BigDecimal) tempResult.get("DONE")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("UNDONE"))) {
                        vo.setUnDone(0);
                    } else {
                        vo.setUnDone(((BigDecimal) tempResult.get("UNDONE")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("TOTAL"))) {
                        vo.setTotal(0);
                    } else {
                        vo.setTotal(((BigInteger) tempResult.get("TOTAL")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("ISFALSE"))) {
                        vo.setIsFalse(0);
                    } else {
                        vo.setIsFalse(((BigDecimal) tempResult.get("ISFALSE")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("ADVICEATYPIA"))) {
                        vo.setAdviceAtypia(0);
                    } else {
                        vo.setAdviceAtypia(((BigDecimal) tempResult.get("ADVICEATYPIA")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("ANALYSISATYPIA"))) {
                        vo.setAnalysisAtypia(0);
                    } else {
                        vo.setAnalysisAtypia(((BigDecimal) tempResult.get("ANALYSISATYPIA")).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("alertAdviceCount".toUpperCase()))) {
                        vo.setAlertAdviceCount(0);
                    } else {
                        vo.setAlertAdviceCount(((BigDecimal) tempResult.get("alertAdviceCount".toUpperCase())).intValue());
                    }


                    if (ToolsUtil.isEmpty(tempResult.get("twoHourDone".toUpperCase()))) {
                        vo.setTwoHourDone(0);
                    } else {
                        vo.setTwoHourDone(((BigDecimal) tempResult.get("twoHourDone".toUpperCase())).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("fourHourDone".toUpperCase()))) {
                        vo.setFourHourDone(0);
                    } else {
                        vo.setFourHourDone(((BigDecimal) tempResult.get("fourHourDone".toUpperCase())).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("eightHourDone".toUpperCase()))) {
                        vo.setEightHourDone(0);
                    } else {
                        vo.setEightHourDone(((BigDecimal) tempResult.get("eightHourDone".toUpperCase())).intValue());
                    }

                    if (ToolsUtil.isEmpty(tempResult.get("gtEightHourDone".toUpperCase()))) {
                        vo.setGtEightHourDone(0);
                    } else {
                        vo.setGtEightHourDone(((BigDecimal) tempResult.get("gtEightHourDone".toUpperCase())).intValue());
                    }


                    if (vo.getTotal() != 0 && vo.getDone() != 0) {
                        BigDecimal dv1 = new BigDecimal(vo.getDone());
                        BigDecimal dv2 = new BigDecimal(vo.getTotal());
                        double rate = dv1.divide(dv2, 2, RoundingMode.HALF_UP).doubleValue() * 100;
                        vo.setRate(String.format("%.2f", rate));
                    } else if (vo.getTotal() == 0 && vo.getDone() != 0) {
                        vo.setRate("0");
                    } else if (vo.getTotal() == 0 && vo.getDone() == 0) {
                        vo.setRate("100");
                    } else {
                        vo.setRate("0");
                    }
                    result.add(vo);
                }
                return result.stream().sorted(Comparator.comparing(DoneRateVo::getTotal).reversed()).collect(Collectors.toList());
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    @Override
    public void exportDoneRateByModuleCodes(JSONObject jsonObject, HttpServletResponse response) {
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("模块KPI统计详情");
        // 设置sheet表头名称
        exportParams.setSheetName("模块KPI统计详情");
        jsonObject.put("pageIndex", 1);
        jsonObject.put("pageSize", Integer.MAX_VALUE);

        List<WarningInfo> warningInfoList = findAlertListByModuleCodeAndTime(jsonObject);
        if(ToolsUtil.isEmpty(warningInfoList)){
            try{
                @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(exportParams, WarningInfoEO.class, new ArrayList<WarningInfoEO>());
                ToolsUtil.downLoadExcel("模块KPI统计详情", response, workBook);
                return;
            }catch (Exception e){

            }
        }

        List<WarningInfoEO> warningRuleEOList = JSONArray.parseArray(JSON.toJSONString(warningInfoList), WarningInfoEO.class);
        warningRuleEOList = warningRuleEOList.stream().map(x -> {
            if (!ToolsUtil.isEmpty(x.getStatus())) {
                if (0 == x.getStatus()) {
                    x.setStatusName("否");
                }

                if (2 == x.getStatus()) {
                    x.setStatusName("是");
                }
            }
            if (!ToolsUtil.isEmpty(x.getIsFalse())) {
                if (0 == x.getIsFalse()) {
                    x.setIsFalseName("否");
                }

                if (1 == x.getIsFalse()) {
                    x.setIsFalseName("是");
                }

            }
            if(!ToolsUtil.isEmpty(x.getDealTime()) && !ToolsUtil.isEmpty(x.getCreateDateTime())){
                x.setDiffTime(DateUtils.calTimeDiffString(x.getDealTime() , x.getCreateDateTime()));
            }
            return x;
        }).collect(Collectors.toList());
        try {
            @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(exportParams, WarningInfoEO.class, warningRuleEOList);
            ToolsUtil.downLoadExcel("模块KPI统计详情", response, workBook);
        } catch (Exception e) {

        }
    }

    @Override
    public Map<String, List<DoneRateVo>> findRateByRoleIDsWithTime(JSONObject jsonObject) {
        List<String> roleIDs = new ArrayList<>();
        if (ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && !ToolsUtil.isEmpty(jsonObject.get("productionLineName")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            List<String> productionLineNameList = ToolsUtil.jsonObjectToEntityList(jsonObject.get("productionLineName"), String.class);
            for (String plName : productionLineNameList) {
                roleIDs.addAll(roleDao.findRolesByProductionLineName(plName).stream().map(x -> x.getId()).collect(Collectors.toList()));
            }
            roleIDs = roleIDs.stream().distinct().collect(Collectors.toList());
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            roleIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("roleIDs"), String.class);
        }

        if (!ToolsUtil.isEmpty(roleIDs)) {

            Map<String, List<DoneRateVo>> result = new HashMap<>();
            List<Map<String, Object>> tempResult;
            List<DoneRateVo> doneRateVoList;

            try {
                Date startTime = DateUtils.parseDate(jsonObject.getString("startTime") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
                Date endTime = DateUtils.parseDate(jsonObject.getString("endTime") + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
                Role role;
                DoneRateVo vo;

                for (String roleID : roleIDs) {
                    role = roleDao.getById(roleID);
                    tempResult = warningInfoDao.findRateByRoleIDsWithTime(roleID, startTime, endTime);
                    doneRateVoList = new ArrayList<>();

                    for (Map<String, Object> item : tempResult) {
                        vo = new DoneRateVo();

                        if (!ToolsUtil.isEmpty(item.get("DATETIME"))) {
                            vo.setDateTime((String) item.get("DATETIME"));
                        }


                        if (ToolsUtil.isEmpty(item.get("FIRSTLEVEL"))) {
                            vo.setFirstLevel(0);
                        } else {
                            vo.setFirstLevel(((BigDecimal) item.get("FIRSTLEVEL")).intValue());
                        }

                        if (ToolsUtil.isEmpty(item.get("SECONDLEVEL"))) {
                            vo.setSecondLevel(0);
                        } else {
                            vo.setSecondLevel(((BigDecimal) item.get("SECONDLEVEL")).intValue());
                        }

                        if (ToolsUtil.isEmpty(item.get("THIRDLEVEL"))) {
                            vo.setThirdLevel(0);
                        } else {
                            vo.setThirdLevel(((BigDecimal) item.get("THIRDLEVEL")).intValue());
                        }

                        if (ToolsUtil.isEmpty(item.get("DONE"))) {
                            vo.setDone(0);
                        } else {
                            vo.setDone(((BigDecimal) item.get("DONE")).intValue());
                        }

                        if (ToolsUtil.isEmpty(item.get("UNDONE"))) {
                            vo.setUnDone(0);
                        } else {
                            vo.setUnDone(((BigDecimal) item.get("UNDONE")).intValue());
                        }

                        if (ToolsUtil.isEmpty(item.get("TOTAL"))) {
                            vo.setTotal(0);
                        } else {
                            vo.setTotal(((BigInteger) item.get("TOTAL")).intValue());
                        }

                        if (ToolsUtil.isEmpty(item.get("ISFALSE"))) {
                            vo.setIsFalse(0);
                        } else {
                            vo.setIsFalse(((BigDecimal) item.get("ISFALSE")).intValue());
                        }

                        if (vo.getTotal() != 0 && vo.getDone() != 0) {
                            BigDecimal dv1 = new BigDecimal(vo.getDone());
                            BigDecimal dv2 = new BigDecimal(vo.getTotal());
                            double rate = dv1.divide(dv2, 2, RoundingMode.HALF_UP).doubleValue() * 100;
                            vo.setRate(String.format("%.2f", rate));
                        } else if (vo.getTotal() == 0 && vo.getDone() != 0) {
                            vo.setRate("0");
                        } else if (vo.getTotal() == 0 && vo.getDone() == 0) {
                            vo.setRate("100.00");
                        } else {
                            vo.setRate("0");
                        }

                        doneRateVoList.add(vo);
                    }

                    result.put(role.getRoleName(), doneRateVoList);
                }
                return result;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    @Override
    public Map<String, List<DoneRateVo>> findRateByModuleCodesWithTime(JSONObject jsonObject) {
        List<String> moduleCodes = new ArrayList<>();

        if (!ToolsUtil.isEmpty(jsonObject.get("moduleCodes"))) {
            moduleCodes = ToolsUtil.jsonObjectToEntityList(jsonObject.get("moduleCodes"), String.class);
        } else {
            moduleCodes = warningInfoDao.findModuleCodes();
        }

        if (!ToolsUtil.isEmpty(moduleCodes)) {

            Map<String, List<DoneRateVo>> result = new HashMap<>();
            List<Map<String, Object>> tempResult;
            List<DoneRateVo> doneRateVoList;

            try {
                Date startTime = DateUtils.parseDate(jsonObject.getString("startTime") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
                Date endTime = DateUtils.parseDate(jsonObject.getString("endTime") + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
                Role role;
                DoneRateVo vo;


                for (String moduleCode : moduleCodes) {
                    tempResult = warningInfoDao.findRateByModuleCodesWithTime(moduleCode, startTime, endTime);
                    doneRateVoList = new ArrayList<>();
                    String moduleName = "";
                    for (Map<String, Object> item : tempResult) {
                        vo = new DoneRateVo();

                        if (ToolsUtil.isEmpty(item.get("MODULENAME")) || ToolsUtil.isEmpty(item.get("MODULECODE"))) {
                            continue;
                        }

                        moduleName = String.valueOf(item.get("MODULENAME"));

                        vo.setRoleName(String.valueOf(item.get("MODULENAME")));
                        vo.setModuleCode(String.valueOf(item.get("MODULECODE")));

                        if (!ToolsUtil.isEmpty(item.get("DATETIME"))) {
                            vo.setDateTime((String) item.get("DATETIME"));
                        }

                        if (ToolsUtil.isEmpty(item.get("FIRSTLEVEL"))) {
                            vo.setFirstLevel(0);
                        } else {
                            vo.setFirstLevel(((BigDecimal) item.get("FIRSTLEVEL")).intValue());
                        }

                        if (ToolsUtil.isEmpty(item.get("SECONDLEVEL"))) {
                            vo.setSecondLevel(0);
                        } else {
                            vo.setSecondLevel(((BigDecimal) item.get("SECONDLEVEL")).intValue());
                        }

                        if (ToolsUtil.isEmpty(item.get("THIRDLEVEL"))) {
                            vo.setThirdLevel(0);
                        } else {
                            vo.setThirdLevel(((BigDecimal) item.get("THIRDLEVEL")).intValue());
                        }

                        if (ToolsUtil.isEmpty(item.get("DONE"))) {
                            vo.setDone(0);
                        } else {
                            vo.setDone(((BigDecimal) item.get("DONE")).intValue());
                        }

                        if (ToolsUtil.isEmpty(item.get("UNDONE"))) {
                            vo.setUnDone(0);
                        } else {
                            vo.setUnDone(((BigDecimal) item.get("UNDONE")).intValue());
                        }

                        if (ToolsUtil.isEmpty(item.get("TOTAL"))) {
                            vo.setTotal(0);
                        } else {
                            vo.setTotal(((BigInteger) item.get("TOTAL")).intValue());
                        }

                        if (ToolsUtil.isEmpty(item.get("ISFALSE"))) {
                            vo.setIsFalse(0);
                        } else {
                            vo.setIsFalse(((BigDecimal) item.get("ISFALSE")).intValue());
                        }

                        if (vo.getTotal() != 0 && vo.getDone() != 0) {
                            BigDecimal dv1 = new BigDecimal(vo.getDone());
                            BigDecimal dv2 = new BigDecimal(vo.getTotal());
                            double rate = dv1.divide(dv2, 2, RoundingMode.HALF_UP).doubleValue() * 100;
                            vo.setRate(String.format("%.2f", rate));
                        } else if (vo.getTotal() == 0 && vo.getDone() != 0) {
                            vo.setRate("0");
                        } else if (vo.getTotal() == 0 && vo.getDone() == 0) {
                            vo.setRate("100.00");
                        } else {
                            vo.setRate("0");
                        }

                        doneRateVoList.add(vo);
                    }

                    result.put(moduleName, doneRateVoList);
                }
                return result;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    @Override
    public Map<String, List<DoneRateVo>> findIsFalseByModuleCodesWithTime(JSONObject jsonObject) {
        return null;
    }

    @Override
    public List<DoneRateVo> getUnDoneRateByRoleIDs(JSONObject jsonObject) {
        return null;
    }

    @Override
    public List<DoneRateVo> getUnDoneRateGroupRoles(JSONObject jsonObject) {
        return null;
    }

    @Override
    public Page<WarningInfo> findHistoryByRoleAndTime(JSONObject jsonObject) {
        String roleID = jsonObject.getString("roleID");
        String status = jsonObject.getString("status");
        String startTime = jsonObject.getString("startTime");
        String endTime = jsonObject.getString("endTime");
        Pageable pageable = ToolsUtil.initPage(jsonObject);
        return warningInfoDao.findHistoryByRoleAndTime(roleID , status , DateUtils.parseDateDef(startTime) , DateUtils.parseDateDef(endTime) , pageable);
    }

    @Override
    public Map<String , Object> findFinishSummary(JSONObject jsonObject) {
        String startTime = jsonObject.getString("startTime");
        String endTime = jsonObject.getString("endTime");

        List<String> roleIDs = new ArrayList<>();
        if (ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && !ToolsUtil.isEmpty(jsonObject.get("productionLineName")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            List<String> productionLineNameList = ToolsUtil.jsonObjectToEntityList(jsonObject.get("productionLineName"), String.class);
            for (String plName : productionLineNameList) {
                roleIDs.addAll(roleDao.findRolesByProductionLineName(plName).stream().map(x -> x.getId()).collect(Collectors.toList()));
            }
            roleIDs = roleIDs.stream().distinct().collect(Collectors.toList());
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            roleIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("roleIDs"), String.class);
        }

        if (ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && ToolsUtil.isEmpty(jsonObject.get("productionLineName")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            List<String> productionLineNameList = Arrays.asList("第一炼钢厂", "宽厚板厂");
            for (String plName : productionLineNameList) {
                roleIDs.addAll(roleDao.findRolesByProductionLineName(plName).stream().map(x -> x.getId()).collect(Collectors.toList()));
            }
            roleIDs = roleIDs.stream().distinct().collect(Collectors.toList());
        }

        List<Map<String , Object>> mapList = warningInfoDao.findFinishSummary(DateUtils.parseDateDef(startTime) , DateUtils.parseDateDef(endTime) , roleIDs);
        return mapList.get(0);
    }


    List<WarningInfo> findAlertListByRoleIDAndTime(JSONObject jsonObject) {
        List<String> roleIDs = new ArrayList<>();
        if (ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && !ToolsUtil.isEmpty(jsonObject.get("productionLineName")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            List<String> productionLineNameList = ToolsUtil.jsonObjectToEntityList(jsonObject.get("productionLineName"), String.class);
            for (String plName : productionLineNameList) {
                roleIDs.addAll(roleDao.findRolesByProductionLineName(plName).stream().map(x -> x.getId()).collect(Collectors.toList()));
            }
            roleIDs = roleIDs.stream().distinct().collect(Collectors.toList());
        }

        if (!ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            roleIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("roleIDs"), String.class);
        }

        if (ToolsUtil.isEmpty(jsonObject.get("roleIDs")) && ToolsUtil.isEmpty(jsonObject.get("productionLineName")) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            List<String> productionLineNameList = Arrays.asList("第一炼钢厂", "宽厚板厂");
            for (String plName : productionLineNameList) {
                roleIDs.addAll(roleDao.findRolesByProductionLineName(plName).stream().map(x -> x.getId()).collect(Collectors.toList()));
            }
            roleIDs = roleIDs.stream().distinct().collect(Collectors.toList());
        }


        if (!ToolsUtil.isEmpty(roleIDs)) {
            try {
                Date startTime = DateUtils.parseDate(jsonObject.getString("startTime") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
                Date endTime = DateUtils.parseDate(jsonObject.getString("endTime") + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
                List<WarningInfo> result = new ArrayList<>();
                for (String roleID : roleIDs) {
                    result.addAll(warningInfoDao.findAlertListByRoleIDAndTime(roleID, startTime, endTime));
                }
                return result;
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    List<WarningInfo> findAlertListByModuleCodeAndTime(JSONObject jsonObject) {
        List<String> moduleCodes = new ArrayList<>();
        if(ToolsUtil.isEmpty(jsonObject.get("moduleCodes"))){
            moduleCodes = warningInfoDao.findModuleCodes();
        }else{
            moduleCodes = ToolsUtil.jsonObjectToEntityList(jsonObject.get("moduleCodes"), String.class);
        }

        if (!ToolsUtil.isEmpty(moduleCodes) && !ToolsUtil.isEmpty("startTime") && !ToolsUtil.isEmpty("endTime")) {
            try {
                Date startTime = DateUtils.parseDate(jsonObject.getString("startTime") + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
                Date endTime = DateUtils.parseDate(jsonObject.getString("endTime") + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
                List<WarningInfo> result = new ArrayList<>();
                for (String moduleCode : moduleCodes) {
                    result.addAll(warningInfoDao.findAlertListByModuleCodeAndTime(moduleCode, startTime, endTime));
                }
                return result;
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    List<Map<String, String>> findIDNameMap(String sql) {
        Session session = entityManager.unwrap(Session.class);
        SQLQuery dataSqlQuery = session.createSQLQuery(sql);
        dataSqlQuery.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return dataSqlQuery.list();
    }
}

