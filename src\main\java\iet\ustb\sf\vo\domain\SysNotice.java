package iet.ustb.sf.vo.domain;


import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;
import java.util.StringTokenizer;

/**
 * 系统公告
 *
 * <AUTHOR>
 * @create 2024-02-18
 * @see BaseEntity
 */
@Data
@Entity
@Table(name = "SYS_NOTICE")
public class SysNotice extends BaseEntity {

    /**
     * 标题
     */
    @Column(length = 128, nullable = false)
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 内容
     */
    @Column(length = 5000)
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 资源id
     */
    @Column(length = 500)
    @ApiModelProperty(value = "资源id")
    private String resourceId;

    /**
     * 发布人
     */
    @Column(length = 64)
    @ApiModelProperty(value = "发布人")
    private String publishUserName;

    /**
     * 切换时间(秒)
     */
    @Column(length = 64)
    @ApiModelProperty(value = "切换时间(秒)")
    private String switchTime;

    /**
     * 公告部门
     */
    @Column(columnDefinition = "int")
    @ApiModelProperty(value = "公告部门 ：0-事业部 1-板卷厂 2-宽板厂 3-中板厂 4-一炼钢厂 5-金石厂 6-金润厂")
    private Integer noticeDept;

    /**
     * 公告类型
     */
    @Column(columnDefinition = "int default 1")
    @ApiModelProperty(value = "公告类型 ：1-文字 2-图片 3-视频")
    private Integer noticeType;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 状态
     */
    @Column(columnDefinition = "int default 1")
    @ApiModelProperty(value = "状态 ：1-草稿 2-发布 3-废弃")
    private Integer status;

    /**
     * 公告分类
     */
    @Column(columnDefinition = "int")
    @ApiModelProperty(value = "公告分类 ：1-信息发布 2-系统公告")
    private Integer noticeCategory;

    /**
     * 当前登录人账号
     */
    @Transient
    private String userNo;

    @Transient
    private List<String> idList;

}
