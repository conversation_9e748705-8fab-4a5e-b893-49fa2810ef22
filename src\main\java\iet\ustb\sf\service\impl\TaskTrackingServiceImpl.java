package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.TaskTrackingDao;
import iet.ustb.sf.vo.domain.TaskTracking;
import iet.ustb.sf.service.TaskTrackingService;
//import iet.ustb.sf.service.WeComMessagePushService;
import iet.ustb.sf.utils.DateTools;
import iet.ustb.sf.utils.ToolsUtil;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.apachecommons.CommonsLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务进度跟踪
 *
 * <AUTHOR>
 * @create 2024-04-01
 */
@Service
@CommonsLog
public class TaskTrackingServiceImpl implements TaskTrackingService {
    @Autowired
    private TaskTrackingDao taskTrackingDao;
//    @Autowired
//    private WeComMessagePushService weComMessagePushService;

    @Override
    public List<TaskTracking> findAll() {
        return taskTrackingDao.findAll();
    }

    @Override
    public List<TaskTracking> findAllByMultiCondition(JSONObject jsonObject) {
        return taskTrackingDao.findAll(createSpecs(jsonObject));
    }

    @Override
    public Page<TaskTracking> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<TaskTracking> trackingDaoPage = taskTrackingDao.findAll(createSpecs(jsonObject), pageable);
        return trackingDaoPage;
    }

    private Specification<TaskTracking> createSpecs(JSONObject json) {
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            String serviceName = json.getString("serviceName");// 模块名称
            String taskName = json.getString("taskName");// 任务名称
            Integer taskType = json.getInteger("taskType");// 任务类型
            String serviceUser = json.getString("serviceUser");// 业务责任人
            String devUser = json.getString("devUser");// 开发责任人
            String createStartDate = json.getString("createStartDate");// 填报开始日期
            String createEndDate = json.getString("createEndDate");// 填报结束日期
            String planCompleteStartDate = json.getString("planCompleteStartDate");// 计划完成开始日期
            String planCompleteEndDate = json.getString("planCompleteEndDate");// 计划完成结束日期
            String actualCompleteStartDate = json.getString("actualCompleteStartDate");// 实际完成开始日期
            String actualCompleteEndDate = json.getString("actualCompleteEndDate");// 实际完成结束日期
            Integer overdueDays = json.getInteger("overdueDays");// 超期天数
            Integer delayDays = json.getInteger("delayDays");// 延期天数
            Integer status = json.getInteger("status");// 状态

            if (StringUtils.isNotBlank(serviceName)) {
                list.add(cb.equal(root.get("serviceName"), serviceName));
            }
            if (StringUtils.isNotBlank(taskName)) {
                list.add(cb.like(root.get("taskName"), "%" + taskName + "%"));
            }
            if (taskType != null) {
                list.add(cb.equal(root.get("taskType"), taskType));
            }
            if (StringUtils.isNotBlank(serviceUser)) {
                list.add(cb.like(root.get("serviceUser"), "%" + serviceUser + "%"));
            }
            if (StringUtils.isNotBlank(devUser)) {
                list.add(cb.like(root.get("devUser"), "%" + devUser + "%"));
            }
            if (StringUtils.isNotBlank(createStartDate)) {
                list.add(cb.greaterThanOrEqualTo(root.get("createDateTime").as(String.class), createStartDate + " 00:00:00"));
            }
            if (StringUtils.isNotBlank(createEndDate)) {
                list.add(cb.lessThanOrEqualTo(root.get("createDateTime").as(String.class), createEndDate + " 23:59:59"));
            }
            if (StringUtils.isNotBlank(planCompleteStartDate)) {
                list.add(cb.greaterThanOrEqualTo(root.get("planCompleteDate"), planCompleteStartDate));
            }
            if (StringUtils.isNotBlank(planCompleteEndDate)) {
                list.add(cb.lessThanOrEqualTo(root.get("planCompleteDate"), planCompleteEndDate));
            }
            if (StringUtils.isNotBlank(actualCompleteStartDate)) {
                list.add(cb.greaterThanOrEqualTo(root.get("actualCompleteDate"), actualCompleteStartDate));
            }
            if (StringUtils.isNotBlank(actualCompleteEndDate)) {
                list.add(cb.lessThanOrEqualTo(root.get("actualCompleteDate"), actualCompleteEndDate));
            }
            if (overdueDays != null) {
                list.add(cb.equal(root.get("overdueDays"), overdueDays));
            }
            if (delayDays != null) {
                list.add(cb.equal(root.get("delayDays"), delayDays));
            }
            if (status != null) {
                list.add(cb.equal(root.get("status"), status));
            }
            query.where(list.toArray(new Predicate[list.size()]));
            query.orderBy(cb.desc(root.get("createDateTime")));
            return query.getRestriction();
        };
    }

    @Override
    public void save(TaskTracking taskTracking) {
        String id = taskTracking.getId();
        // 新增
        if (StringUtils.isBlank(id)) {
            taskTracking.setStatus(1);// 状态 默认 进行中
            taskTracking.setOverdueDays(0);// 超期天数 默认0
            taskTracking.setDelayDays(0);// 延期天数 默认0
        } else {
            TaskTracking taskTrackingOri = taskTrackingDao.findById(id).orElse(new TaskTracking());
            String planCompleteDateOri = taskTrackingOri.getPlanCompleteDate();
            String planCompleteDate = taskTracking.getPlanCompleteDate();
            if (!planCompleteDateOri.equals(planCompleteDate)) {
                // 计算两个日期相隔的天数
                int diffDays = DateTools.nDaysBetweenTwoDate(planCompleteDateOri, planCompleteDate);
                // 延期日期 = 已延期天数 + (新计划完成日期 - 原来计划完成日期)天数
                int currDelayDays = taskTracking.getDelayDays() + diffDays;
                taskTracking.setDelayDays(currDelayDays);
                if (currDelayDays > 0) {
                    taskTracking.setStatus(4);// 延期未完成
                    taskTracking.setScore(0);
                }
            }
            Integer statusOri = taskTrackingOri.getStatus();
            Integer status = taskTracking.getStatus();
            String actualCompleteDate = taskTracking.getActualCompleteDate();
            Integer delayDays = taskTracking.getDelayDays();
            Integer overdueDays = taskTracking.getOverdueDays();
            // 前端用户选择完成，则判断 若延期时间>0 且 实际完成时间<=计划完成时间，则延期完成；
            // 若 超期时间>0 且 实际完成时间<=计划完成时间，则超期完成
            if (statusOri != 2 && status == 2) {
                if (overdueDays > 0 && DateTools.nDaysBetweenTwoDate(planCompleteDate, actualCompleteDate) <= 0) {
                    taskTracking.setStatus(7);// 超期完成
                    taskTracking.setScore(3);
                } else if (delayDays > 0 && DateTools.nDaysBetweenTwoDate(planCompleteDate, actualCompleteDate) <= 0) {
                    taskTracking.setStatus(5);// 延期完成
                    taskTracking.setScore(5);
                } else {
                    taskTracking.setStatus(2);// 按期完成
                    taskTracking.setScore(10);
                }
            }
        }
        taskTrackingDao.save(taskTracking);
    }

    @Override
    public void saveAll(List<TaskTracking> taskTrackings) {
        taskTrackingDao.saveAll(taskTrackings);
    }

    @Override
    public void delete(TaskTracking taskTracking) throws Exception {
        taskTrackingDao.delete(taskTracking);
    }

    @Override
    public void sendMessageToWecom() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("roleIDs", Arrays.asList("190b4232-eb26-42f6-ac8c-4875449f2720"));

        JSONObject paramJson = new JSONObject();
        List<TaskTracking> taskTrackingList = this.findAllByMultiCondition(paramJson);
        taskTrackingList.stream().map(taskTracking -> {
            int diffDays;
            String actualCompleteDate = taskTracking.getActualCompleteDate();
            String planCompleteDate = taskTracking.getPlanCompleteDate();
            if (StringUtils.isNotBlank(actualCompleteDate)) {
                diffDays = DateTools.nDaysBetweenTwoDate(planCompleteDate, actualCompleteDate);
            } else {
                String currDate = DateTools.getFullNowDateTime("yyyy-MM-dd");
                diffDays = DateTools.nDaysBetweenTwoDate(planCompleteDate, currDate);
            }
            Integer status = taskTracking.getStatus();
            // 超期 且 状态为进行中或延期未完成时，更新超期天数 与 状态变更为超期未完成
            if (diffDays > 0 && (status == 1 || status == 4)) {
                taskTracking.setOverdueDays(diffDays);
                taskTracking.setStatus(6);// 超期未完成
                taskTracking.setScore(0);
            }
            return taskTracking;
        }).filter(taskTracking -> taskTracking.getStatus() == 6)// 超期未完成
        .forEach(taskTracking -> {
            // 更新 超期天数 与 状态变更为超期
            taskTrackingDao.save(taskTracking);

            String serviceName = taskTracking.getServiceName();
            String serviceUser = taskTracking.getServiceUser();
            String taskName = taskTracking.getTaskName();
            String planCompleteDate = taskTracking.getPlanCompleteDate();
            Integer overdueDays = taskTracking.getOverdueDays();

            String message = String.format("项目组内部任务管理\n模块名称：%s\n业务责任人：%s\n任务名称：%s\n计划完成时间：%s\n超期天数：%s天",
                    serviceName, serviceUser, taskName, planCompleteDate, overdueDays);
            jsonObject.put("message", message);

            String devUser = taskTracking.getDevUser();

            String[] serviceUsers = serviceUser.split("\\|");
            String serviceUserNo = serviceUsers.length > 0 ? serviceUsers[0] : null;

            String[] devUsers = devUser.split("\\|");
            String devUserNo = devUsers.length > 0 ? devUsers[0] : null;

            List<String> extraUserNos = Arrays.asList(serviceUserNo, devUserNo).stream().distinct().collect(Collectors.toList());
            jsonObject.put("extraUserNos", extraUserNos);

//            weComMessagePushService.sendToRolesByTextMessage(jsonObject);
        });
    }

    @Override
    public List<Map<String, Object>> findTaskScoreStatistics() {
        return taskTrackingDao.findTaskScoreStatistics();
    }
}
