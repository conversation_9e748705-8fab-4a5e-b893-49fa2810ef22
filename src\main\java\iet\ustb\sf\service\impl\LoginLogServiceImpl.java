package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.LoginLogDao;
import iet.ustb.sf.dao.OrgDao;
import iet.ustb.sf.dao.UserDao;
import iet.ustb.sf.vo.domain.LoginLog;
import iet.ustb.sf.vo.domain.Org;
import iet.ustb.sf.vo.domain.User;
import iet.ustb.sf.service.LoginLogService;
import iet.ustb.sf.utils.IpUtil;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.utils.UserThreadUtil;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.apachecommons.CommonsLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import java.util.LinkedList;
import java.util.List;

/**
 * 登录日志
 *
 * <AUTHOR>
 * @create 2022-09-21
 * @see LoginLogService
 */
@Service
@CommonsLog
public class LoginLogServiceImpl implements LoginLogService {

    @Autowired
    private LoginLogDao loginLogDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private OrgDao orgDao;

    @Override
    public List<LoginLog> findAll() {
        return loginLogDao.findAll();
    }

    @Override
    public Page<LoginLog> findByMultiCondition(JSONObject jsonObject) {

        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<LoginLog> pages = loginLogDao.findAll(createSpecs(jsonObject), pageable);

        return pages;
    }

    @Override
    public void save(LoginLog loginLog, HttpServletRequest request) {

        User user = UserThreadUtil.getUser();
        Assert.notNull(user, "该员工不存在");
        String userNo = user.getUserNo();

        // 开发人员不保存应用访问记录
        if ("dev".equals(user.getRemarks())) {
            return;
        }

        loginLog.setUserNo(userNo);
        loginLog.setOrgCode(user.getOrgCode());// 组织编号
        String ipAddress = IpUtil.getIpAddress(request);
        loginLog.setIpAddress(ipAddress);// ip地址
//        loginLog.setLoginCity(IpUtil.getCityByIP(ipAddress));// 登录城市
        loginLogDao.save(loginLog);
    }

    @Override
    public Boolean isDevUser(String userNo) {
        Boolean flag = false;
        // 过滤掉开发项目组人员
        List<Org> orgList = orgDao.findOrgsByParentOrgCode("X11110000");
        for (Org org : orgList) {
            List<User> userList = userDao.findUsersByOrgCode(org.getOrgCode());
            for (User user : userList) {
                if (user.getUserNo().equals(userNo)) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }

    private Specification<LoginLog> createSpecs(JSONObject json) {
        Specification<LoginLog> specs = (root, query, cb) -> {
            List<Predicate> list = new LinkedList<>();

            String userNo = json.getString("userNo");// 员工编号
            String orgCode = json.getString("orgCode");// 组织编号
            String startTime = json.getString("startTime");//起始日期
            String endTime = json.getString("endTime");//结束日期

            if (StringUtils.isNotBlank(userNo)) {
                list.add(cb.equal(root.get("userNo"), userNo));
            }
            if (StringUtils.isNotBlank(orgCode)) {
                list.add(cb.equal(root.get("orgCode"), orgCode));
            }
            if (StringUtils.isNotBlank(startTime)) {
                list.add(cb.greaterThanOrEqualTo(root.get("loginTime").as(String.class), startTime));
            }
            if (StringUtils.isNotBlank(endTime)) {
                list.add(cb.lessThanOrEqualTo(root.get("loginTime").as(String.class), endTime));
            }
            return query.where(list.toArray(new Predicate[list.size()])).getRestriction();
        };
        return specs;
    }


}
