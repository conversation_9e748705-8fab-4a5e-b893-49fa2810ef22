package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.LoginLog;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface LoginLogService {

    /**
     * 查找所有
     *
     * @return {@link List }<{@link LoginLog }>
     * <AUTHOR>
     * @create 2022-09-21
     */
    List<LoginLog> findAll();

    /**
     * 按多条件查找
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link LoginLog }>
     * <AUTHOR>
     * @create 2022-09-24
     */
    Page<LoginLog> findByMultiCondition(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param loginLog 登录日志
     * @param request
     * <AUTHOR>
     * @create 2022-09-21
     */
    void save(LoginLog loginLog, HttpServletRequest request);

    /**
     * 判断是否开发人员
     *
     * @param userNo 用户编号
     * @return {@link Boolean }
     * <AUTHOR>
     * @create 2022-09-28
     */
    Boolean isDevUser(String userNo);
}
