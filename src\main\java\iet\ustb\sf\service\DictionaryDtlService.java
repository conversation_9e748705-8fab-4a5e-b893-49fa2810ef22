package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.DictionaryDtl;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 数据字典详细信息Service
 *
 * <AUTHOR>
 * @create 2023-02-09
 */
public interface DictionaryDtlService {

    /**
     * 查找所有内容
     *
     * @return {@link List }<{@link DictionaryDtl }>
     * <AUTHOR>
     * @create 2023-02-09
     */
    List<DictionaryDtl> findAll();

    /**
     * 按多条件查找分页
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link DictionaryDtl }>
     * <AUTHOR>
     * @create 2023-02-09
     */
    Page<DictionaryDtl> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param dictionaryDtl 字典dtl
     * @return {@link DictionaryDtl }
     * <AUTHOR>
     * @create 2023-02-09
     */
    DictionaryDtl save(DictionaryDtl dictionaryDtl);

    /**
     * 删除
     *
     * @param dictionaryDtl 字典dtl
     * <AUTHOR>
     * @create 2023-02-09
     */
    void delete(DictionaryDtl dictionaryDtl) throws Exception;

    /**
     * 按字典编码查找字典详情列表
     *
     * @param dictCode 字典编码
     * @return {@link List }<{@link DictionaryDtl }>
     * <AUTHOR>
     * @create 2023-02-09
     */
    List<DictionaryDtl> findByDictCode(String dictCode);

    List<DictionaryDtl> findByDictId(String DictId);
}
