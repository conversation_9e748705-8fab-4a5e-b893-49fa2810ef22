package iet.ustb.sf.vo.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 周/月/日总结
 *
 * <AUTHOR>
 * @create 2023-01-04
 * @see BaseEntity
 */
@Data
@Entity
@Table(name = "WEEKLY_SUMMARY")
@ApiModel(value = "周/月/日总结")
public class WeeklySummary extends BaseEntity {

    /**
     * 周日期
     */
    @ApiModelProperty(value = "周日期")
    private String weekDate;
    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    private Integer module;
    /**
     * 本周/月/日总结
     */
    @Column(columnDefinition = "Varchar(2000)")
    @ApiModelProperty(value = "本周/月/日总结")
    private String currWeekSummary;
    /**
     * 下周计划
     */
    @Column(columnDefinition = "Varchar(2000)")
    @ApiModelProperty(value = "下周计划")
    private String nextWeekPlan;
    /**
     * 已上线功能
     */
    @Column(columnDefinition = "Varchar(2000)")
    @ApiModelProperty(value = "已上线功能")
    private String onlineFunction;
    /**
     * 未上线功能
     */
    @Column(columnDefinition = "Varchar(2000)")
    @ApiModelProperty(value = "未上线功能")
    private String notOnlineFunction;
    /**
     * 备注
     */
    @Column(columnDefinition = "Varchar(2000)")
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 类型：1-日总结 2-周总结 3-月总结
     */
    @ApiModelProperty(value = "类型：1-日总结 2-周总结 3-月总结")
    private Integer type;

    /**
     * 优先级：值越小，优先级越高
     */
    @ApiModelProperty(value = "优先级：值越小，优先级越高")
    private Integer priority;



    //    项目组负责人
    @Column(length = 64)
    private String projectTeamLeader;

    //  甲方负责人
    @Column(length = 64)
    private String sheetMaterialLeader;

    //  负责单位
    @Column(length = 64)
    private String responsibleUnit;

    //  是否完成
    @Column(length = 2)
    private String completed;

    //  未完成情况说明
    @Column(length = 5000)
    private String explanation;


    /**
     * 工作安排日期
     */
    private String  workArrangeDate;
    /**
     * 计划完成日期
     */
    private String plannCompleteDate;


}
