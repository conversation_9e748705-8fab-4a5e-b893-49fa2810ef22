package iet.ustb.sf.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.micrometer.core.instrument.util.StringUtils;
import io.netty.util.internal.StringUtil;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.jdom2.Element;
import org.json.XML;

import javax.xml.namespace.QName;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 调用主数据接口工具类
 */
@CommonsLog
public class XMLUtil {

    /**
     * 调用webservice接口进行封装 返回一个json数组格式的String
     *
     * @param wsdlUrl      wsdl地址
     * @param namespaceURI 命名空间
     * @param localPart    视图名即方法名
     * @return
     * @throws Exception
     */
    public static Object[] callXmlService(String wsdlUrl, String namespaceURI, String localPart, String InXml) {
        Object[] objects = new Object[0];
        try {
            //创建动态客户端
            JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();

            //调用wsdl地址
            Client client = dcf.createClient(wsdlUrl);
            QName qName = new QName(namespaceURI, localPart);
            objects = client.invoke(qName, InXml);
        } catch (Exception e) {
//            log.error("调用接口异常：" + e);
        }
        return objects;
    }

    /**
     * xml字符串转json
     *
     * @param xml
     * @return
     */
    public static String xml2JSON(String xml) {
        /*JSONObject obj = new JSONObject();
        try {
            InputStream is = new ByteArrayInputStream(xml.getBytes("utf-8"));
            SAXBuilder sb = new SAXBuilder();
            Document doc = sb.build(is);
            Element root = doc.getRootElement();
            obj.put(root.getName(), iterateElement(root));
            return obj.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }*/

        org.json.JSONObject xmlJSONObj;
        try {
            xmlJSONObj = XML.toJSONObject(xml);
            return xmlJSONObj.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return StringUtil.EMPTY_STRING;
    }

    /**
     * 遍历根元素下的子节点
     *
     * @param element
     * @return
     */
    private static Map iterateElement(Element element) {
        List jiedian = element.getChildren();
        Element et = null;
        Map obj = new HashMap();
        List list = null;
        for (int i = 0; i < jiedian.size(); i++) {
            list = new LinkedList();
            et = (Element) jiedian.get(i);
            if (et.getTextTrim().equals("")) {
                if (et.getChildren().size() == 0) {
                    continue;
                }
                //如果元素有子节点,则在原来的节点后面添加元素
                if (obj.containsKey(et.getName())) {
                    list = (List) obj.get(et.getName());
                }
                list.add(iterateElement(et));
                obj.put(et.getName(), list);
            } else {
                if (obj.containsKey(et.getName())) {
                    list = (List) obj.get(et.getName());
                }
                list.add(et.getTextTrim());
                obj.put(et.getName(), list);
            }
        }
        return obj;
    }

    /**
     * 处理从表数据(组织,岗位,级别)
     *
     * @param root
     * @param in
     * @param out
     * @return
     */
    public static String handleXml(String root, String orgNumber, String in, String out) {
        if (StringUtils.isNotEmpty(root)) {
            JSONArray json2 = JSONArray.parseArray(root);
            JSONObject json3 = json2.getJSONObject(0);
            JSONArray json4 = JSONArray.parseArray(json3.getString("O_CHILD"));
            for (int j = 0; j < json4.size(); j++) {
                JSONObject json5 = json4.getJSONObject(j);
                String orgCode = json5.getString(in).substring(2, json5.getString(in).length() - 2);
                if (orgCode.equals(orgNumber)) {
                    if (StringUtils.isNotEmpty(json5.getString(out))) {
                        return json5.getString(out).substring(2, json5.getString(out).length() - 2);
                    }
                    return json5.getString(out);
                }
            }
        }
        return "";
    }

    /**
     * 主数据参数
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static String getInXml(String startDate, String endDate) {
        if (StringUtils.isBlank(endDate)) endDate = "";

        String InXml = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n" +
                "<I_DATAS>  \n" +
                "\t<I_DATA>\n" +
                "\t\t<I_UDEF1>ERP</I_UDEF1> \n" +
                "\t\t<I_UDEF2>" + startDate + "</I_UDEF2> \n" +
                "\t\t<I_UDEF3>" + endDate + "</I_UDEF3> \n" +
                "\t\t<I_UDEF4></I_UDEF4>  \n" +
                "\t</I_DATA>\t\n" +
                "</I_DATAS>";
        return InXml;
    }

    /**
     * 处理xml参数
     *
     * @param objects
     * @return
     */
    public static String assemblyXml(Object[] objects) {
        String callJson = "";
        if (objects.length > 0) {
            String xml = objects[0].toString();
            //去除文件中的bom头
            xml = xml.substring(xml.indexOf(">", 2) + 1);
            //xml字符串转json字符串
            String jsonStr = xml2JSON(xml);

            //循环迭代json字符串
            JSONObject json = JSON.parseObject(jsonStr);
            String datas = json.getString("O_DATAS");
            if (StringUtils.isNotBlank(datas)) {
                JSONObject jsonObj = json.getJSONObject("O_DATAS");
                callJson = jsonObj.getString("O_DATA");
            }
        }
        return callJson;
    }
}
