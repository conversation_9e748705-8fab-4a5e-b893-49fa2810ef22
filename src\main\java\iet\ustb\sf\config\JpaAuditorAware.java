package iet.ustb.sf.config;

import iet.ustb.sf.vo.domain.User;
import iet.ustb.sf.utils.UserThreadUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

import java.util.Optional;

/**
 * JPA获取当前审计人, 供实体类@CreatedBy和@LastModifiedBy 自动保存当前登录人信息
 *
 * <AUTHOR>
 * @create 2022-06-17
 * @see AuditorAware
 */
@Configuration
@EnableJpaAuditing(auditorAwareRef = "jpaAuditorAware")
public class JpaAuditorAware implements AuditorAware<String> {

    @Override
    public Optional<String> getCurrentAuditor() {
        // TODO: 2022/6/17  新增/修改人先写死,后读取当前登录人工号
        User user = UserThreadUtil.getUser();
        String userNo = user != null ? user.getUserNo() : "023958";
        return Optional.of(userNo);
    }
}
