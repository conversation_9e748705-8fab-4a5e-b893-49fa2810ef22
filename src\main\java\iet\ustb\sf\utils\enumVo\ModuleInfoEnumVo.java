package iet.ustb.sf.utils.enumVo;

/**
 * @Author: Dr.Monster
 * @Title: WarningTypeEnumVo
 * @Date: 23/12/02 11:2041
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */

public enum ModuleInfoEnumVo {
    MODULE_INFO_ENUM_VO_1(1,"数据节点"),
    MODULE_INFO_ENUM_VO_2(2 , "工厂服务引擎"),
    MODULE_INFO_ENUM_VO_3(3 , "物料跟踪"),
    MODULE_INFO_ENUM_VO_4(4 , "智慧生产"),
    MODULE_INFO_ENUM_VO_5(5 , "智慧质量"),
    MODULE_INFO_ENUM_VO_6(6 , "智慧能源"),
    MODULE_INFO_ENUM_VO_7(7 , "智慧成本"),
    MODULE_INFO_ENUM_VO_8(8 , "智慧运维"),
    MODULE_INFO_ENUM_VO_9(9 , "协同运管"),
    MODULE_INFO_ENUM_VO_10(10 , "数字工厂"),
    MODULE_INFO_ENUM_VO_11(11 , "一炼钢设备监测"),
    MODULE_INFO_ENUM_VO_12(12 , "宽厚板设备监测"),
    MODULE_INFO_ENUM_VO_13(13 , "宽厚板振动监测"),
    MODULE_INFO_ENUM_VO_14(14 , "宽厚板集控"),
    MODULE_INFO_ENUM_VO_15(15 , "一炼钢集控");

    ModuleInfoEnumVo(int code, String name) {
        this.code = code;
        this.name = name;
    }

    int code;
    String name;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static int getCodeByName(String name){
       for(ModuleInfoEnumVo vo : ModuleInfoEnumVo.values()){
           if(vo.getName().equals(name)){
               return vo.getCode();
           }
       }
       return 0;
    }
}
