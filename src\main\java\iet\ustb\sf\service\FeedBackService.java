package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.FeedBack;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 问题反馈
 *
 * <AUTHOR>
 * @create 2022-10-26
 */
public interface FeedBackService {

    /**
     * 查找所有内容
     *
     * @return {@link List }<{@link FeedBack }>
     * <AUTHOR>
     * @create 2022-10-26
     */
    List<FeedBack> findAll();

    /**
     * 按多条件查找分页
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link FeedBack }>
     * <AUTHOR>
     * @create 2022-10-26
     */
    Page<FeedBack> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param feedback 反馈
     * @return {@link FeedBack }
     * <AUTHOR>
     * @create 2022-10-26
     */
    FeedBack save(FeedBack feedback);

    /**
     * 删除
     *
     * @param jsonObject json对象
     * <AUTHOR>
     * @create 2022-10-26
     */
    void delete(JSONObject jsonObject) throws Exception;

    /**
     * 按状态查找计数
     *
     * @param statusList 状态列表
     * @param startDate
     * @param endDate
     * @return {@link Map }<{@link String }, {@link Integer }>
     * <AUTHOR>
     * @create 2023-01-05
     */
    List<Map<String, Integer>> findCountByStatus(List<Integer> statusList, Date startDate, Date endDate);

    /**
     * 查找用户问题反馈
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-09-06
     */
    List<Map<String, Object>> findUserFeedBackNum(String startDate, String endDate);

    /**
     * 导出用户反馈统计Excel
     *
     * @param jsonObject json对象
     * @param response   回答
     * <AUTHOR>
     * @create 2023-09-06
     */
    void exportUserFeedBackExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException;

    /**
     * 查找厂处问题反馈数量
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2024-01-30
     */
    List<Map<String, Object>> findOneOrgFeedBackNum(JSONObject jsonObject);
}
