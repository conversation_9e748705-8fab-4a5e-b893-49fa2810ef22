package iet.ustb.sf.vo.domain;


import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 任务进度跟踪
 */
@Data
@Entity
@Table(name = "TASK_TRACKING")
public class TaskTracking extends BaseEntity {

    /**
     * 模块名称
     */
    @Column(length = 64, nullable = false)
    private String serviceName;

    /**
     * 任务名称
     */
    @Column(length = 500, nullable = false)
    private String taskName;

    /**
     * 任务类型：1-会议要求 2-工作计划
     */
    @Column(nullable = false)
    private Integer taskType;

    /**
     * 业务责任人
     */
    @Column(length = 128, nullable = false)
    private String serviceUser;

    /**
     * 开发责任人
     */
    @Column(length = 128, nullable = false)
    private String devUser;

    /**
     * 计划完成日期
     */
    @Column(length = 64, nullable = false)
    private String planCompleteDate;

    /**
     * 实际完成日期
     */
    @Column(length = 64)
    private String actualCompleteDate;

    /**
     * 超期天数
     */
    private Integer overdueDays;

    /**
     * 延期天数
     */
    private Integer delayDays;

    /**
     * 当前进展
     */
    private Integer currentProgress;

    /**
     * 状态：1-进行 2-完成 3-终止 4-延期未完成 5-延期完成 6-超期未完成 7-超期完成
     */
    @Column(nullable = false)
    private Integer status;

    /**
     * 周日期
     */
    @Column(length = 500)
    private String weekDate;

    /**
     * 本周进展
     */
    @Column(length = 5000)
    private String weekProgress;

    /**
     * 下周计划
     */
    @Column(length = 5000)
    private String nextWeekPlan;

    /**
     * 未完成原因
     */
    @Column(length = 5000)
    private String unfinishedReason;

    /**
     * 填报人
     */
    @Column(length = 500)
    private String weekSubmitUser;

    /**
     * 评分：10分-按期完成 5分-延期完成 3分-超期完成 0-超期未完成
     */
    private Integer score;
}
