# 使用JDK8 Alpine镜像作为基础镜像
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/openjdk:8-jdk-alpine3.9

# 设置维护者信息（可选）
LABEL maintainer="<EMAIL>"

# 设置Nacos服务器地址环境变量（默认值）
ENV NACOS_SERVER=127.0.0.1:8848

# 创建应用目录并设置工作目录
RUN mkdir -p /app
WORKDIR /app

# 复制JAR文件到容器中
COPY iet-resources-service-0.0.1.jar /app/iet-resources-service.jar

# 暴露应用端口（按实际需要修改）
EXPOSE 9701

# 启动应用时传入Nacos地址参数
ENTRYPOINT ["sh", "-c", "java -Dspring.cloud.nacos.config.server-addr=${NACOS_SERVER} -Dspring.cloud.nacos.discovery.server-addr=${NACOS_SERVER} -jar /app/iet-resources-service.jar"]