package iet.ustb.sf.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class RedisUtil {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public RedisUtil() {
    }

    public boolean expire(String key, long time) {
        try {
            if (time > 0L) {
                this.redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public long getExpire(String key) {
        return this.redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    public boolean hasKey(String key) {
        try {
            return this.redisTemplate.hasKey(key);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                this.redisTemplate.delete(key[0]);
            } else {
                this.redisTemplate.delete(Arrays.asList(key));
            }
        }

    }

    public Object get(String key) {
        return key == null ? null : this.redisTemplate.opsForValue().get(key);
    }

    public boolean set(String key, Object value) {
        try {
            this.redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0L) {
                this.redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                this.set(key, value);
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /** 存储 Hash */
    public void setHash(String key, String field, Object value) {
        redisTemplate.opsForHash().put(key, field, value);
    }

    /** 获取 Hash */
    public Object getHash(String key, String field) {
        return redisTemplate.opsForHash().get(key, field);
    }

    /** 获取整个 Hash */
    public Map<Object, Object> getAllHash(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /** 删除 Hash 中的某个字段 */
    public void deleteHashField(String key, String field) {
        redisTemplate.opsForHash().delete(key, field);
    }

    /**
     * 存储 List 到 Hash（转换成 JSON）
     * @param key
     * @param field
     * @param list
     */
    public void setHashList(String key, String field, List<?> list) {
        try {
            String json = objectMapper.writeValueAsString(list);
            redisTemplate.opsForHash().put(key, field, json);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    /**
     *  获取 Hash 中的 List（反序列化 JSON）
     * @param key
     * @param field
     * @param clazz
     * @return
     * @param <T>
     */
    public <T> List<T> getHashList(String key, String field, Class<T> clazz) {
        try {
            Object json = redisTemplate.opsForHash().get(key, field);
            if (json != null) {
                return objectMapper.readValue(json.toString(),
                        objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
