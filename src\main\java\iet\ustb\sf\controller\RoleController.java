package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.service.RoleService;
import iet.ustb.sf.utils.FileUtil;
import iet.ustb.sf.vo.AjaxJson;
import iet.ustb.sf.vo.domain.Role;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.channels.AsynchronousFileChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.controller
 * @title: DsRoleController
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/1010:50
 */
@RestController
@RequestMapping("/role")
@Api(value = "角色管理", tags = "角色管理")
public class RoleController {

    @Autowired
    RoleService roleService;


    //新增
    @ResponseBody
    @PostMapping("/doCreateRole")
    @ApiOperation(value = "新增", notes = "新增,{\n" +
            "            \"createdBy\": null,\n" +
            "            \"createdDate\": null,\n" +
            "            \"id\": \"000068d3-6417-4db9-80aa-e21333a52462\",\n" +
            "            \"isDef\": \"1\",\n" +
            "            \"lastModifiedBy\": null,\n" +
            "            \"lastModifiedDate\": null,\n" +
            "            \"new\": false,\n" +
            "            \"resourceList\": [],\n" +
            "            \"roleCode\": \"escaa\",\n" +
            "            \"roleName\": \"管理员\",\n" +
            "            \"status\": 1,\n" +
            "            \"roleType\": 1,\n" +
            "            \"orgID\": \"\",\n" +
            "            \"moduleName\": \"dsm\",\n" +
            "            \"type\": \"A\",\n" +
            "            \"userList\": []\n" +
            "        }", produces = "application/json")
    public AjaxJson doCreateRole(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(roleService.doCreateRole(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    //修改
    @ResponseBody
    @PostMapping("/doUpdateRole")
    @ApiOperation(value = "修改", notes = "修改,{\n" +
            "            \"createdBy\": null,\n" +
            "            \"createdDate\": null,\n" +
            "            \"id\": \"000068d3-6417-4db9-80aa-e21333a52462\",\n" +
            "            \"isDef\": \"1\",\n" +
            "            \"lastModifiedBy\": null,\n" +
            "            \"lastModifiedDate\": null,\n" +
            "            \"new\": false,\n" +
            "            \"resourceList\": [],\n" +
            "            \"roleCode\": \"escaa\",\n" +
            "            \"roleName\": \"管理员\",\n" +
            "            \"status\": 1,\n" +
            "            \"roleType\": 1,\n" +
            "            \"orgID\": \"\",\n" +
            "            \"moduleName\": \"dsm\",\n" +
            "            \"type\": \"A\",\n" +
            "            \"userList\": []\n" +
            "        }", produces = "application/json")
    public AjaxJson doUpdateRole(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(roleService.doUpdateRole(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/doDeleteRole")
    public AjaxJson doDeleteRole(@RequestBody Role role) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(roleService.doDeleteRole(role));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    //切换状态
    @ResponseBody
    @PostMapping("/doChangeStatus")
    @ApiOperation(value = "切换状态(0-禁用,1-启用)", notes = "切换状态(0-禁用,1-启用),{" +
            "\"id\":\"000068d3-6417-4db9-80aa-e21333a52462\"" +
            "\"status\":1" +
            "}", produces = "application/json")
    public AjaxJson doChangeStatus(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            roleService.doChangeStatus(jsonObject);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    //查询
    @ResponseBody
    @PostMapping("/findAllRole")
    @ApiOperation(value = "获取所有角色", notes = "获取所有角色,{\n" +
            "    \"roleCode\": \"escaa\",\n" +
            "    \"roleName\": \"\",\n" +
            "    \"roleType\":1,\n" +
            "    \"orgID\":\"\",\n" +
            "    \"moduleCode\":\"1\",\n" +
            "    \"productionLineName\": [\n" +
            "        \"宽厚板厂\",\n" +
            "        \"第一炼钢厂\"\n" +
            "    ]," +
            "    \"pageIndex\": 1,\n" +
            "    \"pageSize\": 10\n" +
            "}", produces = "application/json")
    public AjaxJson findAllRole(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(roleService.findAllRole(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/findAllRoleNoPage")
    @ApiOperation(value = "获取所有角色无分页", notes = "获取所有角色无分页,{}", produces = "application/json")
    public AjaxJson findAllRoleNoPage(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(roleService.findAllRoleNoPage(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/findRoleByUserID")
    @ApiOperation(value = "根据用户查询角色", notes = "查询用户对应角色,{" +
            "\"userID\":\"000068d3-6417-4db9-80aa-e21333a52462\"" +
            "}", produces = "application/json")
    public AjaxJson findRoleByUserID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(roleService.findRoleByUserID(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/findRoleByRscID")
    @ApiOperation(value = "根据资源查询角色", notes = "查询用户对应角色,{" +
            "\"rscID\":\"1dcb076f-0698-413d-ba1c-549bce08abbb\"" +
            "}", produces = "application/json")
    public AjaxJson findRoleByRscID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(roleService.findRoleByRscID(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/findOneRoleByID")
    @ApiOperation(value = "查询角色", notes = "查询角色,{\"id\":\"000068d3-6417-4db9-80aa-e21333a52462\"}", produces = "application/json")
    public AjaxJson findOneRoleByID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(roleService.findOneRoleByID(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/relateResource")
    @ApiOperation(value = "关联资源", notes = "关联资源,{\n" +
            "    \"id\":\"60139d1e-7692-4719-bd7f-ec14fee831dx\",\n" +
            "    \"resourceIDs\":[\n" +
            "        \"111\",\"222\",\"333\"\n" +
            "    ]\n" +
            "}", produces = "application/json")
    public AjaxJson relateResource(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(roleService.relateResource(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/relateUser")
    @ApiOperation(value = "关联人员", notes = "关联人员,{\n" +
            "    \"id\":\"60139d1e-7692-4719-bd7f-ec14fee831dx\",\n" +
            "    \"userIDs\":[\n" +
            "        \"111\",\"222\",\"333\"\n" +
            "    ]\n" +
            "}", produces = "application/json")
    public AjaxJson relateUser(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(roleService.relateUser(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/exchangeRoleRelation")
    @ApiOperation(value = "角色互换", notes = "角色互换,{\n" +
            "    \"originUserID\": \"0000bce3-accb-4774-8cc8-1a9b93201184\",\n" +
            "    \"exchangeUserID\": \"00083511-21d8-4014-bb4c-acd68ce76847\"\n" +
            "}", produces = "application/json")
    public AjaxJson exchangeRole(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            roleService.exchangeRole(jsonObject);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }



    @PostMapping(value = "/importRoles", headers = "content-type=multipart/form-data")
    @ResponseBody
    @ApiOperation(value = "导入角色", notes = "导入角色", produces = "application/json")
    public AjaxJson importRoles(@RequestPart("file") MultipartFile multipartFile) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            File file = FileUtil.multipartFileToFile(multipartFile);
            roleService.importRoles(0, file);
            FileUtil.delteTempFile(file);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

}
