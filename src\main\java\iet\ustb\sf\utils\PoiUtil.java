package iet.ustb.sf.utils;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * word及excel解析
 */
public class PoiUtil {


//    public static String readWordFile(String path) {
//        String buffer = "";
//        try {
//            if (path.endsWith(".doc")) {
//                InputStream is = new FileInputStream(new File(path));
//                WordExtractor ex = new WordExtractor(is);
//                buffer = ex.getText();
//                ex.close();
//            } else if (path.endsWith("docx")) {
//                OPCPackage opcPackage = POIXMLDocument.openPackage(path);
//                POIXMLTextExtractor extractor = new XWPFWordExtractor(opcPackage);
//                buffer = extractor.getText();
//                extractor.close();
//            } else {
//                System.out.println("此文件不是word文件！");
//            }
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return buffer;
//    }

//    public static Map<String, String[][]> readExcelFileByPath(String fileAbsolutePath) {
//        InputStream inputStream = null;
//        try {
//            inputStream = new FileInputStream(new File(fileAbsolutePath));
//            XSSFWorkbook book = new XSSFWorkbook(inputStream);
//            Map<String, String[][]> stringMap = new HashMap<>(book.getNumberOfSheets() + 1);
//            for (int sheetIndex = 0; sheetIndex < book.getNumberOfSheets(); sheetIndex++) {
//                XSSFSheet sheet = book.getSheetAt(sheetIndex);
//                int rowNum = sheet.getLastRowNum() + 1;
//                int coloumNum = 0;
//                int index = 0;
//                while (true) {
//                    if (!ToolsUtil.isEmpty(sheet.getRow(index))) {
//                        coloumNum = sheet.getRow(index).getPhysicalNumberOfCells();
//                        break;
//                    }
//                    index++;
//                }
//                String[][] contents = new String[rowNum][coloumNum];
//                for (int j = 0; j < rowNum; j++) {
//                    XSSFRow row = sheet.getRow(j);
//                    if (row != null) {
//                        for (int k = 0; k < row.getLastCellNum(); k++) {
//                            contents[j][k] = getXCellFormatValue(row.getCell(k));
//                        }
//                    }
//                }
//                stringMap.put("sheet_" + sheetIndex, contents);
//            }
//            return stringMap;
//        } catch (FileNotFoundException fe) {
//        } catch (IOException ie) {
//        } finally {
//            if (inputStream != null) {
//                try {
//                    inputStream.close();
//                } catch (Exception e) {
//                }
//            }
//        }
//        return null;
//    }


//    public static Map<String, String[][]> readExcelFile(File file) {
//        InputStream inputStream = null;
//        try {
//            inputStream = new FileInputStream(file);
//            XSSFWorkbook book = new XSSFWorkbook(inputStream);
//            Map<String, String[][]> stringMap = new HashMap<>(book.getNumberOfSheets() + 1);
//            for (int sheetIndex = 0; sheetIndex < book.getNumberOfSheets(); sheetIndex++) {
//                XSSFSheet sheet = book.getSheetAt(sheetIndex);
//                int rowNum = sheet.getLastRowNum() + 1;
//                int coloumNum = 0;
//                int index = 0;
//                while (true) {
//                    if (!ToolsUtil.isEmpty(sheet.getRow(index)) && sheet.getRow(index).getLastCellNum() != -1) {
//                        if (!checkCellAllEmpty(sheet.getRow(index), sheet.getRow(index).getLastCellNum())) {
//                            coloumNum = sheet.getRow(index).getPhysicalNumberOfCells() + index;
//                            break;
//                        }
//                    }
//                    index++;
//                }
//                String[][] contents = new String[rowNum][coloumNum];
//                for (int j = 0; j < rowNum; j++) {
//                    XSSFRow row = sheet.getRow(j);
//                    if (row != null) {
//                        for (int k = 0; k < row.getLastCellNum(); k++) {
//                            contents[j][k] = getXCellFormatValue(row.getCell(k));
//                        }
//                    }
//                }
//                stringMap.put("sheet_" + sheetIndex, contents);
//            }
//            return stringMap;
//        } catch (FileNotFoundException fe) {
//        } catch (IOException ie) {
//        } finally {
//            if (inputStream != null) {
//                try {
//                    inputStream.close();
//                } catch (Exception e) {
//                }
//            }
//        }
//        return null;
//    }


    public static Map<String, String[][]> readExcelFile_1(File file) {
        FileInputStream fis = null;
        Map<String, String[][]> stringMap = new HashMap<>();
        try {
            fis = new FileInputStream(file);
            Workbook workbook = new XSSFWorkbook(fis);

            int numberOfSheets = workbook.getNumberOfSheets();
            for (int i = 0; i < numberOfSheets; i++) {
                Sheet sheet = workbook.getSheetAt(i);
                int numberOfRows = sheet.getPhysicalNumberOfRows();
                String[][] contents = new String[numberOfRows][7];
                for (int j = 1; j < numberOfRows; j++) {
                    Row row = sheet.getRow(j);
                    if(!ToolsUtil.isEmpty(row)){
                        for (int k = 1; k < 7; k++) {
                            Cell cell = row.getCell(k);
                            if(ToolsUtil.isEmpty(cell)){
                                continue;
                            }
                            contents[j][k] = cell.getStringCellValue();
                        }
                    }
                }
                stringMap.put(sheet.getSheetName(), contents);
            }
            workbook.close();
            fis.close();
            return stringMap;
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }



    //检查改列所有单元格是否为空，都是空，返回true，不是返回false
    public static boolean checkCellAllEmpty(XSSFRow row, int cols) {
        int checkFlag = 0;
        for (int i = 0; i < cols; i++) {
            if (!ToolsUtil.isEmpty(row.getCell(i)) && !ToolsUtil.isEmpty(row.getCell(i).getRawValue())) {
            } else {
                checkFlag++;
            }
        }
        return checkFlag == cols;
    }

//    private static String getXCellFormatValue(XSSFCell cell) {
//        String cellValue = "";
//        if (null != cell) {
//            switch (String.valueOf(cell.getCellType().getCode())) {
//                case "1":
//                    cellValue = cell.getRawValue();
//                    break;
//                case "0":
//                    cellValue = cell.getNumericCellValue() + "";
//                    break;
//                default:
//                    cellValue = "";
//            }
////            cellValue = cell.getRichStringCellValue().getString();
//        } else {
//            cellValue = "";
//        }
//        return cellValue;
//    }

}
