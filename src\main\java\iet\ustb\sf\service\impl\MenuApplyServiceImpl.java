package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.MenuApplyDao;
import iet.ustb.sf.dao.UserDao;
import iet.ustb.sf.vo.domain.MenuApply;
import iet.ustb.sf.vo.domain.User;
import iet.ustb.sf.service.MenuApplyService;
import iet.ustb.sf.utils.ToolsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.List;

@Service
public class MenuApplyServiceImpl implements MenuApplyService {

    @Autowired
    MenuApplyDao menuApplyDao;

    @Autowired
    UserDao userDao;

    @Override
    public String save(JSONObject jsonObject) {
        MenuApply menuApply = ToolsUtil.jsonObjectToEntity(jsonObject , MenuApply.class);

        if(!ToolsUtil.isEmpty(menuApply.getCreateUserNo())){
            User user = userDao.findOneUserByUserNo(menuApply.getCreateUserNo());
            menuApply.setCreateUserName(user.getUserName());
        }

        return menuApplyDao.save(menuApply).getId();
    }

    @Override
    public String update(JSONObject jsonObject) {
        MenuApply menuApply = ToolsUtil.jsonObjectToEntity(jsonObject , MenuApply.class);

        if(!ToolsUtil.isEmpty(menuApply.getCreateUserNo())){
            User user = userDao.findOneUserByUserNo(menuApply.getCreateUserNo());
            menuApply.setCreateUserName(user.getUserName());
        }

        return menuApplyDao.save(menuApply).getId();
    }

    @Override
    public Page<MenuApply> find(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<MenuApply> page = menuApplyDao.findAll(new Specification<MenuApply>() {
            @Override
            public Predicate toPredicate(Root<MenuApply> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<Predicate>();
                Predicate[] arr = new Predicate[list.size()];

                String userNo = jsonObject.getString("userNo");
                String userName = jsonObject.getString("userName");
                Integer status = jsonObject.getInteger("status");


                String startTime = jsonObject.getString("startTime");
                String endTime = jsonObject.getString("endTime");

                if (!ToolsUtil.isEmpty(userNo)) {
                    list.add(cb.equal(root.get("userNo"), userNo));
                }

                if (!ToolsUtil.isEmpty(status)) {
                    list.add(cb.equal(root.get("status"), status));
                }else {
                    list.add(cb.notEqual(root.get("status"), -1));
                }

                if (!ToolsUtil.isEmpty(userName)) {
                    list.add(cb.like(root.get("userName"), "%" + userName + "%"));
                }

                if (!ToolsUtil.isEmpty(startTime)) {
                    list.add(cb.greaterThanOrEqualTo(root.get("createDateTime").as(String.class), startTime + ":00"));
                }
                if (!ToolsUtil.isEmpty(endTime)) {
                    list.add(cb.lessThanOrEqualTo(root.get("createDateTime").as(String.class), endTime + ":59"));
                }

                cq.where(list.toArray(arr));
                cq.orderBy(cb.desc(root.get("createDateTime")));
                return null;
            }
        } , pageable);

        return page;
    }

    @Override
    public String delete(JSONObject jsonObject) {
        String id = jsonObject.getString("id");
        MenuApply menuApply = menuApplyDao.getById(id);
        menuApply.setStatus(-1);
        return menuApplyDao.save(menuApply).getId();
    }
}
