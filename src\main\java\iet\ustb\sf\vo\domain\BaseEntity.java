package iet.ustb.sf.vo.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 实体类父类-序列化
 * @create 2022/6/17
 **/
@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(length = 64)
    @GenericGenerator(name = "def-uuid", strategy = "iet.ustb.sf.config.CustomUUIDGenerator")
    @GeneratedValue(generator = "def-uuid")
    protected String id;

    /**
     * 创建人工号
     */
    @CreatedBy
    @Column(length = 32, updatable = false)
    protected String createUserNo;

    /**
     * 创建日期
     */
    @CreatedDate
    @Column(updatable = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(shape= JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    protected Date createDateTime;

    /**
     * 修改人工号
     */
    @LastModifiedBy
    @Column(length = 32, insertable = false)
    protected String updateUserNo;

    /**
     * 修改日期
     */
    @LastModifiedDate
    @Column(insertable = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(shape= JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    protected Date updateDateTime;

    //第几页
    @Transient
    protected  Integer pageIndex;

    //每页大小
    @Transient
    protected  Integer pageSize;
}
