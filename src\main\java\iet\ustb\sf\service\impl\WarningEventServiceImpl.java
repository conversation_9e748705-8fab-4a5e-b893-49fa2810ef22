package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.*;
import iet.ustb.sf.service.DictionaryDtlService;
import iet.ustb.sf.service.RoleService;
import iet.ustb.sf.service.UserService;
import iet.ustb.sf.service.WarningEventService;
import iet.ustb.sf.utils.DateUtils;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.vo.ServiceVo;
import iet.ustb.sf.vo.domain.*;
import iet.ustb.sf.webSocket.WebSocket;
import io.micrometer.core.instrument.util.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: Dr.Monster
 * @Slogan: The never-compromising angry youth.
 * @PackageName: iet.ustb.sf.service.impl
 * @Date: 2022/08/10/14:18
 * @Description:
 */
@Service
public class WarningEventServiceImpl implements WarningEventService {

    @Autowired
    WarningEventDao warningEventDao;

    @Autowired
    WarningEventBodyDao warningEventBodyDao;

    @Autowired
    WarningInfoDao warningInfoDao;

    @Autowired
    WebSocket webSocket;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    DictionaryDtlService dictionaryDtlService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAlertInfo(JSONObject jsonObject) {
        if (!ToolsUtil.isEmpty(jsonObject)) {
            WarningEvent warningEvent = ToolsUtil.jsonObjectToEntity(jsonObject, WarningEvent.class);
            String serviceName = warningEvent.getServiceName();
            String userNo = warningEvent.getUserNo();
            if(!webSocket.checkUserIsOnline(serviceName , userNo)){
                warningEvent.setIsPush("0");
                warningEvent.setIsOffline("1");
            }else{
                warningEvent.setIsPush("1");
                warningEvent.setIsOffline("0");
            }
            WarningEventBody warningEventBody = warningEvent.getWarningEventBody();
            warningEventBodyDao.save(warningEventBody);
            warningEventDao.save(warningEvent).getId();
            String message = "时间：" + warningEvent.getCreateDateTime() + " 报警信息：" + warningEvent.getWarningEventBody().getTitle()
                    + " 报警等级：" + warningEvent.getAlertLevel() + " 报警类型：" + warningEvent.getAlertType();
            webSocket.setMessageToRoles(warningEvent.getServiceName(), warningEvent.getWarningEventBody().getPushRoleIDs(), message);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAlertInfo(JSONObject jsonObject) {
        if (!ToolsUtil.isEmpty(jsonObject) && !ToolsUtil.isEmpty(jsonObject.get("msgID"))) {
            String msgID = jsonObject.getString("msgID");
            //额外用户
            String userNos = jsonObject.getString("userNos");
            List<String> userList = new ArrayList<>();
            if(!ToolsUtil.isEmpty(userNos)){
                userList = Arrays.asList(userNos.split(","));
            }
            List<WarningEvent> warningEvents = warningEventDao.findAlertsByMsgID(msgID);
            if(ToolsUtil.isEmpty(warningEvents)){
                return;
            }
            String serviceName = warningEvents.get(0).getServiceName();

            for(WarningEvent warningEvent : warningEvents){
                String userNo = warningEvent.getUserNo();
                if(!webSocket.checkUserIsOnline(serviceName , userNo)){
                    warningEvent.setIsPush("0");
                    warningEvent.setIsOffline("1");
                }else{
                    warningEvent.setIsPush("1");
                    warningEvent.setIsOffline("0");
                }
                warningEvent.setStatus("1");
                WarningEventBody warningEventBody = warningEvent.getWarningEventBody();
                warningEventBodyDao.save(warningEventBody);
                warningEventDao.save(warningEvent).getId();
            }

            String message = "报警问题已解决";
            if(!ToolsUtil.isEmpty(warningEvents.get(0).getWarningEventBody().getPushRoleIDs())){
                webSocket.notifyRoles(serviceName, warningEvents.get(0).getWarningEventBody().getPushRoleIDs(), message);
            }
            if(!ToolsUtil.isEmpty(userList)){
                webSocket.notifyUser(serviceName , userList , message);
            }
        }
    }

    @Override
    public List<WarningEvent> findAlertsByServiceName(JSONObject jsonObject) {
        if (!ToolsUtil.isEmpty(jsonObject) && !ToolsUtil.isEmpty(jsonObject.getString("serviceName"))) {
            String serviceName = jsonObject.getString("serviceName");
            return warningEventDao.findAlertsByServiceName(serviceName);
        }
        return new ArrayList<>();
    }


    @Override
    public List<WarningEvent> findAlertsByAlertTypeAndServiceName(JSONObject jsonObject) {
        if (!ToolsUtil.isEmpty(jsonObject) &&
                !ToolsUtil.isEmpty(jsonObject.getString("serviceName")) &&
                !ToolsUtil.isEmpty(jsonObject.getString("alertType"))) {
            String serviceName = jsonObject.getString("serviceName");
            String alertType = jsonObject.getString("alertType");
            return warningEventDao.findAlertsByAlertTypeAndServiceName(alertType, serviceName);
        }
        return new ArrayList<>();
    }

    @Override
    public List<WarningEvent> findAlertsByAlertLevelAndServiceName(JSONObject jsonObject) {
        if (!ToolsUtil.isEmpty(jsonObject) &&
                !ToolsUtil.isEmpty(jsonObject.getString("serviceName")) &&
                !ToolsUtil.isEmpty(jsonObject.getString("alertLevel"))) {
            String serviceName = jsonObject.getString("serviceName");
            String alertLevel = jsonObject.getString("alertLevel");
            return warningEventDao.findAlertsByAlertLevelAndServiceName(alertLevel, serviceName);
        }
        return new ArrayList<>();
    }


    @Autowired
    RoleDao roleDao;

    @Override
    public Page<WarningEvent> findAlertInfoByMultipleChoices(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<WarningEvent> rscPage = warningEventDao.findAll(new Specification<WarningEvent>() {
            @Override
            public Predicate toPredicate(Root<WarningEvent> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<Predicate>();
                Predicate[] arr = new Predicate[list.size()];

                if(!ToolsUtil.isEmpty(jsonObject.get("alertLevel"))){
                    list.add(cb.equal(root.get("alertLevel"), jsonObject.get("alertLevel")));
                }
                if(!ToolsUtil.isEmpty(jsonObject.get("alertType"))){
                    list.add(cb.equal(root.get("alertType"), jsonObject.get("alertType")));
                }
                if(!ToolsUtil.isEmpty(jsonObject.get("serviceName"))){
                    list.add(cb.equal(root.get("serviceName"), jsonObject.get("serviceName")));
                }
                if(!ToolsUtil.isEmpty(jsonObject.get("userNo"))){
                    list.add(cb.equal(root.get("userNo"), jsonObject.get("userNo")));

                    List<Role> roles = roleDao.findRoleByUserID(jsonObject.getString("userNo"));
                    if(!ToolsUtil.isEmpty(roles)){

                    }
                }
                if(!ToolsUtil.isEmpty(jsonObject.get("isOffline"))){
                    list.add(cb.equal(root.get("isOffline"), jsonObject.get("isOffline")));
                }
                if(!ToolsUtil.isEmpty(jsonObject.get("isPush"))){
                    list.add(cb.equal(root.get("isPush"), jsonObject.get("isPush")));
                }
                if(!ToolsUtil.isEmpty(jsonObject.get("status"))){
                    list.add(cb.equal(root.get("status"), jsonObject.get("status")));
                }
                if(!ToolsUtil.isEmpty(jsonObject.get("waringPlant"))){
                    list.add(cb.equal(root.get("waringPlant"), jsonObject.get("waringPlant")));
                }
                if(!ToolsUtil.isEmpty(jsonObject.get("areaName"))){
                    list.add(cb.equal(root.get("areaName"), jsonObject.get("areaName")));
                }
                if(!ToolsUtil.isEmpty(jsonObject.get("pointName"))){
                    list.add(cb.equal(root.get("pointName"), jsonObject.get("pointName")));
                }
                if(!ToolsUtil.isEmpty(jsonObject.get("deviceName"))){
                    list.add(cb.equal(root.get("deviceName"), jsonObject.get("deviceName")));
                }
                if(!ToolsUtil.isEmpty(jsonObject.get("warningType"))){
                    list.add(cb.equal(root.get("warningType"), jsonObject.get("warningType")));
                }

                String startTime = jsonObject.getString("startTime");
                String endTime = jsonObject.getString("endTime");

                if (!ToolsUtil.isEmpty(startTime)) {
                    list.add(cb.greaterThanOrEqualTo(root.get("createDateTime").as(String.class), startTime + ":00"));
                }
                if (StringUtils.isNotBlank(endTime)) {
                    list.add(cb.lessThanOrEqualTo(root.get("createDateTime").as(String.class), endTime + ":59"));
                }

                cq.where(list.toArray(arr));
                cq.orderBy(cb.desc(root.get("createDateTime")));
                return null;
            }
        }, pageable);
        matchServiceName(rscPage);
        return rscPage;
    }


    public void matchServiceName(Page<WarningEvent> page){
        List<ServiceVo> serviceNameList = getServiceList();
        page.getContent().stream().filter(x -> !ToolsUtil.isEmpty(x.getServiceName())).map(x -> {
            for(ServiceVo item : serviceNameList){
                if(item.getName().toLowerCase().equals(x.getServiceName().toLowerCase())){
                    x.setServiceNameCN(item.getCName());
                }else{
                    continue;
                }
            }
            return x;
        }).collect(Collectors.toList());
    }

    @Override
    public void deleteAlert() {

    }

    @Override
    public void saveOfflineMessage(JSONObject jsonObject) {
        if (!ToolsUtil.isEmpty(jsonObject) && !ToolsUtil.isEmpty(jsonObject.get("userNo"))
                && !ToolsUtil.isEmpty(jsonObject.get("warningEvent"))) {
            WarningEvent warningEvent = ToolsUtil.jsonObjectToEntity(jsonObject.get("warningEvent"), WarningEvent.class);
            warningEvent.setIsOffline("1");
            warningEvent.setIsPush("0");
        } else {
            return;
        }
    }

    @Override
    public List<WarningEvent> getOfflineWaringEvent(JSONObject jsonObject) {
        if (!ToolsUtil.isEmpty(jsonObject) && !ToolsUtil.isEmpty(jsonObject.get("userNo"))
                && !ToolsUtil.isEmpty(jsonObject.get("serviceName"))) {
            String userNo = jsonObject.getString("userNo");
            String serviceName = jsonObject.getString("serviceName");
            return warningEventDao.getOfflineWaringEvent(userNo, serviceName);
        }
        return new ArrayList<>();
    }

    @Autowired
    UserService userService;

    @Autowired
    RoleService roleService;

    @Override
    public Map<String, Object> getUndoneRateByUserID(JSONObject jsonObject) {

        if(ToolsUtil.isEmpty(jsonObject.get("userNo"))){
            return null;
        }

        User user = userService.findUserByUserNo(jsonObject.getString("userNo"));
        if(ToolsUtil.isEmpty(user)){
            return null;
        }

        if(ToolsUtil.isEmpty(user.getId())){
            return null;
        }

        List<String> roleIDs = roleService.findRoleIDsByUserID(user.getId());

        int sumOfDone = 0;
        int sumOfUndone = 0;
        int sumOfTotal = 0;

        Map<String , Object> tempResult;

        Date date = new Date();
        String dateDay = DateUtils.formatDate(date, "yyyy-MM-dd");
        try {
            Date startTime = DateUtils.parseDate(dateDay + " 00:00:00" , "yyyy-MM-dd HH:mm:ss");
            Date endTime = DateUtils.parseDate(dateDay + " 23:59:59" , "yyyy-MM-dd HH:mm:ss");
            for(String roleID : roleIDs){
                tempResult = new HashMap<>();
                tempResult = warningEventDao.getUndoneRateByRoleID(roleID , startTime , endTime);
//                tempResult = getUndoneRateByRoleID(roleID , startTime , endTime);
                if(ToolsUtil.isEmpty(tempResult.get("DONE"))){
                    sumOfDone += 0;
                }else {
                    sumOfDone += ((BigDecimal)tempResult.get("DONE")).intValue();
                }
                if(ToolsUtil.isEmpty(tempResult.get("UNDONE"))){
                    sumOfUndone += 0;
                }else {
                    sumOfUndone += ((BigDecimal)tempResult.get("UNDONE")).intValue();
                }
                if(ToolsUtil.isEmpty(tempResult.get("TOTAL"))){
                    sumOfTotal += 0;
                }else {
                    sumOfTotal += ((BigInteger)tempResult.get("TOTAL")).intValue();
                }
            }

            Map<String , Object> result = new HashMap<>();

            result.put("done" , sumOfDone);
            result.put("unDone" , sumOfUndone);
            result.put("total" , sumOfTotal);
            return result;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public List<Map<String , String>> findModuleInfoList() {
        return warningEventDao.findModuleInfoList();
    }

    @Override
    public List<String> findAreaNameList() {
        return warningEventDao.findAreaNameList();
    }

    @Override
    public List<String> findDeviceNameList() {
        return warningEventDao.findDeviceNameList();
    }

    @Override
    public List<String> findPointNameList() {
        return warningEventDao.findPointNameList();
    }


    Map<String , Object> getUndoneRateByRoleID(String roleID , Date createTime , Date endTime){
        String sql = "select sum(case when we.status = 1 then 1 else 0 end) as done ,\n" +
                "       sum(case when we.status = 0 then 1 else 0 end) as unDone ,\n" +
                "       count(distinct we.id) total\n" +
                "from warning_event we\n" +
                "         join warning_event_body web on we.warningeventbody_id = web.id\n" +
                "where web.pushroleids like '%" + roleID +"%'\n" +
                "  and web.createdatetime between '" + createTime + "' and '" + endTime + "'";
        Session session = entityManager.unwrap(Session.class);
        SQLQuery dataSqlQuery = session.createSQLQuery(sql);
        dataSqlQuery.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String , Object>> mapList = dataSqlQuery.list();
        if(ToolsUtil.isEmpty(mapList)){
            return null;
        }
        return  mapList.get(0);
    }

    public List<ServiceVo> getServiceList(){
        String SERVICE_STR2 = "[";
        List<DictionaryDtl> list = dictionaryDtlService.findByDictId("e38d0f03-3755-42ba-a17a-d078f01cfab3");
        for (DictionaryDtl di :
                list) {
            SERVICE_STR2 = SERVICE_STR2+ ","+di.getValue()   ;
        }
        SERVICE_STR2 = SERVICE_STR2 + "]";
        return JSONArray.parseArray(SERVICE_STR2.replaceFirst(",", ""), ServiceVo.class);
    }


    @Test
    public void test1(){
        WarningEventBody body = new WarningEventBody();
        WarningEvent event = new WarningEvent();

        body.setTitle("bodyTitle");
        body.setMsg("bodyMsg");
        body.setTreatment("bodyTreatment");
        body.setDetailInfo("bodyDetailInfo");
        body.setAlertUrl("bodyAlertUrl");
        body.setPushRoleIDs("1,2");
        body.setOtherInfo("bodyOtherInfo");
        body.setRemark("bodyRemark");

        event.setMsgID("msgID");
        event.setIsOffline("1");
        event.setIsPush("0");
        event.setUserNo("007132");
        event.setWaringPlant("C2");
        event.setServiceName("rsc");
        event.setAlertType("info");
        event.setAlertLevel("2");
        event.setWarningEventBody(body);
        event.setStatus("0");
        System.out.println(JSON.toJSONString(event));
    }

}
