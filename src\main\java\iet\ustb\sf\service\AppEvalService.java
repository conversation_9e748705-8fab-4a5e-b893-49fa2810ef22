package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.AppEval;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 应用评价
 *
 * <AUTHOR>
 * @create 2023-12-14
 */
public interface AppEvalService {

    /**
     * 查找所有
     *
     * @return {@link List }<{@link AppEval }>
     * <AUTHOR>
     * @create 2023-12-14
     */
    List<AppEval> findAll();

    /**
     * 按多条件查找全部
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link AppEval }>
     * <AUTHOR>
     * @create 2023-12-14
     */
    List<AppEval> findAllByMultiCondition(JSONObject jsonObject);

    /**
     * 按多条件查找
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link AppEval }>
     * <AUTHOR>
     * @create 2023-12-14
     */
    Page<AppEval> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 查找周应用评估
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2024-01-03
     */
    Page<Map<String, Object>> findWeekAppEval(JSONObject jsonObject);

    /**
     * 按服务和模型分组查找
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2024-02-04
     */
    List<Map<String, Object>> findGroupByServerAndModel(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param appEval 应用评价
     * <AUTHOR>
     * @create 2023-12-14
     */
    void save(AppEval appEval);

    /**
     * 批量保存
     *
     * @param appEvals 应用程序评估
     * <AUTHOR>
     * @create 2024-01-03
     */
    void saveAll(List<AppEval> appEvals);

    /**
     * 删除
     *
     * @param appEval
     * <AUTHOR>
     * @create 2023-12-14
     */
    void delete(AppEval appEval) throws Exception;

    /**
     * 按多个键删除
     *
     * @param jsonObject json对象
     * <AUTHOR>
     * @create 2024-01-03
     */
    void deleteByMultiKey(JSONObject jsonObject) throws Exception;

    /**
     * 导出Pdf
     *
     * @param jsonObject json对象
     * @param request    要求
     * @param response   回答
     * @return
     * <AUTHOR>
     * @create 2024-01-05
     */
    String exportPdf(JSONObject jsonObject, HttpServletRequest request, HttpServletResponse response);

    /**
     * 导出excel
     * @param jsonObject json对象
     * @param response   回答
     * @Author: 023958 茆荣伟
     * @Create: 2024-03-28 08:56
     */
    void exportExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException;
}
