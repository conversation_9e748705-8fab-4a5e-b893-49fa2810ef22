package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.DictionaryDao;
import iet.ustb.sf.mapper.DictionaryDtlMapper;
import iet.ustb.sf.utils.RedisUtil;
import iet.ustb.sf.utils.constant.Constant;
import iet.ustb.sf.vo.domain.BasicDataConfig;
import iet.ustb.sf.vo.domain.Dictionary;
import iet.ustb.sf.service.DictionaryService;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.vo.domain.DictionaryDtl;
import io.micrometer.core.instrument.util.StringUtils;
import org.apache.tomcat.util.bcel.Const;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据字典impl
 *
 * <AUTHOR>
 * @create 2023-02-09
 * @see DictionaryService
 */
@Service
@Transactional
public class DictionaryServiceImpl implements DictionaryService {

    @Autowired
    private DictionaryDao dictionaryDao;

    @Autowired
    private DictionaryDtlMapper dictionaryDtlMapper;

    @Override
    public List<Dictionary> findAll() {
        return dictionaryDao.findAll();
    }

    @Override
    public Page<Dictionary> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<Dictionary> dictionaryPage = dictionaryDao.findAll(createSpecs(jsonObject), pageable);
        return dictionaryPage;
    }

    private Specification<Dictionary> createSpecs(JSONObject json) {
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            String code = json.getString("code");// 编码
            String name = json.getString("name");// 名称

            if (StringUtils.isNotBlank(code)) {
                list.add(cb.like(root.get("code"), "%" + code + "%"));
            }
            if (StringUtils.isNotBlank(name)) {
                list.add(cb.like(root.get("name"), "%" + name + "%"));
            }
            query.where(list.toArray(new Predicate[list.size()]));
            query.orderBy(cb.desc(root.get("createDateTime")));
            return query.getRestriction();
        };
    }

    @Override
    public Dictionary save(Dictionary dictionary) {

        return dictionaryDao.save(dictionary);
    }

    @Override
    public void delete(Dictionary dictionary) throws Exception {
        dictionaryDao.delete(dictionary);

        //级联删除数据字典子表数据
        DictionaryDtl dictionaryDtl = new DictionaryDtl();
        dictionaryDtl.setDictId(dictionary.getId());
        dictionaryDtlMapper.deleteByDicInfo(dictionaryDtl);
    }
}
