package iet.ustb.sf.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import iet.ustb.sf.vo.domain.IconImg;
import org.apache.ibatis.annotations.Param;
import iet.ustb.sf.vo.PortMonitoringInfo;

/**
 * 接口监控(PortMonitoringInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-21 17:05:44
 */
public interface PortMonitoringInfoMapper extends BaseMapper<PortMonitoringInfo> {

    /**
     * 获取图标信息
     * @param portMonitoringInfo
     * @return
     */
    IPage<PortMonitoringInfo> getPortMonitoringInfoByPage(Page<PortMonitoringInfo> page, @Param("portMonitoringInfo") PortMonitoringInfo portMonitoringInfo);

    PortMonitoringInfo getRequestData(PortMonitoringInfo portMonitoringInfo);

    PortMonitoringInfo getResponseData(PortMonitoringInfo portMonitoringInfo);

    PortMonitoringInfo getErrorData(PortMonitoringInfo portMonitoringInfo);


}

