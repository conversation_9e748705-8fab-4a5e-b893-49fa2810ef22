package iet.ustb.sf.utils;

import iet.ustb.sf.vo.domain.Org;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 组织树
 *
 * <AUTHOR>
 * @create 2022-07-16
 */
public class OrgTreeUtil {

    private List<Org> rootList; // 根节点
    private List<Org> bodyList; // 主体节点，包含根节点

    public OrgTreeUtil(List<Org> rootList, List<Org> bodyList) {
        this.rootList = rootList;
        this.bodyList = bodyList;
    }

    /**
     * 获取树
     *
     * @return
     * <AUTHOR>
     * @create 2022-07-16 16:19
     */
    public List<Org> getTree() {
        bodyList.stream();
        if (bodyList != null && !bodyList.isEmpty()) {
            // 声明一个map，用来过滤已操作过的数据
            Map<String, String> map = new HashMap<>();
            rootList.forEach(rootOrg -> getChild(rootOrg, map));// 传递根对象和一个空map
            return rootList;
        }
        return null;
    }

    /**
     * 获取所有子节点
     *
     * @param rootOrg
     * @param map
     * <AUTHOR>
     * @create 2022-07-16 16:20
     */
    public void getChild(Org rootOrg, Map<String, String> map) {
        List<Org> childList = new ArrayList<>();
        bodyList.stream()
                .filter(org -> !map.containsKey(org.getOrgCode()))// map内不包含子节点的code
                .filter(org -> rootOrg.getOrgCode().equals(org.getParentOrgCode()))// 子节点的父code等于根节点code 继续循环
                .forEach(org -> {
                    map.put(org.getOrgCode(), org.getParentOrgCode());// 当前节点code和父节点code
                    getChild(org, map);//递归调用
                    childList.add(org);
                });
        rootOrg.setChildren(childList);
    }
}
