package iet.ustb.sf.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.Rows;
import com.deepoove.poi.data.Tables;
import com.hikvision.artemis.sdk.constant.ContentType;
import iet.ustb.sf.dao.AppEvalDao;
import iet.ustb.sf.vo.domain.AppEval;
import iet.ustb.sf.vo.domain.Attach;
import iet.ustb.sf.vo.domain.DictionaryDtl;
import iet.ustb.sf.vo.domain.WeeklySummary;
import iet.ustb.sf.utils.enumVo.OneOrgEnumVo;
import iet.ustb.sf.service.*;
import iet.ustb.sf.utils.DateTools;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.vo.DoneRateVo;
import iet.ustb.sf.vo.eo.AppEvalNumEO;
import iet.ustb.sf.vo.eo.OneOrgFeedBackNumEO;
import iet.ustb.sf.vo.eo.UserAccessNumEO;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.Cleanup;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.criteria.Predicate;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 应用评价
 *
 * <AUTHOR>
 * @create 2023-12-14
 */
@Service
@CommonsLog
public class AppEvalServiceImpl implements AppEvalService {

    @Autowired
    private AppEvalDao appEvalDao;
    @Autowired
    private DictionaryDtlService dictionaryDtlService;
    @Autowired
    private AttachService attachService;
    @Autowired
    private WeeklySummaryService weeklySummaryService;
    @Autowired
    private PageLogService pageLogService;
    @Autowired
    private FeedBackService feedBackService;
    @Autowired
    private WarningInfoService warningInfoService;

    @Override
    public List<AppEval> findAll() {
        return appEvalDao.findAll();
    }

    @Override
    public List<AppEval> findAllByMultiCondition(JSONObject jsonObject) {
        return appEvalDao.findAll(createSpecs(jsonObject));
    }

    @Override
    public Page<AppEval> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<AppEval> appEvalPage = appEvalDao.findAll(createSpecs(jsonObject), pageable);
        return appEvalPage;
    }

    @Override
    public Page<Map<String, Object>> findWeekAppEval(JSONObject jsonObject) {
        String startDate = jsonObject.getString("startDate");
        String endDate = jsonObject.getString("endDate");
        String serviceNo = jsonObject.getString("serviceNo");
        String inputUnit = jsonObject.getString("inputUnit");
        String modelNo = jsonObject.getString("modelNo");

        Pageable pageable = ToolsUtil.initPage(jsonObject);
        Page<Map<String, Object>> mapPage = appEvalDao.findWeekAppEval(startDate, endDate, serviceNo, inputUnit, modelNo, pageable);
        return mapPage;
    }

    @Override
    public List<Map<String, Object>> findGroupByServerAndModel(JSONObject jsonObject) {
        String startDate = jsonObject.getString("startDate");
        String endDate = jsonObject.getString("endDate");
        String serviceNo = jsonObject.getString("serviceNo");
        String inputUnit = jsonObject.getString("inputUnit");
        String modelNo = jsonObject.getString("modelNo");

        List<Map<String, Object>> mapList = appEvalDao.findGroupByServerAndModel(startDate, endDate, serviceNo, inputUnit, modelNo);
        return mapList;
    }

    private Specification<AppEval> createSpecs(JSONObject json) {
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            String serviceNo = json.getString("serviceNo");// 应用编号
            String modelNo = json.getString("modelNo");// 模型编号
            String valueType = json.getString("valueType");// 价值类型
            String inputUnit = json.getString("inputUnit");// 填报单位
            String startDate = json.getString("startDate");//起始日期
            String endDate = json.getString("endDate");//结束日期

            if (StringUtils.isNotBlank(serviceNo)) {
                list.add(cb.equal(root.get("serviceNo"), serviceNo));
            }
            if (StringUtils.isNotBlank(modelNo)) {
                list.add(cb.equal(root.get("modelNo"), modelNo));
            }
            if (StringUtils.isNotBlank(valueType)) {
                list.add(cb.equal(root.get("valueType"), valueType));
            }
            if (StringUtils.isNotBlank(inputUnit)) {
                list.add(cb.equal(root.get("inputUnit"), inputUnit));
            }
            if (StringUtils.isNotBlank(startDate)) {
//                list.add(cb.greaterThanOrEqualTo(root.get("createDateTime").as(String.class), startDate + " 00:00:00"));
                list.add(cb.greaterThanOrEqualTo(root.get("weekDate"), startDate));
            }
            if (StringUtils.isNotBlank(endDate)) {
//                list.add(cb.lessThanOrEqualTo(root.get("createDateTime").as(String.class), endDate + " 23:59:59"));
                list.add(cb.lessThanOrEqualTo(root.get("weekDate"), endDate));
            }
            query.where(list.toArray(new Predicate[list.size()]));
            query.orderBy(cb.desc(root.get("createDateTime")));
            return query.getRestriction();
        };
    }

    @Override
    public void save(AppEval appEval) {
        appEvalDao.save(appEval);
    }

    @Override
    public void saveAll(List<AppEval> appEvals) {
        appEvalDao.saveAll(appEvals);
    }

    @Override
    public void delete(AppEval appEval) throws Exception {
        appEvalDao.delete(appEval);
    }

    @Override
    public void deleteByMultiKey(JSONObject jsonObject) throws Exception {
        String weekDate = jsonObject.getString("weekDate");
        String serviceNo = jsonObject.getString("serviceNo");
        appEvalDao.deleteByMultiKey(weekDate, serviceNo);
    }

    @Override
    public String exportPdf(JSONObject jsonObject, HttpServletRequest request, HttpServletResponse response) {
        try {
            HashMap<String, Object> finalMap = new HashMap<>();
            String week = jsonObject.getString("week");
            String weekPeriod = jsonObject.getString("weekPeriod");
            String startDate = jsonObject.getString("startDate");
            String endDate = jsonObject.getString("endDate");
            finalMap.put("week", week);
            finalMap.put("weekPeriod", weekPeriod);
            // 注意：此处用的是 <区块对>key是字符串，value则放置一个集合，类似于模板引擎的foreach标签
            ArrayList<Object> inputList = new ArrayList<>();
            List<DictionaryDtl> dictDtlList1 = dictionaryDtlService.findByDictCode("inputUnit");
            List<DictionaryDtl> dictDtlList2 = dictionaryDtlService.findByDictCode("serviceByEval");
            List<DictionaryDtl> dictDtlList3 = dictionaryDtlService.findByDictCode("modelNo");
            List<DictionaryDtl> dictDtlList4 = dictionaryDtlService.findByDictCode("module");

            List<AppEval> appEvalList = appEvalDao.findAll(createSpecs(jsonObject));
            appEvalList = appEvalList.stream()
                    .map(e -> {
                        String inputUnit = e.getInputUnit();
                        String inputUnitNew = dictDtlList1.stream()
                                .filter(d -> d.getCode().equals(inputUnit))
                                .map(DictionaryDtl::getValue).findFirst().orElse(inputUnit);
                        e.setInputUnit(inputUnitNew);

                        String serviceNo = e.getServiceNo();
                        String serviceNoNew = dictDtlList2.stream()
                                .filter(d -> d.getCode().equals(serviceNo))
                                .map(DictionaryDtl::getValue).findFirst().orElse(serviceNo);
                        e.setServiceNo(serviceNoNew);

                        String modelNo = e.getModelNo();
                        String modelNoNew = dictDtlList3.stream()
                                .filter(d -> d.getCode().equals(modelNo))
                                .map(DictionaryDtl::getValue).findFirst().orElse(modelNo);
                        e.setModelNo(modelNoNew);
                        return e;
                    }).collect(Collectors.toList());
            Map<String, List<AppEval>> collect1 = appEvalList.stream()
                    .collect(Collectors.groupingBy(AppEval::getInputUnit));

            for (Map.Entry entry : collect1.entrySet()) {
                HashMap<String, Object> inputUnitMap = new HashMap<>();
                String inputUnit = (String) entry.getKey();
                inputUnitMap.put("inputUnit", inputUnit);

                List<AppEval> appEvalList1 = (List<AppEval>) entry.getValue();

                ArrayList<Object> serviceList = new ArrayList<>();
                Map<String, List<AppEval>> collect2 = appEvalList1.stream()
                        .collect(Collectors.groupingBy(AppEval::getServiceNo));
                for (Map.Entry entry2 : collect2.entrySet()) {
                    HashMap<String, Object> serviceNoMap = new HashMap<>();
                    String serviceNo = (String) entry2.getKey();
                    serviceNoMap.put("serviceNo", serviceNo);

                    List<AppEval> appEvalList2 = (List<AppEval>) entry2.getValue();

                    ArrayList<Object> modelList = new ArrayList<>();
                    Map<String, List<AppEval>> collect3 = appEvalList2.stream()
                            .collect(Collectors.groupingBy(AppEval::getModelNo));
                    List<String> modelNoList = new ArrayList<>();
                    for (Map.Entry entry3 : collect3.entrySet()) {
                        String modelNo = (String) entry3.getKey();
                        modelNoList.add(modelNo);

                        List<AppEval> appEvalList3 = (List<AppEval>) entry3.getValue();
                        for (int i = 0; i < appEvalList3.size(); i++) {
                            AppEval appEval = appEvalList3.get(i);
                            // 模拟从mysql查询列表数据
                            HashMap<String, Object> stateItem = new HashMap<>();
                            stateItem.put("modelNo", appEval.getModelNo());
                            stateItem.put("appDescription", appEval.getAppDescription());
                            String feedBack = appEval.getFeedBack();
                            stateItem.put("feedBack", StringUtils.isNotBlank(feedBack) ? feedBack : "无");
                            modelList.add(stateItem);
                        }
                    }
                    serviceNoMap.put("模型列表", modelList);
                    serviceList.add(serviceNoMap);
                }
                inputUnitMap.put("模块列表", serviceList);
                inputList.add(inputUnitMap);
            }
            finalMap.put("应用列表", inputList);

            JSONObject paramJson = new JSONObject();
            paramJson.put("loginTimeStart", startDate);
            paramJson.put("loginTimeEnd", endDate);
            paramJson.put("leaderType", 1);// 高层
            paramJson.put("pageIndex", 1);
            paramJson.put("pageSize", 9999);
            Page<UserAccessNumEO> userPageAccessList1 = pageLogService.findUserPageAccessList(paramJson);
            List<UserAccessNumEO> accessList1 = userPageAccessList1.getContent();

            paramJson.put("leaderType", 2);// 中层
            Page<UserAccessNumEO> userPageAccessList2 = pageLogService.findUserPageAccessList(paramJson);
            List<UserAccessNumEO> accessList2 = userPageAccessList2.getContent();

            List<UserAccessNumEO> mergedAccessList = Stream.concat(accessList1.stream(), accessList2.stream())
                    .sorted(Comparator.comparingLong(UserAccessNumEO::getAccessNum).reversed())
                    .collect(Collectors.toList());

            //添加表格
            //填充表头，表格的第一行
            RowRenderData row0 = Rows.of("用户名", "访问次数", "本周排名").textBold().textFontSize(10).center().create();
            Tables.TableBuilder tableBuilder = Tables.of(row0);
            for (int i = 0; i < mergedAccessList.size(); i++) {
                UserAccessNumEO userAccessNumEO = mergedAccessList.get(i);
                RowRenderData row = Rows.of(userAccessNumEO.getUserName(), String.valueOf(userAccessNumEO.getAccessNum()), String.valueOf(i + 1))
                        .textFontSize(10).center().create();
                tableBuilder.addRow(row);
            }
            finalMap.put("accessTable", tableBuilder.create());// 中层页面访问排行

            // 本周总共
            paramJson = new JSONObject();
            paramJson.put("startDate", startDate);
            paramJson.put("endDate", endDate);
            List<OneOrgFeedBackNumEO> tempFeedBackNumList = this.getOneOrgFeedBackNumEOS(paramJson);
            List<OneOrgFeedBackNumEO> feedBackNumList = new ArrayList<>(tempFeedBackNumList);
            for (OneOrgEnumVo enumVo : OneOrgEnumVo.values()) {
                String code = enumVo.getCode();
                boolean flag = false;// 不存在
                for (OneOrgFeedBackNumEO oneOrgFeedBackNumEO : tempFeedBackNumList) {
                    String oneOrgCode = oneOrgFeedBackNumEO.getOneOrgCode();
                    if (code.equals(oneOrgCode)) {
                        flag = true;// 存在
                        break;
                    }
                }
                if (!flag) {// 不存在，则追加 次数补0
                    OneOrgFeedBackNumEO eo = new OneOrgFeedBackNumEO(enumVo.getCode(), enumVo.getName(), 0);
                    feedBackNumList.add(eo);
                }
            }
            long totalNum = feedBackNumList.stream()
                    .mapToLong(OneOrgFeedBackNumEO::getNum)
                    .sum();
            finalMap.put("totalNum", totalNum);// 本周总共问题反馈个数

            OneOrgFeedBackNumEO oneOrgFeedBackNumEO = feedBackNumList.stream()
                    .findFirst().orElse(new OneOrgFeedBackNumEO());
            String oneOrgName = oneOrgFeedBackNumEO.getOneOrgName();
            Integer num = oneOrgFeedBackNumEO.getNum();
            finalMap.put("mostUnit", oneOrgName);// 本周反馈问题最多的厂处
            finalMap.put("mostNum", num);// 本周反馈问题最多的次数

            // 本周已处理
            paramJson = new JSONObject();
            paramJson.put("startDate", startDate);
            paramJson.put("endDate", endDate);
            paramJson.put("handleStatus", 2);
            List<OneOrgFeedBackNumEO> completedFeedBackNumList = this.getOneOrgFeedBackNumEOS(paramJson);
            long completedNum = completedFeedBackNumList.stream()
                    .mapToLong(OneOrgFeedBackNumEO::getNum)
                    .sum();
            finalMap.put("completedNum", completedNum);// 已处理问题反馈个数

            // 上周总共
            paramJson.put("startDate", DateTools.nDaysAfterOneDateString(startDate, -7));
            paramJson.put("endDate", DateTools.nDaysAfterOneDateString(endDate, -7));
            List<OneOrgFeedBackNumEO> lastFeedBackNumList = this.getOneOrgFeedBackNumEOS(paramJson);
            long lastTotalNum = lastFeedBackNumList.stream()
                    .mapToLong(OneOrgFeedBackNumEO::getNum)
                    .sum();

            long diffNum = totalNum - lastTotalNum;
            String calcuMethod = diffNum >= 0 ? "增加" : "减少";
            long addNum = Math.abs(diffNum);
            finalMap.put("calcuMethod", calcuMethod);// 计算方式
            finalMap.put("addNum", addNum);// 较上周增加/减少个数

            row0 = Rows.of("单位", "次数").textBold().textFontSize(10).center().create();
            tableBuilder = Tables.of(row0);
            //填充表格内容
            for (OneOrgFeedBackNumEO e : feedBackNumList) {
                RowRenderData row = Rows.of(e.getOneOrgName(), String.valueOf(e.getNum()))
                        .textFontSize(10).center().create();
                tableBuilder.addRow(row);
            }
            finalMap.put("feedbackTable", tableBuilder.create());

            paramJson = new JSONObject();
            paramJson.put("startTime", startDate);
            paramJson.put("endTime", endDate);
            // 报警推送处置情况统计
            List<DoneRateVo> doneRateVoList = warningInfoService.getDoneRateGroupRoles(paramJson);

            row0 = Rows.of("岗位名称", "报警数量", "处置数量", "未处置数", "处置率").textBold().textFontSize(10).center().create();
            tableBuilder = Tables.of(row0);
            //填充表格内容
            for (DoneRateVo doneRateVo : doneRateVoList) {
                RowRenderData row = Rows.of(doneRateVo.getRoleName(),
                        String.valueOf(doneRateVo.getTotal()), String.valueOf(doneRateVo.getDone()),
                        String.valueOf(doneRateVo.getUnDone()), doneRateVo.getRate() + "%")
                        .textFontSize(10).center().create();
                tableBuilder.addRow(row);
            }
            finalMap.put("warningTable", tableBuilder.create());

            paramJson = new JSONObject();
            paramJson.put("plannCompleteStartDate", startDate);
            paramJson.put("plannCompleteEndDate", endDate);
            paramJson.put("type", 2);
            List<WeeklySummary> weeklySummaryList = weeklySummaryService.findByMultiCondition(paramJson)
                    .stream()
                    .map(e -> {
                        String module = String.valueOf(e.getModule());
                        String moduleNew = dictDtlList4.stream()
                                .filter(d -> d.getCode().equals(module))
                                .map(DictionaryDtl::getValue).findFirst().orElse(null);
                        e.setRemark(moduleNew);
                        return e;
                    }).collect(Collectors.toList());

            int weekTotalNum = weeklySummaryList.size();

            long weekCompletedNum = weeklySummaryList.stream()
                    .filter(e -> "1".equals(e.getCompleted())).count();

            BigDecimal weekRate;
            if (weekTotalNum != 0) {
                weekRate = BigDecimal.valueOf(weekCompletedNum)
                        .divide(BigDecimal.valueOf(weekTotalNum), BigDecimal.ROUND_HALF_UP, 2)
                        .multiply(BigDecimal.valueOf(100));
            } else {
                weekRate = BigDecimal.valueOf(100);
            }

            finalMap.put("weekTotalNum", weekTotalNum);// 本周工作计划总数
            finalMap.put("weekCompletedNum", weekCompletedNum);// 本周工作计划完成总数
            finalMap.put("weekRate", weekRate);// 本周工作计划完成率

            //填充表头，表格的第一行
            row0 = Rows.of("模块名称", "周计划内容", "项目组负责人", "板材负责人", "负责单位", "工作安排日期",
                    "计划完成日期", "是否完成", "未完成情况说明").textBold().textFontSize(10).center().create();
            tableBuilder = Tables.of(row0);
            //填充表格内容
            for (WeeklySummary weeklySummary : weeklySummaryList) {
                RowRenderData row = Rows.of(weeklySummary.getRemark(), weeklySummary.getCurrWeekSummary(), weeklySummary.getProjectTeamLeader(),
                                weeklySummary.getSheetMaterialLeader(), weeklySummary.getResponsibleUnit(), weeklySummary.getWorkArrangeDate(),
                                weeklySummary.getPlannCompleteDate(), "1".equals(weeklySummary.getCompleted()) ? "完成" : "未完成", weeklySummary.getExplanation())
                        .textFontSize(10).center().create();
                tableBuilder.addRow(row);
            }
            finalMap.put("thisWeekTable", tableBuilder.create());

            paramJson = new JSONObject();
            paramJson.put("plannCompleteStartDate", DateTools.nDaysAfterOneDateString(startDate, 7));
            paramJson.put("plannCompleteEndDate", DateTools.nDaysAfterOneDateString(endDate, 7));
            paramJson.put("type", 2);
            weeklySummaryList = weeklySummaryService.findByMultiCondition(paramJson)
                    .stream()
                    .map(e -> {
                        String module = String.valueOf(e.getModule());
                        String moduleNew = dictDtlList4.stream()
                                .filter(d -> d.getCode().equals(module))
                                .map(DictionaryDtl::getValue).findFirst().orElse(null);
                        e.setRemark(moduleNew);
                        return e;
                    }).collect(Collectors.toList());

            //填充表头，表格的第一行
            row0 = Rows.of("模块名称", "周计划内容", "项目组负责人", "板材负责人", "负责单位", "工作安排日期",
                    "计划完成日期").textBold().textFontSize(10).center().create();
            tableBuilder = Tables.of(row0);
            //填充表格内容
            for (WeeklySummary weeklySummary : weeklySummaryList) {
                RowRenderData row = Rows.of(weeklySummary.getRemark(), weeklySummary.getCurrWeekSummary(), weeklySummary.getProjectTeamLeader(),
                                weeklySummary.getSheetMaterialLeader(), weeklySummary.getResponsibleUnit(), weeklySummary.getWorkArrangeDate(), weeklySummary.getPlannCompleteDate())
                        .textFontSize(10).center().create();
                tableBuilder.addRow(row);
            }
            finalMap.put("nextWeekTable", tableBuilder.create());

            finalMap.put("weekEndDate", DateTools.getFormatDateTime(endDate, "yyyy年MM月dd日"));

            JSONObject json = new JSONObject();
            json.put("serviceNo", "res");
            json.put("name", "工作推进周报.docx");
            Attach attach = attachService.findAllByMultiCondition(json)
                    .stream()
                    .sorted(Comparator.comparing(Attach::getCreateDateTime).reversed())
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("工作推进周报模板不存在"));
            String realPath = attach.getPath();
            File file = new File(realPath);

            // 进行编译
            String absolutePath = file.getAbsolutePath();
            XWPFTemplate render = XWPFTemplate.compile(absolutePath).render(finalMap);

            String outPath = "/var/files/023958/工作推进周报示例.docx";
            File outFile = new File(outPath);
            render.writeToFile(outFile.getAbsolutePath());

            // 如果是下载，而不是直接显示，没必要写这个媒体类型
            response.setContentType("application/msword");
            response.setCharacterEncoding("UTF-8");
            String fileName = "工作推进周报-第" + week + "周(" + weekPeriod + ")";
            String fileNameEncoder = URLEncoder.encode(fileName, "utf-8");
            // 如果是下载，不需要上面设置媒体类型，直接此处设置附件名字即可
            response.setHeader("Content-disposition", "attachment;filename=" + fileNameEncoder + ".docx");
            // 获取OutputStream对象
            @Cleanup ServletOutputStream out = response.getOutputStream();
            out.write(Files.readAllBytes(Paths.get(outPath)));

            //删除临时文件
            File tempFile = new File(outPath);
            Files.delete(tempFile.toPath());

            //设置临时文件的地址
            /*String tempPath = UUID.randomUUID() + ".docx";
            //根据模板生成临时文件
            render.writeToFile(tempPath);
            String downloadPath = this.getDownloadPath(request);
            //将docx流转换为pdf流
            String outPath = downloadPath + File.separator + "工作推进周报-第" + week + "周(" + weekPeriod + ").pdf";
            WordConvertPdf.doc2pdf(tempPath, outPath);
            */

        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    private List<OneOrgFeedBackNumEO> getOneOrgFeedBackNumEOS(JSONObject paramJson) {
        // 查找厂处问题反馈数量
        List<Map<String, Object>> mapList = feedBackService.findOneOrgFeedBackNum(paramJson);
        List<OneOrgFeedBackNumEO> feedBackNumList = JSON.parseObject(JSON.toJSONString(mapList), new TypeReference<List<OneOrgFeedBackNumEO>>() {});
        feedBackNumList = feedBackNumList.stream()
                .map(e -> {
                    String oneOrgCode = e.getOneOrgCode();
                    for (OneOrgEnumVo enumVo : OneOrgEnumVo.values()) {
                        if (oneOrgCode.equals(enumVo.getCode())) {
                            e.setOneOrgName(enumVo.getName());
                        }
                    }
                    return e;
                }).collect(Collectors.toList());
        return feedBackNumList;
    }

    public MultipartFile convertToMultipartFile(String filePath) throws IOException {


        // 创建一个File对象，将文件路径作为参数传入
        File file = new File(filePath);

        // 创建一个文件输入流，将File对象作为参数传入
        FileInputStream inputStream = new FileInputStream(file);

        // 创建一个MockMultipartFile对象，将文件名、文件字节流、文件类型作为参数传入
        MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), ContentType.CONTENT_TYPE_XML, inputStream);

        return multipartFile;
    }

    /**
     * 获取不同系统的下载路径
     *
     * @param request
     * @return {@link String }
     * <AUTHOR>
     * @create 2024-01-05
     */
    private String getDownloadPath(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        Assert.hasText(userAgent, "请求头不包含系统信息，请核实！");
        boolean isMac = userAgent.contains("Mac");

        String downloadPath = "";
        if (isMac) {
            downloadPath = "/Users/<USER>/Downloads";
        } else {
            downloadPath = "D:\\Downloads";
        }
        return downloadPath;
    }

    @Override
    public void exportExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException {

        //功能描述：把同一个表格多个sheet测试结果重新输出
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("应用评价统计");
        // 设置sheet表头名称
        exportParams.setSheetName("应用评价统计");
        // 创建sheet1使用得map
        Map<String, Object> oneExportMap = new HashMap<>();
        // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
        oneExportMap.put("title", exportParams);
        // 模版导出对应得实体类型
        oneExportMap.put("entity", AppEvalNumEO.class);

        List<DictionaryDtl> serviceByEvals = dictionaryDtlService.findByDictCode("serviceByEval");
        List<DictionaryDtl> modelNos = dictionaryDtlService.findByDictCode("modelNo");

        // 获取厂处数据 排除 板材事业部江苏南钢板材销售有限公司、板材事业部金石高新材料项目部
        List<Map<String, Object>> dataList = this.findGroupByServerAndModel(jsonObject);
        List<AppEvalNumEO> list = JSON.parseObject(JSON.toJSONString(dataList), new TypeReference<List<AppEvalNumEO>>() {});
        List<AppEvalNumEO> newList = list.stream()
            .map(e -> {
                String serviceNo = e.getServiceNo();
                String modelNo = e.getModelNo();
                String serviceName = serviceByEvals.stream()
                        .filter(d -> d.getCode().equals(serviceNo))
                        .map(DictionaryDtl::getValue).findFirst().orElse(null);
                String modelName = modelNos.stream()
                        .filter(d -> d.getCode().equals(modelNo))
                        .map(DictionaryDtl::getValue).findFirst().orElse(null);
                e.setServiceName(serviceName);
                e.setModelName(modelName);
                return e;
            }).collect(Collectors.toList());

        oneExportMap.put("data", newList);

        // 将sheet使用得map进行包装
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        //后续增加sheet组，则后面继续追加即可;
        sheetsList.add(oneExportMap);

        // 执行方法
        @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
        //设置编码格式
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        //设置内容类型
        response.setContentType("application/octet-stream");
        //设置头及文件命名。
        response.setHeader("Content-Disposition", "attachment;filename="
                + URLEncoder.encode("应用评价统计.xls", StandardCharsets.UTF_8.name()));
        //写出流
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        workBook.write(outputStream);
    }

}
