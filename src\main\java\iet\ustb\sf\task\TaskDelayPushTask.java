//package iet.ustb.sf.schedule;
//
//import iet.ustb.sf.service.TaskTrackingService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
///**
// * 任务超时企业微信推送
// * <AUTHOR>
// * @create 2024-04-01
// */
//@Component
//@EnableScheduling
//public class TaskDelayPushTask {
//
//    @Autowired
//    private TaskTrackingService taskTrackingService;
//
//    @Scheduled(cron = "0 0 09 * * ?")
//    public void sendMessageToWecom() {
//        taskTrackingService.sendMessageToWecom();
//    }
//
//}
