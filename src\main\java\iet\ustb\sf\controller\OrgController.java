package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.Org;
import iet.ustb.sf.service.OrgService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2022/6/22
 */
@RestController
@RequestMapping("/org")
@Api(value = "组织管理", tags = "组织管理")
public class OrgController {

    @Autowired
    private OrgService orgService;

    @ResponseBody
    @ApiOperation(value = "查询所有组织列表", notes = "查询所有组织列表")
    @PostMapping("/findAll")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<Org> orgList = orgService.findAll();
            String jsonStr = JSON.toJSONString(orgList, true);
            ajaxJson.setData(JSONArray.parseArray(jsonStr));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ResponseBody
    @ApiOperation(value = "保存", produces = "application/json")
    @PostMapping("/save")
    public AjaxJson doAddResource(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {

            ajaxJson.setData(orgService.save(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
            e.printStackTrace();
        }
        return ajaxJson;
    }


    @ResponseBody
    @ApiOperation(value = "根据组织编号懒加载组织列表", notes = "入参：{\"orgCode\":\"*********\"}", produces = "application/json")
    @PostMapping("/findListByOrgCode")
    public AjaxJson findListByOrgCode(@RequestBody JSONObject json) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            // 根据组织编号懒加载组织列表
            List<Map<String, Object>> orgList = orgService.findListByOrgCode(json);
            ajaxJson.setData(orgList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    //删除
    @ResponseBody
    @PostMapping("/doDeleteOrg")
    @ApiOperation(value = "删除", notes = "删除,{\"id\":\"000068d3-6417-4db9-80aa-e21333a52462\"}", produces = "application/json")
    public AjaxJson doDeleteResource(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            orgService.doDeleteOrg(jsonObject);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    @ResponseBody
    @ApiOperation(value = "根据组织编号加载组织树", notes = "入参：{\"orgCode\":\"*********\"}", produces = "application/json")
    @PostMapping("/findOrgTreeByOrgCode")
    public AjaxJson findOrgTreeByOrgCode(@RequestBody JSONObject json) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            // 根据组织编号查询组织列表
            List<Org> orgList = orgService.findOrgTreeByOrgCode(json);
            String jsonStr = JSON.toJSONString(orgList, true);
            ajaxJson.setData(JSONArray.parseArray(jsonStr));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/findOrgByUserNo")
    @ApiOperation(value = "根据userNo查找组织", notes = "{\"userNo\":\"018048\"}", produces = "application/json")
    public AjaxJson findOrgByUserNo(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(orgService.findOrgByUserNo(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/findOrgsByNames")
    @ApiOperation(value = "根据userName查找组织", notes = "{\"userName\":\"018048\"}", produces = "application/json")
    public AjaxJson findOrgsByNames(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(orgService.findOrgsByNames(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

}
