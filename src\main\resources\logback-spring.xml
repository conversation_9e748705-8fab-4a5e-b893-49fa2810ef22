<configuration>
    <!-- 彩色控制台日志 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!-- 彩色日志格式 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %highlight(%-5level) %cyan(%logger{36}) - %msg%n</pattern>
            <!-- 启用彩色输出 -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 滚动日志文件，按日期存储 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/iet-resources-service.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/iet-resources-service-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>10</maxHistory> <!-- 保留10天的日志 -->
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 权限系统客户端日志配置 - 特殊颜色标识 -->
    <logger name="iet.ustb.sf.client.PermissionSystemClient" level="DEBUG" additivity="false">
        <appender name="PERMISSION_CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <!-- 权限系统日志使用特殊颜色：绿色背景 + 白色字体 -->
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %highlight(%-5level) %green([权限系统]) %cyan(%logger{36}) - %yellow(%msg)%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- JWT工具类日志配置 - 特殊颜色标识 -->
    <logger name="iet.ustb.sf.utils.JwtUtil" level="DEBUG" additivity="false">
        <appender name="JWT_CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <!-- JWT日志使用特殊颜色：蓝色背景 + 白色字体 -->
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %highlight(%-5level) %blue([JWT令牌]) %cyan(%logger{36}) - %magenta(%msg)%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        <appender-ref ref="FILE"/>
    </logger>

    <!-- 根日志配置，控制台和文件都输出 -->
    <root level="info">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>
