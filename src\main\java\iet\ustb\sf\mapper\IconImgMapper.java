package iet.ustb.sf.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import iet.ustb.sf.vo.domain.IconImg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * (IconImg)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-14 14:02:09
 */
@Mapper
public interface IconImgMapper extends BaseMapper<IconImg> {

    /**
    * 批量新增数据（MyBatis原生foreach方法）
    *
    * @param entities List<IconImg> 实例对象列表
    * @return 影响行数
    */
    int insertBatch(@Param("entities") List<IconImg> entities);

    /**
    * 批量新增或按主键更新数据（MyBatis原生foreach方法）
    *
    * @param entities List<IconImg> 实例对象列表
    * @return 影响行数
    * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
    */
    int insertOrUpdateBatch(@Param("entities") List<IconImg> entities);

    /**
     * 获取图标信息
     * @param iconImg
     * @return
     */
    IPage<IconImg> getIconImgInfo(Page<IconImg> page, @Param("iconImg") IconImg iconImg);

}

