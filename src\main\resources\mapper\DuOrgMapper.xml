<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="iet.ustb.sf.mapper.DuOrgMapper">

    <resultMap type="iet.ustb.sf.vo.domain.Org" id="DuOrgMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="orgCode" column="orgcode" jdbcType="VARCHAR"/>
        <result property="orgName" column="orgname" jdbcType="VARCHAR"/>
        <result property="accOrgCode" column="accorgcode" jdbcType="VARCHAR"/>
        <result property="accSysCode" column="accsyscode" jdbcType="VARCHAR"/>
        <result property="erpSpecField" column="erpspecfield" jdbcType="VARCHAR"/>
        <result property="fax" column="fax" jdbcType="VARCHAR"/>
        <result property="operStus" column="operstus" jdbcType="VARCHAR"/>
        <result property="orgAllName" column="orgallname" jdbcType="VARCHAR"/>
        <result property="orgCurCode" column="orgcurcode" jdbcType="VARCHAR"/>
        <result property="orgDesc" column="orgdesc" jdbcType="VARCHAR"/>
        <result property="orgType" column="orgtype" jdbcType="VARCHAR"/>
        <result property="isLegalPerson" column="islegalperson" jdbcType="VARCHAR"/>
        <result property="orgLegalPerson" column="orglegalperson" jdbcType="VARCHAR"/>
        <result property="parentOrgCode" column="parentorgcode" jdbcType="VARCHAR"/>
        <result property="postCode" column="postcode" jdbcType="VARCHAR"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="repeatField" column="repeatfield" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="webAddr" column="webaddr" jdbcType="VARCHAR"/>
        <result property="orgLevel" column="orgLevel" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into resource.du_org(orgcode, orgname, accorgcode, accsyscode, erpspecfield, fax, operstus, orgallname, orgcurcode, orgdesc, orgtype, islegalperson, orglegalperson, parentorgcode, postcode, remarks, repeatfield, status, webaddr, orgLevel)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.orgcode}, #{entity.orgname}, #{entity.accorgcode}, #{entity.accsyscode}, #{entity.erpspecfield}, #{entity.fax}, #{entity.operstus}, #{entity.orgallname}, #{entity.orgcurcode}, #{entity.orgdesc}, #{entity.orgtype}, #{entity.islegalperson}, #{entity.orglegalperson}, #{entity.parentorgcode}, #{entity.postcode}, #{entity.remarks}, #{entity.repeatfield}, #{entity.status}, #{entity.webaddr},#{entity.orglevel})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into resource.du_org(orgcode, orgname, accorgcode, accsyscode, erpspecfield, fax, operstus, orgallname, orgcurcode, orgdesc, orgtype, islegalperson, orglegalperson, parentorgcode, postcode, remarks, repeatfield, status, webaddr, createdatetime, createuserno, updatedatetime, updateuserno, orgLevel)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orgcode}, #{entity.orgname}, #{entity.accorgcode}, #{entity.accsyscode}, #{entity.erpspecfield}, #{entity.fax}, #{entity.operstus}, #{entity.orgallname}, #{entity.orgcurcode}, #{entity.orgdesc}, #{entity.orgtype}, #{entity.islegalperson}, #{entity.orglegalperson}, #{entity.parentorgcode}, #{entity.postcode}, #{entity.remarks}, #{entity.repeatfield}, #{entity.status}, #{entity.webaddr},  #{entity.orglevel})
        </foreach>
        on duplicate key update
orgcode = values(orgcode) , orgname = values(orgname) , accorgcode = values(accorgcode) , accsyscode = values(accsyscode) , erpspecfield = values(erpspecfield) , fax = values(fax) , operstus = values(operstus) , orgallname = values(orgallname) , orgcurcode = values(orgcurcode) , orgdesc = values(orgdesc) , orgtype = values(orgtype) , islegalperson = values(islegalperson) , orglegalperson = values(orglegalperson) , parentorgcode = values(parentorgcode) , postcode = values(postcode) , remarks = values(remarks) , repeatfield = values(repeatfield) , status = values(status) , webaddr = values(webaddr) , orgLevel = values(orgLevel)     </insert>

    <select id="checkOrgCodesExist" resultMap="DuOrgMap">
        SELECT
            *
        FROM
            resource.du_org
        <where>
            <if test="orgCodeList != null and orgCodeList.size() > 0">
                orgcode IN
                <foreach collection="orgCodeList" item="orgcode" open="(" separator="," close=")">
                    #{orgcode}
                </foreach>
            </if>
            <if test="parentOrgCodeList != null and parentOrgCodeList.size() > 0">
                AND parentorgcode IN
                <foreach collection="parentOrgCodeList" item="parentorgcode" open="(" separator="," close=")">
                    #{parentorgcode}
                </foreach>
            </if>
        </where>
    </select>


    <!-- 获取当前最大一级菜单编号 -->
    <select id="getMaxTopLevelCode" resultType="String">
        SELECT
            LPAD(MAX(CAST(SUBSTRING_INDEX(orgcode, '_', -1) AS UNSIGNED)), 3, '0')
        FROM
            resource.du_org
        WHERE
            parentorgcode IS NULL
          AND orgcode REGEXP '^res_[0-9]+_[0-9]+$'
    </select>

    <!-- 获取当前最大子菜单编号 -->
    <select id="getMaxSubLevelCode" resultType="String">
        SELECT
            MAX(SUBSTRING_INDEX(orgcode, '_', -1))
        FROM
            resource.du_org
        WHERE
            parentorgcode = #{parentCode}
    </select>

    <!-- 根据父级编码查询组织信息-->
    <select id="findListByOrgCode" resultType="map">
        SELECT
            id,
            orgCode,
            orgAllName,
            orgDesc,
            parentOrgCode,
            (SELECT s.orgAllName FROM du_org s WHERE s.orgCode = t.parentOrgCode) AS parentOrgName,
            createUserNo,
            createDateTime,
            status,
            EXISTS (SELECT id FROM du_org WHERE parentOrgCode = t.orgCode ) AS hasChildren
        FROM du_org t
        WHERE 1=1
        <if test="parentOrgCode == null or parentOrgCode == ''">
            AND t.parentOrgCode IS NULL
        </if>
        <if test="parentOrgCode != null and parentOrgCode != ''">
            AND t.parentOrgCode = #{parentOrgCode}
        </if>
    </select>


</mapper>

