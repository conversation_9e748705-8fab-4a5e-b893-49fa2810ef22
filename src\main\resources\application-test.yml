spring:
  cloud:
    consul:
      enabled: false
  redis:
    host: localhost
    port: 6379
    password:
    database: 1
  data:
    redis:
      repositories:
        enabled: true
  datasource:
    hikari:
      maximum-pool-size: 30
      max-lifetime: 300000
    url: ****************************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.jdbc.Driver
    test-on-borrow: false
    test-while-idel: true
    time-between-eviction-runs-millis: 3600000
    tomcat:
      max-wait: 10000
      max-active: 50

  #配置minio
minio:
  host: http://localhost:9001
  url: ${minio.host}/${minio.bucket}/
  access-key: KW5RLy7rJcb9GIpzIY9n
  secret-key: QpmV6u0xvyrog2BSvRioFyuqzIdFCJb3HNDb1oaS
  bucket: public
server:
  port: 8081

#权限系统配置 - 测试环境
permission:
  system:
    base:
      url: http://*************:7106  # 测试环境权限系统地址
    timeout: 5000  # 测试环境超时时间
    cache:
      expire-minutes: 30  # 测试环境缓存过期时间