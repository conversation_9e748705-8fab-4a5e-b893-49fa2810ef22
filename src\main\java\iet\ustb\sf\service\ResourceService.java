package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.Resource;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.service
 * @title: ResourceService
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/1011:04
 */
public interface ResourceService {

    //新增
    String doCreateResource(JSONObject jsonObject);

    //修改
    String doUpdateResource(JSONObject jsonObject);

    //删除
    void doDeleteResource(Resource resource);

    //查询
    Page<Resource> findAllResource(JSONObject jsonObject);

    List<Resource> findAllOfResource(Resource resource);

    List findAllResourceNoPage(Resource resource);

    Resource findResourceByParentID(JSONObject jsonObject);

    Resource findOneResourceByID(JSONObject jsonObject);

    List<Resource> filterResourceSelected(JSONObject jsonObject);

    String relateRole(JSONObject jsonObject);

    List<Resource> findResourceByUserNoAndServiceNameAndType(JSONObject jsonObject);

    List<Resource> findRootResource(Resource resource);

    void batchUpdateResource(JSONObject jsonObject);

    /**
     * 查找所有负责业务员
     *
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @create 2023-04-06
     */
    List<String> findAllHandleUser();

    /**
     * 查找所有负责业务员负责页面数量
     *
     * @param userNo 用户编号
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-04-07
     */
    List<Map<String, Object>> findAllHandleUserNum(String userNo);

    /**
     * 按id查找
     *
     * @param id 身份证件
     * @return {@link Optional }<{@link Resource }>
     * <AUTHOR>
     * @create 2023-03-27
     */
    Optional<Resource> findById(String id);

    List findAllResourceNoPageWithIcon(JSONObject jsonObject);

    /**
     * 根据菜单信息查询相关所有菜单数据
     * @param resource
     * @return
     */
    List<Resource> getResourceInfo (Resource resource);


    /**
     * 根据菜单信息查询相关所有菜单数据，并以树结构将数据返回
     * @param resource
     * @return
     */
    List<Resource> getResourceTree (Resource resource);
}
