package iet.ustb.sf.vo.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Lob;
import java.util.List;

/**
 * <AUTHOR>
 * @description 公共配置
 * @remark
 * @create 2022-03-15 09:18
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BasicDataConfig {

    @Id
    //父级code
    private String type;

    //子级编码
    private String code;

    //名称
    private String name;

    //内容
    @Lob
    private String content;

    //内容数据类型
    private String flag;
    //描述

    private String description;

    private List<DictionaryDtl> dictList;

}
