package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.Attach;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 附件
 *
 * <AUTHOR>
 * @create 2022-10-27
 */
public interface AttachService {

    /**
     * 查找所有内容
     *
     * @return {@link List }<{@link Attach }>
     * <AUTHOR>
     * @create 2022-10-26
     */
    List<Attach> findAll();

    /**
     * 按业务id查找
     *
     * @param relatedId 业务id
     * @return {@link List }<{@link Attach }>
     * <AUTHOR>
     * @create 2022-10-27
     */
    List<Attach> findByRelatedId(String relatedId);

    /**
     * 按业务id集合查找
     *
     * @param relatedIdList 业务id列表
     * @return {@link List }<{@link Attach }>
     * <AUTHOR>
     * @create 2023-04-12
     */
    List<Attach> findByRelatedIds(List<String> relatedIdList);

    /**
     * 按多条件查找所有
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link Attach }>
     * <AUTHOR>
     * @create 2022-11-02
     */
    List<Attach> findAllByMultiCondition(JSONObject jsonObject);

    /**
     * 按多条件查找分页
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link Attach }>
     * <AUTHOR>
     * @create 2022-10-26
     */
    Page<Attach> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param attach 反馈
     * @return {@link Attach }
     * <AUTHOR>
     * @create 2022-10-26
     */
    Attach save(Attach attach);

    /**
     * 全部保存
     *
     * @param attachList 附加列表
     * @return {@link List }<{@link Attach }>
     * <AUTHOR>
     * @create 2022-10-27
     */
    List<Attach> saveAll(List<Attach> attachList);

    /**
     * 上传附件
     *
     * @param files     文件
     * @param relatedId 关联 ID
     * @param serviceNo 业务编号
     * @return {@link List }<{@link Attach }>
     * <AUTHOR>
     * @create 2023-09-07
     */
    List<Attach> uploadFile(MultipartFile[] files, String relatedId, String serviceNo) throws Exception;

    /**
     * 按id下载附件
     *
     * @param id       身份证件
     * @param response 回答
     * <AUTHOR>
     * @create 2022-11-01
     */
    void downloadFileById(String id, HttpServletResponse response) throws Exception;

    /**
     * 删除
     *
     * @param jsonObject json对象
     * <AUTHOR>
     * @create 2022-10-26
     */
    void deleteByIds(JSONObject jsonObject) throws Exception;

    /**
     * 按相关id删除
     *
     * @param relatedId 相关id
     * <AUTHOR>
     * @create 2022-10-29
     */
    void deleteByRelatedId(String relatedId) throws Exception;

    /**
     * 按id查找
     *
     * @param idList id列表
     * @return {@link List }<{@link Attach }>
     * <AUTHOR>
     * @create 2022-10-28
     */
    List<Attach> findByIds(List<String> idList);
}
