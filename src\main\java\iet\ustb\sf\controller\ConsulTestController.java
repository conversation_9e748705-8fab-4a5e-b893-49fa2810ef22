package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.BasicDataModel;
import iet.ustb.sf.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Title:
 * Packet:iet.ustb.test.consultest.controller
 * Description:
 * Author:崔庆胜
 * Create Date: 2021/12/30 4:03 PM
 * Modify User:
 * Modify Date:
 * Modify Description:
 */
@RestController
@RequestMapping("/temp")
public class ConsulTestController {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private TestService testService;


//    @GetMapping("/health")
//    private String health() {
//        return "health";
//    }

    @Value("${server.port}")
    private String port;

    @GetMapping("/hello")
    private List<BasicDataModel> hello() {
        System.out.println(port);
        return testService.findAllByName();
//        return "hello consul 1\t" + port;
    }

    @GetMapping("/test")
    private String test(HttpServletRequest request, HttpServletResponse response) throws Exception {
        response.sendRedirect("/temp/test2");
        return "";
    }

    @GetMapping("/test2")
    private String test2(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return "test2";
    }

    @PostMapping("/test3")
    public JSONObject test(@RequestBody JSONObject content) {
        String url = "http://bancai.com/baicai/test";
//        JSONObject content = new JSONObject();
//        content.put("heatNo",heatNo);
//        content.put("occrCd","P");
        JSONObject res = restTemplate.postForObject(url, content, JSONObject.class);
        System.out.println(res);
        return res;
    }
}
