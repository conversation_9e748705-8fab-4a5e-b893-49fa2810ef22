package iet.ustb.sf.vo.domain;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.util.Date;

@Data
@Entity
@Table(name = "MENU_APPLY")
public class MenuApply extends BaseEntity  {

    @Column
    String createUserNo;

    //创建人姓名
    @Column
    String createUserName;

    //员工编号
    @Lob
    String userNo;

    //员工姓名
    @Lob
    String userName;

    //已拥有菜单
    @Lob
    String ownRscList;

    //新增菜单
    @Lob
    String newRscList;

    //处理状态,0-待审批,1-已审批，3-已处理，9-驳回
    @Column
    Integer status = 0;

    //备注
    @Column
    String remark;

    //审批用户编号
    @Column
    String approveUserNo;

    //审批用户姓名
    @Column
    String approveUserName;

    //审批时间
    @Column
    Date approveTime;

    //处理用户编号
    @Column
    String dealUserNo;

    //处理用户姓名
    @Column
    String dealUserName;

    //处理时间
    @Column
    Date dealTime;

}
