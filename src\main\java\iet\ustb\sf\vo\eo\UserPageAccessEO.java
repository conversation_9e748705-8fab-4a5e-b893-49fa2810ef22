package iet.ustb.sf.vo.eo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户页面访问明细
 *
 * <AUTHOR>
 * @create 2022-11-03
 * @see Serializable
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPageAccessEO implements Serializable {

    /**
     * 工号
     */
    @Excel(name = "工号",width = 20)
    private String userNo;
    /**
     * 姓名
     */
    @Excel(name = "姓名",width = 20)
    private String userName;

    /**
     * 组织编号
     */
    @Excel(name = "组织编号",width = 20)
    private String orgCode;

    /**
     * 组织名称
     */
    @Excel(name = "组织名称",width = 50)
    private String orgAllName;

    /**
     * 访问时间
     */
    private Date loginTime;

    /**
     * 访问时间
     */
    @Excel(name = "访问时间",width = 20)
    private String accessTime;

    /**
     * 模块
     */
    @Excel(name = "模块",width = 20)
    private String serviceName;

    /**
     * 菜单1
     */
    @Excel(name = "菜单1",width = 20)
    private String menu1;

    /**
     * 菜单2
     */
    @Excel(name = "菜单2",width = 20)
    private String menu2;

    /**
     * 菜单3
     */
    @Excel(name = "菜单3",width = 20)
    private String menu3;

    /**
     * 菜单4
     */
    @Excel(name = "菜单4",width = 20)
    private String menu4;

    /**
     * 菜单5
     */
    @Excel(name = "菜单5",width = 20)
    private String menu5;

    /**
     * 页面名称
     */
    @Excel(name = "页面名称",width = 20)
    private String pageName;
}
