package iet.ustb.sf.vo.domain;

import lombok.Data;
import org.hibernate.annotations.Proxy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.domain
 * @title: Desktop 桌面主类
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/2515:59
 */
@Data
@Entity
@Table(name = "desktop")
@Proxy(lazy = false)
public class Desktop extends BaseEntity {

    @Column
    String name;

    //类型，1-桌面，2-桌面控件
    @Column
    String type;

    //type为2时，parentID关联桌面ID
    @Column
    String parentID;

    //0-仪表板，1-主页面，2-工作台，3-自定义桌面
    @Column
    Integer dpCheck;

    @Column
    String userNo;

//    @OneToMany(fetch = FetchType.EAGER)
//    @Fetch(FetchMode.SUBSELECT)
//    List<FileFolder> fileFolderList;
//
//    @OneToMany(fetch = FetchType.EAGER)
//    @Fetch(FetchMode.SUBSELECT)
//    List<Icon> iconList;

    //删除标志,0-不删除，1-删除
    @Column
    Integer deleteTag = 0;
}
