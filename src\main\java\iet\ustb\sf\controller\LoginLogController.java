package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.LoginLog;
import iet.ustb.sf.service.LoginLogService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 登录日志
 *
 * <AUTHOR>
 * @create 2022-09-21
 */
@RestController
@RequestMapping("/loginLog")
@Api(value = "登录日志", tags = "登录日志")
public class LoginLogController {

    @Autowired
    private LoginLogService loginLogService;

    @ResponseBody
    @ApiOperation(value = "查询所有", notes = "查询所有")
    @PostMapping("/findAll")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<LoginLog> loginLogList = loginLogService.findAll();
            ajaxJson.setData(loginLogList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ResponseBody
    @ApiOperation(value = "按多条件查找登录日志", notes = "入参：{\"userNo\":\"023958\",\"orgCode\":\"1212\", \"startTime\": \"2022-09-22 09:18:50\",\"endTime\": \"2022-09-22 09:18:59\"}")
    @PostMapping("/findByMultiCondition")
    public AjaxJson findByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<LoginLog> loginLogPages = loginLogService.findByMultiCondition(jsonObject);
            ajaxJson.setData(loginLogPages);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ResponseBody
    @ApiOperation(value = "保存", produces = "application/json")
    @PostMapping("/save")
    public AjaxJson save(@RequestBody @Valid LoginLog loginLog, HttpServletRequest request) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            // 保存
            loginLogService.save(loginLog, request);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }



}
