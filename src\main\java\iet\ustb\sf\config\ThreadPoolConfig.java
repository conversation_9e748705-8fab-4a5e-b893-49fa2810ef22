package iet.ustb.sf.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: Dr.Monster
 * @Slogan: The never-compromising angry youth.
 * @PackageName: iet.ustb.sf.config
 * @Date: 2022/08/15/15:19
 * @Description:
 */
@Configuration
public class ThreadPoolConfig {
//    @Bean(value = "threadPoolInstance")
//    public ExecutorService createThreadPoolInstance() {
//        //通过guava类库的ThreadFactoryBuilder来实现线程工厂类并设置线程名称
//        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("thread-pool-%d").build();
//        ExecutorService threadPool = new ThreadPoolExecutor(10, 16, 60L, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(100), threadFactory, new ThreadPoolExecutor.AbortPolicy());
//        return threadPool;
//    }
}