package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.BasicDataModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.io.Serializable;
import java.util.List;

public interface BasicDataModelDao extends JpaSpecificationExecutor<BasicDataModel>, JpaRepository<BasicDataModel, Serializable> {

    List<BasicDataModel> findAllByName(String name);

    @Query("select b from BasicDataModel b where b.name like ?1 ")
    List<BasicDataModel> findByNameHql(String name);

    @Query(value = "select b.* from DM_BASIC_DATA_MODEL b where b.NAME like ?1 ", nativeQuery = true)
    List<BasicDataModel> findByNameNative(String name);

    @Transactional
    @Modifying
    @Query("update BasicDataModel b set b.aliasName = ?1 where b.name = ?2 ")
    void updateAlisName(String aliasName, String name);

    @Transactional
    @Modifying
    @Query("delete BasicDataModel b where b.name = ?2 ")
    void deleteByName(String name);

}
