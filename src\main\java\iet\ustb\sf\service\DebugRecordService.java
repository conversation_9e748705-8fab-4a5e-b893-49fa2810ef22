package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.DebugRecord;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 调试记录
 *
 * <AUTHOR>
 * @create 2023-04-19
 */
public interface DebugRecordService {

    /**
     * 查找所有内容
     *
     * @return {@link List }<{@link DebugRecord }>
     * <AUTHOR>
     * @create 2023-01-04
     */
    List<DebugRecord> findAll();

    /**
     * 按多条件查找分页
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link DebugRecord }>
     * <AUTHOR>
     * @create 2023-04-19
     */
    Page<DebugRecord> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param debugRecord
     * @return {@link DebugRecord }
     * <AUTHOR>
     * @create 2023-04-19
     */
    DebugRecord save(DebugRecord debugRecord);

    /**
     * 删除
     *
     * @param jsonObject json对象
     * <AUTHOR>
     * @create 2023-04-19
     */
    void delete(JSONObject jsonObject) throws Exception;
}
