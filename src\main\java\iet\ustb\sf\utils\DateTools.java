package iet.ustb.sf.utils;


import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.util.Assert;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * <AUTHOR>
 * @description 日期工具类
 * @create 2022/6/17
 **/
public class DateTools {

    /**
     * DASH破折号
     */
    public static final String DATE_FORMAT_DASH_TIME = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT_NO_DASH_TIME = "yyyyMMddHHmmss";

    /**
     * 日期格式静态字符串
     */
    public static final String YYYY = "yyyy";
    public static final String YYYY_MM = "yyyy-MM";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    /**
     * 斜线格式的日期
     */
    public static final String DATE_FORMAT_SLASH = "yyyy/MM/dd";

    private static final String[] WEEK_DAYS = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
    private static final String[] WEEK_DAYS_IN_CHINESE = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};

    public static Date dateStringToDate(String date, String strDateFormat) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat(strDateFormat);
        return df.parse(date);
    }

    /**
     * @param strDateFormat
     * @return
     * @function 返回指定格式当前日期, 格式:yyyyMMddHHmmss,如果指定格式报错,返回为标准格式yyyy-MM-dd HH:mm:ss
     */
    public static String getFullNowDateTime(String strDateFormat) {
        try {
            SimpleDateFormat ymd = new SimpleDateFormat(strDateFormat);
            return ymd.format(new Date());
        } catch (Exception e) {
            return DateTools.getFullNowDateTime("yyyy-MM-dd HH:mm:ss");
        }
    }

    /**
     * @return yyyy-MM-dd HH:mm:ss
     * @function 返回指定格式当前日期
     */
    public static String getFullNowDateTime() {
        return DateTools.getFullNowDateTime("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * @return yyyyMMddHHmmss
     * @function 返回指定格式当前日期
     */
    public static String getFullNowDateTimeErp() {
        return DateTools.getFullNowDateTime("yyyyMMddHHmmss");
    }

    /**
     * @return yyyyMMddHHmmss
     * @function 返回指定格式当前日期（事件）
     */
    public static String getFullNowDateTimeHourStart() {
        return DateTools.getFullNowDateTime("yyyyMMddHH");
    }

    /**
     * @return yyyyMMddHHmmss
     * @function 返回指定格式当前日期（事件）
     */
    public static String getFullNowDateTimeHourEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, -1);
        Date start = calendar.getTime();
        SimpleDateFormat ymd = new SimpleDateFormat("yyyyMMddHH");
        return ymd.format(start);
    }

    /**
     * @return yyyyMMddHHmmss
     * @function 返回指定格式当前日期（事件）
     */
    public static String getFullNowDateTimeHourEnd1() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, +1);
        Date start = calendar.getTime();
        SimpleDateFormat ymd = new SimpleDateFormat("yyyyMMddHH");
        return ymd.format(start);
    }

    /**
     * @param strDateFormat
     * @return
     * @function 返回指定格式当前日期, 格式:yyyyMMdd,如果指定格式报错,返回为标准格式yyyy-MM-dd
     */
    public static String getNowDateTime(String strDateFormat) {
        try {
            SimpleDateFormat ymd = new SimpleDateFormat(strDateFormat);
            return ymd.format(new Date());
        } catch (Exception e) {
            return DateTools.getNowDateTime("yyyy-MM-dd");
        }
    }

    /**
     * @return yyyy-MM-dd
     * @function 返回指定格式的字符串日期
     */
    public static String getNowDateTime() {
        return DateTools.getNowDateTime("yyyy-MM-dd");
    }

    /**
     * 获取当天最后一秒的时间,简陋写法
     *
     * @return
     */
    public static String getNowDateTimeLastSencond() {
        return DateTools.getNowDateTime("yyyy-MM-dd 23:59:59");
    }

    /**
     * 获取昨天最后一秒的时间,简陋写法
     *
     * @return
     */
    public static String getLastDateTimeLastSencond() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        Date d = cal.getTime();
        SimpleDateFormat sp = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        return sp.format(d);
    }

    /**
     * @param inputData
     * @param strDateFormat
     * @return
     * @function 返回指定格式指定日期, 格式:yyyyMMdd,如果指定格式报错,返回为标准格式
     */
    public static String getFormatDateTime(String inputData, String strDateFormat) {
        try {
            SimpleDateFormat ymd = new SimpleDateFormat(strDateFormat);
            return ymd.format(ymd.parse(inputData));
        } catch (Exception e) {
            return DateTools.getNowDateTime("yyyy-MM-dd");
        }
    }

    /**
     * @param inputData
     * @param strOldDateFormat
     * @param strDateFormat
     * @return
     * @function 返回指定格式的指定日期, 日期格式转化将strOldDateFormat格式转化为strDateFormat格式
     */
    public static String getFormatDateTimeNew(String inputData, String strOldDateFormat, String strDateFormat) {
        try {
            SimpleDateFormat ymd = new SimpleDateFormat(strOldDateFormat);
            SimpleDateFormat newymd = new SimpleDateFormat(strDateFormat);
            return newymd.format(ymd.parse(inputData));
        } catch (Exception e) {
            return DateTools.getNowDateTime("yyyy-MM-dd HH:mm:ss");
        }
    }

    /**
     * @param inputData
     * @return yyyy-MM-dd HH:mm:ss
     * @function 返回指定格式的指定日期
     */
    public static String getFormatDateTime(String inputData) {
        return getFormatDateTime(inputData, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * @param inputData
     * @return yyyy-MM
     * @function 返回指定格式的指定日期
     */
    public static String getFormatMonth(String inputData) {
        return getFormatDateTime(inputData, "yyyy-MM");
    }

    /**
     * @param inputData
     * @param oldChar
     * @param newChar
     * @return
     * @function 返回指定格式指定日期, 由old格式, 更新为new格式, 执行一个替换
     */
    public static String getFormatDate(String inputData, String oldChar, String newChar) {
        if (inputData == null) {
            return inputData;
        }
        return inputData.replace(oldChar, newChar);
    }

    /**
     * 返回当前周的第一天的日期,默认周一为每周的第一天
     *
     * @return
     */
    public static String startDayOfWeek() {
        Calendar calendar = Calendar.getInstance();
        int min = calendar.getActualMinimum(Calendar.DAY_OF_WEEK); // 获取周开始基准,给定此Calendar的时间值,返回指定日历字段可能拥有的最小值.
        int current = calendar.get(Calendar.DAY_OF_WEEK);// 返回给定日历字段的值.
        calendar.add(Calendar.DAY_OF_WEEK, min - (current - 1)); // 基准-当天,获取周开始日期.
        Date start = calendar.getTime();
        SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
        return ymd.format(start);
    }

    /**
     * 根据参数返回本周的某一天对应的日期,参数为1-7,否则返回为null。例如参数为1,则为本周周一对应的日期。
     *
     * @param intDay
     * @return
     */
    public static String strDayOfWeek(int intDay) {
        if (intDay > 0 && intDay < 8) {
            Calendar calendar = Calendar.getInstance();
            int min = calendar.getActualMinimum(Calendar.DAY_OF_WEEK); // 获取周开始基准,给定此Calendar的时间值,返回指定日历字段可能拥有的最小值.
            int current = calendar.get(Calendar.DAY_OF_WEEK);// 返回给定日历字段的值.
            if (current == 1) {
                calendar.set(Calendar.WEEK_OF_YEAR, calendar.get(Calendar.WEEK_OF_YEAR) - 1);
            }
            calendar.add(Calendar.DAY_OF_WEEK, min - (current - 1) + (intDay - 1)); // 基准-当天,获取周开始日期.
            Date start = calendar.getTime();
            SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
            return ymd.format(start);
        } else {
            return null;
        }
    }

    /**
     * 返回当前周的最后一天的日期,默认周一为每周的第一天
     *
     * @return
     */
    public static String endDayOfWeek() {
        Calendar calendar = Calendar.getInstance();
        int min = calendar.getActualMinimum(Calendar.DAY_OF_WEEK); // 获取周开始基准,给定此Calendar的时间值,返回指定日历字段可能拥有的最小值.
        int current = calendar.get(Calendar.DAY_OF_WEEK);// 返回给定日历字段的值.
        calendar.add(Calendar.DAY_OF_WEEK, min - (current - 1)); // 基准-当天,获取周开始日期.
        calendar.add(Calendar.DAY_OF_WEEK, 6); // 开始+6,获取周结束日期
        Date end = calendar.getTime();
        SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
        return ymd.format(end);
    }

    /**
     * 返回指定某一周周几对应的日期
     *
     * @param intWeek
     * @param intDay
     * @return
     */
    public static String strDayOfWeek(int intWeek, int intDay) {
        if (intDay > 0 && intDay < 8) {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.WEEK_OF_YEAR, intWeek);
            int min = calendar.getActualMinimum(Calendar.DAY_OF_WEEK); // 获取周开始基准,给定此Calendar的时间值,返回指定日历字段可能拥有的最小值.
            int current = calendar.get(Calendar.DAY_OF_WEEK);// 返回给定日历字段的值.
            calendar.add(Calendar.DAY_OF_WEEK, min - (current - 1) + (intDay - 1)); // 基准-当天,获取周开始日期.
            Date start = calendar.getTime();
            SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
            return ymd.format(start);
        } else {
            return null;
        }
    }

    /**
     * 返回当前月
     *
     * @return
     */
    public static String getCurrentMonth() {
        Calendar calendar = Calendar.getInstance();
        return (calendar.get(Calendar.MONTH) + 1) + "";
    }

    /**
     * 按格式返回当前月加减N个月的结果,当月则传0
     */
    public static String nMonthAfterCurrentMonth(String strDateFormat, int i) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, i);
        Date rst = calendar.getTime();
        SimpleDateFormat ymd = new SimpleDateFormat(strDateFormat);
        return ymd.format(rst);
    }

    /**
     * 返回当前月
     *
     * @return
     */
    public static int getIntCurrentMonth() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 返回上个月
     * @return
     */
    public static String lastMonth(){
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date); // 设置为当前时间
        calendar.add(Calendar.MONTH,-1);
        date = calendar.getTime();
        return format.format(date);
    }

    /**
     * 返回当天
     *
     * @return
     */
    public static int getCurrentDay() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 返回当前年
     *
     * @return
     */
    public static int getCurrentYear() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.YEAR);
    }

    /**
     * idm返回当前年
     *
     * @return
     */
    public static String idmGetCurrentYear() {
        Calendar calendar = Calendar.getInstance();
        return "" + calendar.get(Calendar.YEAR);
    }

    /**
     * idm返回当前月
     *
     * @return
     */
    public static String idmGetIntCurrentMonth() {
        Calendar calendar = Calendar.getInstance();
        int monthInt = calendar.get(Calendar.MONTH) + 1;
        String month = null;
        if (monthInt<10){
            month = "0"+monthInt;
        }else {
            month = ""+monthInt;
        }
        return month;
    }

    /**
     * idm返回当天
     *
     * @return
     */
    public static String idmGetCurrentDay() {
        Calendar calendar = Calendar.getInstance();
        int dayInt = calendar.get(Calendar.DAY_OF_MONTH);
        String day = null;
        if (dayInt<10){
            day = "0"+dayInt;
        }else {
            day = ""+dayInt;
        }
        return day;
    }

    /**
     * idm返回昨天
     * @return
     */
    public static String idmGetLastDay(){
        Calendar cal=Calendar.getInstance();
        cal.add(Calendar.DATE,-1);
        Date d=cal.getTime();
        SimpleDateFormat sp=new SimpleDateFormat("yyyyMMdd");
        return sp.format(d);
    }

    /**
     * idm返回上个月结束
     * @return
     */
    public static String idmGetLastMonth(){
        Calendar cal=Calendar.getInstance();
        cal.add(Calendar.MONTH,-1);
        Date d=cal.getTime();
        SimpleDateFormat sp=new SimpleDateFormat("yyyyMM");
        return sp.format(d);
    }


    /**
     * 根据参数返回某年某月的某一天对应的日期。
     *
     * @param year ,month,intDay
     * @return
     */
    public static String strDayOfMonth(int year, int month, int intDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        calendar.set(Calendar.DAY_OF_MONTH, intDay);
        Date start = calendar.getTime();
        SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
        return ymd.format(start);
    }

    /**
     * 根据参数返回某年某月的第一天对应的日期。
     *
     * @param year
     * @param month
     * @return
     */
    public static String startDayOfMonth(int year, int month) {
        SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date beginTime = calendar.getTime();

        return ymd.format(beginTime);
    }

    /**
     * 根据参数返回某年某月的最后一天对应的日期。
     *
     * @param year
     * @param month
     * @return
     */
    public static String endDayOfMonth(int year, int month) {
        SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        calendar.set(Calendar.DATE, 1);
        calendar.roll(Calendar.DATE, -1);
        Date endTime = calendar.getTime();
        return ymd.format(endTime);
    }

    /**
     * 获取当前周,周的第一天为周一
     *
     * @return
     */
    public static int getCurrentWeek() {
        int currentWeek = 0;
        if (Calendar.getInstance().get(Calendar.DAY_OF_WEEK) == 1)
            currentWeek = (Calendar.getInstance().get(Calendar.WEEK_OF_YEAR) - 1);
        else
            currentWeek = Calendar.getInstance().get(Calendar.WEEK_OF_YEAR);
        return currentWeek;
    }

    /**
     * 当前日期加减n天后的日期,返回String (yyyy-mm-dd)
     *
     * @param n
     * @return
     */
    public static String nDaysAftertoday(int n) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Calendar rightNow = Calendar.getInstance();
        rightNow.add(Calendar.DAY_OF_MONTH, +n);
        return df.format(rightNow.getTime());
    }

    /**
     * 当前日期加减n天后的日期,返回String (yyyy-mm-dd hh:mm)
     *
     * @param n
     * @return
     */
    public static String nDaysAftertoday2(int n) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm");
        Calendar rightNow = Calendar.getInstance();
        rightNow.add(Calendar.DAY_OF_MONTH, +n);
        return df.format(rightNow.getTime());
    }

    /**
     * 当前日期加减n天后的日期,返回String (yyyy-mm-dd)
     *
     * @param n
     * @return
     */
    public static Date nDaysAfterNowDate(int n) {
        Calendar rightNow = Calendar.getInstance();
        rightNow.add(Calendar.DAY_OF_MONTH, +n);
        return rightNow.getTime();
    }

    /**
     * 给定一个日期型字符串,返回加减n天后的日期型字符串
     *
     * @param n
     * @return
     */
    public static String nDaysAfterOneDateString(String basicDate, int n) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date tmpDate = null;
        try {
            tmpDate = df.parse(basicDate);
        } catch (Exception e) {
            // 日期型字符串格式错误
        }
        long nDay = (tmpDate.getTime() / (24 * 60 * 60 * 1000) + 1 + n) * (24 * 60 * 60 * 1000);
        tmpDate.setTime(nDay);

        return df.format(tmpDate);
    }

    /**
     * 给定一个日期型字符串,返回当天0秒时间 yyyy-MM-dd --》yyyy-MM-dd 00:00:00
     */
    public static String getOneDayZeroSecond(String basicDate) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date tmpDate = null;
        try {
            tmpDate = df.parse(basicDate);
        } catch (Exception e) {
            // 日期型字符串格式错误
        }
        tmpDate.setHours(0);
        tmpDate.setMinutes(0);
        tmpDate.setSeconds(0);
        return df2.format(tmpDate);
    }

    /**
     * 给定一个日期型字符串,返回当天0秒时间 yyyy-MM-dd --》yyyy-MM-dd 23:59:59
     */
    public static String getOneDayLastSecond(String basicDate) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date tmpDate = null;
        try {
            tmpDate = df.parse(basicDate);
        } catch (Exception e) {
            // 日期型字符串格式错误
        }
        tmpDate.setHours(23);
        tmpDate.setMinutes(59);
        tmpDate.setSeconds(59);
        return df2.format(tmpDate);
    }

    /**
     * 给定一个日期,返回加减n天后的日期
     *
     * @param basicDate
     * @param n
     * @return
     */
    public static Date nDaysAfterOneDate(Date basicDate, int n) {
        long nDay = (basicDate.getTime() / (24 * 60 * 60 * 1000) + 1 + n) * (24 * 60 * 60 * 1000);
        basicDate.setTime(nDay);

        return basicDate;
    }

    /**
     * 计算两个日期相隔的天数 ,日期期
     *
     * @param firstDate
     * @param secondDate
     * @return
     */
    public static int nDaysBetweenTwoDate(Date firstDate, Date secondDate) {
        int nDay = (int) ((secondDate.getTime() - firstDate.getTime()) / (24 * 60 * 60 * 1000));
        return nDay;
    }

    /**
     * 计算两个日期相隔的天数
     *
     * @param firstString
     * @param secondString
     * @return
     */
    public static int nDaysBetweenTwoDate(String firstString, String secondString) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date firstDate = null;
        Date secondDate = null;
        try {
            firstDate = df.parse(firstString);
            secondDate = df.parse(secondString);
        } catch (Exception e) {
            // 日期型字符串格式错误
        }
        int nDay = (int) ((secondDate.getTime() - firstDate.getTime()) / (24 * 60 * 60 * 1000));
        return nDay;
    }

    /**
     * <p>
     * description: 计算年龄
     * </p>
     *
     * @param birthday
     * @return -1 入参为空; -2 入参格式错误; >= 0 正常
     */
    public static int calAge(String birthday) {
        if (StringUtils.isBlank(birthday)) {
            return -1;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date birth = sdf.parse(birthday);
            Calendar c = Calendar.getInstance();
            int thisYear = c.get(Calendar.YEAR) - 1900;
            int birthYear = birth.getYear();
            birth.setYear(thisYear);
            if (birth.after(c.getTime())) {
                return thisYear - birthYear - 1;
            } else {
                return thisYear - birthYear;
            }
        } catch (ParseException e) {
            e.printStackTrace();
            return -2;
        }
    }

    /**
     * 获取今年开始时间
     *
     * @return {@link String }
     * <AUTHOR>
     * @create 2022-11-02
     */
    public static String getYearStartTime() {

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime yearStart = now.with(TemporalAdjusters.firstDayOfYear()).with(LocalTime.MIN);

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dtf.format(yearStart);
    }

    /**
     * 获取今年结束时间
     *
     * @return {@link String }
     * <AUTHOR>
     * @create 2022-11-02
     */
    public static String getYearEndTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime yearEnd = now.with(TemporalAdjusters.lastDayOfYear()).with(LocalTime.MAX);

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dtf.format(yearEnd);
    }

    /**
     * 获取当月开始时间
     *
     * @return {@link String }
     * <AUTHOR>
     * @create 2022-11-02
     */
    public static String getMonthStartTime() {

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime monthStart = now.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dtf.format(monthStart);
    }

    /**
     * 获取当月结束时间
     *
     * @return {@link String }
     * <AUTHOR>
     * @create 2022-11-02
     */
    public static String getMonthEndTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime monthEnd = now.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dtf.format(monthEnd);
    }

    /**
     * 获取今天开始时间
     *
     * @return {@link String }
     * <AUTHOR>
     * @create 2022-11-02
     */
    public static String getDayStartTime() {

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime dayStart = now.with(LocalTime.MIN);

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dtf.format(dayStart);
    }

    /**
     * 获取今天结束时间
     *
     * @return {@link String }
     * <AUTHOR>
     * @create 2022-11-02
     */
    public static String getDayEndTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime dayEnd = now.with(LocalTime.MAX);

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dtf.format(dayEnd);
    }

    /**
     * 获取开始和结束日期
     *
     * @param loginTimeStart 登录时间str
     * @return {@link Map }<{@link String }, {@link Date }>
     * <AUTHOR>
     * @create 2022-11-02
     */
    public static Map<String, Date> getStartAndEndDate(String loginTimeStart, String loginTimeEnd) {

        Assert.notNull(loginTimeStart, "loginTimeStr cannot be null");
        if (loginTimeStart.length() !=4 && loginTimeStart.length() != 7 && loginTimeStart.length() != 10) {
            throw new IllegalArgumentException("请输入年或年月或年月日（格式:yyyy-MM-dd）");
        }
        if (loginTimeEnd.length() !=4 && loginTimeEnd.length() != 7 && loginTimeEnd.length() != 10) {
            throw new IllegalArgumentException("请输入年或年月或年月日（格式:yyyy-MM-dd）");
        }
        String loginTimeStartStr = "";
        String loginTimeEndStr = "";
        if (loginTimeStart.length() == 4) {// 年
            loginTimeStartStr = loginTimeStart + "-01-01 00:00:00";
            loginTimeEndStr = loginTimeEnd + "-12-31 23:59:59";
        }
        if (loginTimeStart.length() == 7) {// 月
            loginTimeStartStr = loginTimeStart + "-01 00:00:00";
            loginTimeEndStr = loginTimeEnd + "-31 23:59:59";
        }
        if (loginTimeStart.length() == 10) {// 日
            loginTimeStartStr = loginTimeStart + " 00:00:00";
            loginTimeEndStr = loginTimeEnd + " 23:59:59";
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date loginTimeStartDate = null;
        Date loginTimeEndDate = null;
        try {
            loginTimeStartDate = sdf.parse(loginTimeStartStr);
            loginTimeEndDate = sdf.parse(loginTimeEndStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Assert.notNull(loginTimeStartDate, "开始时间转换异常");
        Assert.notNull(loginTimeEndDate, "结束时间转换异常");

        Map<String, Date> map = new HashMap<>();
        map.put("loginTimeStart", loginTimeStartDate);
        map.put("loginTimeEnd", loginTimeEndDate);
        return map;
    }

    public static Map<String, String> getStartAndEndDateStr(String loginTimeStart, String loginTimeEnd) {

        Assert.notNull(loginTimeStart, "loginTimeStr cannot be null");
        if (loginTimeStart.length() !=4 && loginTimeStart.length() != 7 && loginTimeStart.length() != 10) {
            throw new IllegalArgumentException("请输入年或年月或年月日（格式:yyyy-MM-dd）");
        }
        if (loginTimeEnd.length() !=4 && loginTimeEnd.length() != 7 && loginTimeEnd.length() != 10) {
            throw new IllegalArgumentException("请输入年或年月或年月日（格式:yyyy-MM-dd）");
        }
        String loginTimeStartStr = "";
        String loginTimeEndStr = "";
        if (loginTimeStart.length() == 4) {// 年
            loginTimeStartStr = loginTimeStart + "-01-01 00:00:00";
            loginTimeEndStr = loginTimeEnd + "-12-31 23:59:59";
        }
        if (loginTimeStart.length() == 7) {// 月
            loginTimeStartStr = loginTimeStart + "-01 00:00:00";
            loginTimeEndStr = loginTimeEnd + "-31 23:59:59";
        }
        if (loginTimeStart.length() == 10) {// 日
            loginTimeStartStr = loginTimeStart + " 00:00:00";
            loginTimeEndStr = loginTimeEnd + " 23:59:59";
        }

        Map<String, String> map = new HashMap<>();
        map.put("loginTimeStart", loginTimeStartStr);
        map.put("loginTimeEnd", loginTimeEndStr);
        return map;
    }

    /**
     * 获取开始日期
     *
     * @param dateString 日期字符串 "yyyy-MM-dd"
     * @return {@link String }
     * <AUTHOR>
     * @create 2023-03-27
     */
    public static String getDayStartTime(String dateString) {

        LocalDate  localDate  =  LocalDate.parse(dateString);

        int  year  =  localDate.getYear();  //  获取年份
        int  month  =  localDate.getMonthValue();  //  获取月份
        int  day  =  localDate.getDayOfMonth();  //  获取天

        LocalDateTime localDateTime = LocalDateTime.of(year, month, day, 0, 0, 0);
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        String formatStart = localDateTime.format(fmt);
        return formatStart;
    }

    /**
     * 获取结束日期
     *
     * @param dateString 日期字符串 "yyyy-MM-dd"
     * @return {@link String }
     * <AUTHOR>
     * @create 2023-03-27
     */
    public static String getDayEndTime(String dateString) {

        LocalDate  localDate  =  LocalDate.parse(dateString);

        int  year  =  localDate.getYear();  //  获取年份
        int  month  =  localDate.getMonthValue();  //  获取月份
        int  day  =  localDate.getDayOfMonth();  //  获取天

        LocalDateTime localDateTime = LocalDateTime.of(year, month, day, 23, 59, 59);
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        String formatStart = localDateTime.format(fmt);
        return formatStart;
    }

}
