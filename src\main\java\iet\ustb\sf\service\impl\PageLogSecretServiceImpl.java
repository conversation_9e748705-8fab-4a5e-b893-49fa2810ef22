package iet.ustb.sf.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import iet.ustb.sf.dao.PageLogSecretDao;
import iet.ustb.sf.dao.RoleDao;
import iet.ustb.sf.dao.UserDao;
import iet.ustb.sf.vo.domain.*;
import iet.ustb.sf.service.*;
import iet.ustb.sf.utils.DateTools;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.utils.UserThreadUtil;
import iet.ustb.sf.vo.eo.*;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.Cleanup;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 页面日志
 *
 * <AUTHOR>
 * @create 2022-09-22
 * @see PageLogService
 */
@Service
@CommonsLog
public class PageLogSecretServiceImpl implements PageLogSecretService {

    @Autowired
    private PageLogSecretDao pageLogSecretDao;
    @Autowired
    private LoginLogService loginLogService;
//    @LoadBalanced
    @Resource
    private RestTemplate restTemplate;
    @Autowired
    private OrgService orgService;
    @Autowired
    private FeedBackService feedBackService;
    @Autowired
    private ResourceService resourceService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private RoleDao roleDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private DictionaryDtlService dictionaryDtlService;

    @Override
    public List<PageLogSecret> findAll() {
        return pageLogSecretDao.findAll();
    }

    @Override
    public List<PageLogSecret> findAllByMultiCondition(JSONObject jsonObject) {
        List<PageLogSecret> pageLogList = pageLogSecretDao.findAll(createSpecs(jsonObject));
        return pageLogList;
    }

    @Override
    public Page<PageLogSecret> findByMultiCondition(JSONObject jsonObject) {

        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<PageLogSecret> pages = pageLogSecretDao.findAll(createSpecs(jsonObject), pageable);
        return pages;
    }

    @Override
    public List<AccessNumEO> findAccessNumByLoginTime(JSONObject jsonObj) {
        String loginTimeStart = jsonObj.getString("loginTimeStart");
        String loginTimeEnd = jsonObj.getString("loginTimeEnd");
        String type = jsonObj.getString("type");

        Map<String, Date> map = DateTools.getStartAndEndDate(loginTimeStart, loginTimeEnd);
        Date loginTimeStartDate = map.get("loginTimeStart");
        Date loginTimeEndDate = map.get("loginTimeEnd");

        List<AccessNumEO> accessNumEOList = new ArrayList<>();
        if (StringUtils.isNotBlank(type) && type.startsWith("org")) {
            // 查询组织访问次数
//            List<AccessNumEO> accessNumEOS = pageLogSecretDao.findGroupOrgByLoginTime(loginTimeStartDate, loginTimeEndDate);
            // 层级：1-厂处 2-科室 3-班组
            Integer level = Integer.parseInt(type.substring(type.length() - 1));
            accessNumEOList = this.calcOrgNum(loginTimeStart, loginTimeEnd).stream()
                    .filter(eo -> eo.getLevel() != null && eo.getLevel().equals(level))
                    .map(eo -> {
                        eo.setName(eo.getName().replace("板材事业部", ""));
                        return eo;
                    })
                    .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                    .collect(Collectors.toList());
        }
        if ("user".equals(type)) {
            // 查询应用用户次数
            accessNumEOList = pageLogSecretDao.findGroupUserByLoginTime(loginTimeStartDate, loginTimeEndDate)
                    .stream()
                    .filter(eo -> StringUtils.isNotBlank(eo.getOrgName()))
                    .collect(Collectors.toList());
        }
        if("app".equals(type)) {
            // 查询应用访问次数
            accessNumEOList = pageLogSecretDao.findGroupAppByLoginTime(loginTimeStartDate, loginTimeEndDate)
                    .stream()
                    .filter(accessNumEO -> !accessNumEO.getCode().contains("old") && !"static".equals(accessNumEO.getCode()))
                    .collect(Collectors.toList());
            // 写入应用名称
            accessNumEOList = this.writeServiceName(accessNumEOList);
        }
        return accessNumEOList;
    }

    @Override
    public void exportUserExcel(JSONObject jsonObject, HttpServletResponse response) throws Exception {
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("用户访问记录");
        // 设置sheet表头名称
        exportParams.setSheetName("用户访问记录");
        // 查找所有
        List<UserAccessNumEO> list = this.findUserPageAccessList(jsonObject).getContent();
        List<UserAccessNumEO> eoList = new ArrayList<>(list);
        // 执行方法
        @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(exportParams, UserAccessNumEO.class, eoList);
        ToolsUtil.downLoadExcel("用户访问记录", response, workBook);
    }

    @Override
    public void exportUserDetExcel(JSONObject jsonObject, HttpServletResponse response) throws Exception {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");

        ExportParams exportParams = new ExportParams();
        exportParams.setTitle("用户访问记录");
        exportParams.setSheetName("用户访问记录");

        Map<String, Object>  oneExportMap = new HashMap<>();
        oneExportMap.put("title", exportParams);
        oneExportMap.put("entity", UserAccessNumEO.class);
        List<UserAccessNumEO> userAccessList = this.findUserPageAccessList(jsonObject).getContent();
        List<UserAccessNumEO> eoList = new ArrayList<>(userAccessList);
        oneExportMap.put("data", eoList);

        exportParams = new ExportParams();
        exportParams.setTitle("用户详细访问记录");
        exportParams.setSheetName("用户详细访问记录");

        Map<String, Object> twoExportMap = new HashMap<>();
        twoExportMap.put("title", exportParams);
        twoExportMap.put("entity", UserDetAccessNumEO.class);
        // 查找所有
        List<UserDetAccessNumEO> userDetAccessList = this.findUserDetPageAccessList(jsonObject);
        List<UserDetAccessNumEO> detEoList = new ArrayList<>(userDetAccessList);
        twoExportMap.put("data", detEoList);

        // 将sheet使用得map进行包装
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        sheetsList.add(oneExportMap);
        sheetsList.add(twoExportMap);

        // 执行方法
        @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
        //设置编码格式
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        //设置内容类型
        response.setContentType("application/octet-stream");
        //设置头及文件命名。
        response.setHeader("Content-Disposition", "attachment;filename="
                + URLEncoder.encode("用户详细访问记录("+loginTimeStart
                +"_"+loginTimeEnd+").xls", StandardCharsets.UTF_8.name()));
        //写出流
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        workBook.write(outputStream);
    }

    @Override
    public void exportPageExcel(JSONObject jsonObject, HttpServletResponse response) throws Exception {
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("页面访问记录");
        // 设置sheet表头名称
        exportParams.setSheetName("页面访问记录");
        // 查找所有
        List<PageAccessNumEO> list = this.findEachPageAccessList(jsonObject).getContent();
        List<PageAccessNumEO> eoList = new ArrayList<>(list);
        // 执行方法
        @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(exportParams, PageAccessNumEO.class, eoList);
        ToolsUtil.downLoadExcel("页面访问记录", response, workBook);
    }

    @Override
    public JSONObject exportWord(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");

        Map<String, Date> map = DateTools.getStartAndEndDate(loginTimeStart, loginTimeEnd);
        Date loginTimeStartDate = map.get("loginTimeStart");
        Date loginTimeEndDate = map.get("loginTimeEnd");

        JSONObject json = new JSONObject();
        // 汇总所有使用人数和访问次数
        List<AccessNumEO> accessNumEOList = pageLogSecretDao.findSummaryNumByLoginTime(loginTimeStartDate, loginTimeEndDate);
        json.put("summary", accessNumEOList);

        // 查找组织使用人数和访问次数
        accessNumEOList = pageLogSecretDao.findGroupOrgByLoginTime(loginTimeStartDate, loginTimeEndDate);
        // 计算组织及所有子组织人数之和
        accessNumEOList = this.calcOrgNum(loginTimeStart, loginTimeEnd);
        // 获取厂处数据
        List<AccessNumEO> dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 1)
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        json.put("org-1", dataList);

        // 获取科室数据
        dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 2)
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        json.put("org-2", dataList);

        // 获取班组数据
        dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 3)
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        json.put("org-3", dataList);


        // 查询用户访问次数
        dataList = pageLogSecretDao.findGroupUserByLoginTime(loginTimeStartDate, loginTimeEndDate).stream()
                .map(eo -> {
                    eo.setOrgName(eo.getOrgName().replace("板材事业部", ""));
                    return eo;
                }).collect(Collectors.toList());
        json.put("user", dataList);


        // 查询应用访问次数
        dataList = pageLogSecretDao.findGroupAppByLoginTime(loginTimeStartDate, loginTimeEndDate)
                .stream()
                .filter(accessNumEO -> !accessNumEO.getCode().contains("old") && !"static".equals(accessNumEO.getCode()))
                .collect(Collectors.toList());
        dataList = this.writeServiceName(dataList);
        json.put("app", dataList);

        Integer recomHandledNum  = 0;
        Integer bugHandledNum  = 0;
        Integer recomUnHandledNum = 0;
        Integer bugUnHandledNum = 0;
        // 查询问题反馈和合理化建议处理数
        List<Integer> statusList = Arrays.asList(2, 4);
        List<Map<String, Integer>> handledNumList = feedBackService.findCountByStatus(statusList, loginTimeStartDate, loginTimeEndDate);
        for (Map<String, Integer> handledNumMap : handledNumList) {
            if (handledNumMap.get("quesType") == 1) {// 合理化建议
                String num = String.valueOf(handledNumMap.get("num"));
                recomHandledNum = Integer.parseInt(num);
            }
            if (handledNumMap.get("quesType") == 2) {// 问题反馈
                String num = String.valueOf(handledNumMap.get("num"));
                bugHandledNum = Integer.parseInt(num);
            }
        }

        statusList = Arrays.asList(1, 3);
        List<Map<String, Integer>> unHandledNumList = feedBackService.findCountByStatus(statusList, loginTimeStartDate, loginTimeEndDate);
        for (Map<String, Integer> unHandledNumMap : unHandledNumList) {
            if (unHandledNumMap.get("quesType") == 1) {// 合理化建议
                String num = String.valueOf(unHandledNumMap.get("num"));
                recomUnHandledNum = Integer.parseInt(num);
            }
            if (unHandledNumMap.get("quesType") == 2) {// 问题反馈
                String num = String.valueOf(unHandledNumMap.get("num"));
                bugUnHandledNum = Integer.parseInt(num);
            }
        }

        JSONObject jsonObj1 = new JSONObject();
        jsonObj1.put("handledNum", recomHandledNum);
        jsonObj1.put("unHandledNum", recomUnHandledNum);
        jsonObj1.put("totalNum", recomHandledNum + recomUnHandledNum);
        json.put("feedBack-recom", jsonObj1);

        JSONObject jsonObj2 = new JSONObject();
        jsonObj2.put("handledNum", bugHandledNum);
        jsonObj2.put("unHandledNum", bugUnHandledNum);
        jsonObj2.put("totalNum", bugHandledNum + bugUnHandledNum);
        json.put("feedBack-bug", jsonObj2);

        return json;
    }

    /**
     * 计算组织及所有子组织人数之和
     *
     * @param loginTimeStart 登录时间开始
     * @param loginTimeEnd   登录时间结束
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-10
     */
    /*private List<AccessNumEO> calcOrgNum(String loginTimeStart, String loginTimeEnd) {
        Map<String, String> map2 = DateTools.getStartAndEndDateStr(loginTimeStart, loginTimeEnd);
        loginTimeStart = map2.get("loginTimeStart");
        loginTimeEnd = map2.get("loginTimeEnd");

        List<AccessNumEO> eoNewList = new ArrayList<>();
        List<AccessNumEO> list = new ArrayList<>();
        List<Map<String, Object>> listNew = pageLogSecretDao.findOrgUsers(loginTimeStart, loginTimeEnd);
        Map<String, Object> mapNew;
        for (Map<String, Object> map : listNew) {
            mapNew = new HashMap<>();
            mapNew.put("code", (String) map.get("orgCode"));
            mapNew.put("name", (String) map.get("orgAllName"));
            mapNew.put("parentCode", (String) map.get("parentOrgCode"));
            mapNew.put("orgNum", ((BigInteger)map.get("orgNum")).longValue());
            mapNew.put("useNum", ((BigInteger)map.get("useNum")).longValue());
            mapNew.put("num", ((BigInteger)map.get("num")).longValue());
            AccessNumEO eo = JSON.parseObject(JSON.toJSONString(mapNew), AccessNumEO.class);
            list.add(eo);
        }

        for (AccessNumEO accessNumEO : list) {
            Long orgNum = 0L;
            Long useNum = 0L;
            Long num = 0L;
            if ("*********".equals(accessNumEO.getParentCode())) {
                String code = accessNumEO.getCode();
                orgNum += accessNumEO.getOrgNum() != null ? accessNumEO.getOrgNum() : 0;
                num += accessNumEO.getNum() != null ? accessNumEO.getNum() : 0;
                for (AccessNumEO eo : list) {
                    Long orgNum1 = 0L;
                    Long useNum1 = 0L;
                    Long num1 = 0L;
                    if (code.equals(eo.getParentCode())) {
                        String code1 = eo.getCode();
                        orgNum += eo.getOrgNum() != null ? eo.getOrgNum() : 0;
                        orgNum1 += eo.getOrgNum() != null ? eo.getOrgNum() : 0;
                        useNum += eo.getUseNum() != null ? eo.getUseNum() : 0;
                        useNum1 += eo.getUseNum() != null ? eo.getUseNum() : 0;
                        num += eo.getNum() != null ? eo.getNum() : 0;
                        num1 += eo.getNum() != null ? eo.getNum() : 0;

                        for (AccessNumEO eo2 : list) {
                            if (code1.equals(eo2.getParentCode())) {
                                orgNum += eo2.getOrgNum() != null ? eo2.getOrgNum() : 0;
                                orgNum1 += eo2.getOrgNum() != null ? eo2.getOrgNum() : 0;
                                useNum += eo2.getUseNum() != null ? eo2.getUseNum() : 0;
                                useNum1 += eo2.getUseNum() != null ? eo2.getUseNum() : 0;
                                num += eo2.getNum() != null ? eo2.getNum() : 0;
                                num1 += eo2.getNum() != null ? eo2.getNum() : 0;

                                Long orgNum2 = eo2.getOrgNum();
                                Long useNum2 = eo2.getUseNum();
                                eo2.setOrgNum(orgNum2);
                                eo2.setUseNum(useNum2);
                                eo2.setNum(eo2.getNum());
                                BigDecimal rate = orgNum2 != 0 ? new BigDecimal(useNum2).multiply(new BigDecimal(100))
                                        .divide(new BigDecimal(orgNum2),0, BigDecimal.ROUND_HALF_UP)
                                        : BigDecimal.ZERO;
                                eo2.setRate(rate);
                                eo2.setLevel(3);
                                eoNewList.add(eo2);
                            }
                        }
                        eo.setOrgNum(orgNum1);
                        eo.setUseNum(useNum1);
                        eo.setNum(num1);
                        BigDecimal rate = orgNum1 != 0 ? new BigDecimal(useNum1).multiply(new BigDecimal(100))
                                .divide(new BigDecimal(orgNum1),0, BigDecimal.ROUND_HALF_UP)
                                : BigDecimal.ZERO;
                        eo.setRate(rate);
                        eo.setLevel(2);
                        eoNewList.add(eo);
                    }
                }
                accessNumEO.setOrgNum(orgNum);
                accessNumEO.setUseNum(useNum);
                accessNumEO.setNum(num);
                BigDecimal rate = orgNum != 0 ? new BigDecimal(useNum).multiply(new BigDecimal(100))
                        .divide(new BigDecimal(orgNum),0, BigDecimal.ROUND_HALF_UP)
                        : BigDecimal.ZERO;
                accessNumEO.setRate(rate);
                accessNumEO.setLevel(1);
                eoNewList.add(accessNumEO);
            }
        }
        return eoNewList;
    }*/

    /**
     * 写入应用名称
     *
     * @param accessNumEOList
     * <AUTHOR>
     * @create 2022-11-01
     * @return
     */
    private List<AccessNumEO> writeServiceName(List<AccessNumEO> accessNumEOList) {
        // 数据中台 数据字典 可获取应用名称
//        String url = "http://************:9800/idm/basicDataConfig/findBasicDataConfigByType.form";
////        String url = "http://iet-idm-service/basicDataConfig/findBasicDataConfigByType.form";
//
//        JSONObject json = new JSONObject();
//        json.put("type", "serviceInfo");
//        JSONObject resJson = restTemplate.postForObject(url, json, JSONObject.class);
//        BasicDataConfig basicDataConfig=basicDataConfigService.findBDCbyType("serviceInfo");
//        String content=basicDataConfig.getContent();
////        String content = resJson.getString("content");
//        JSONArray jsonArray = JSONObject.parseArray(content);
//        List<Map<String, String>> serviceList = new ArrayList<>();
//        for (int i = 0; i < jsonArray.size(); i++) {
//            String obj = jsonArray.getString(i);
//            JSONObject jsonObj = JSONObject.parseObject(obj);
//
//            Map<String, String> map = new HashMap<>();
//            map.put("resourceId", jsonObj.getString("name"));
//            map.put("serviceName", jsonObj.getString("cname"));
//            serviceList.add(map);
//        }


        List<DictionaryDtl> dictionaryDtlList= dictionaryDtlService.findByDictId("e38d0f03-3755-42ba-a17a-d078f01cfab3");
        List<Map<String, String>> serviceList = new ArrayList<>();

        JSONArray jsonArray =new JSONArray();
        for(int i = 0; i < dictionaryDtlList.size(); i++){
            JSONObject jsonObj =JSONObject.parseObject(dictionaryDtlList.get(i).getValue());
            Map<String, String> map = new HashMap<>();
            map.put("PORT",jsonObj.getString("PORT").toString());
            map.put("CODE",dictionaryDtlList.get(i).getCode());
            map.put("IP",jsonObj.getString("IP").toString());
            map.put("NAME",jsonObj.getString("NAME").toString());
            serviceList.add(map);
        }


        for (AccessNumEO accessNumEO : accessNumEOList) {
            String resourceId = accessNumEO.getCode();
            for (Map<String, String> map : serviceList) {
                if (map.get("CODE").equals(resourceId)) {
                    accessNumEO.setName(map.get("NAME"));
                    break;
                }
            }
        }
        // 过滤掉应用名称不存在的应用
        accessNumEOList = accessNumEOList.stream()
                .filter(accessNum -> StringUtils.isNotBlank(accessNum.getName()))
                .collect(Collectors.toList());
        return accessNumEOList;
    }

    @Override
    public void save(PageLogSecret pageLogSecret, HttpServletRequest request) {
        String resourceId = pageLogSecret.getResourceId();
        Assert.hasText(resourceId, "资源id不能为空");

        User user = UserThreadUtil.getUser();
        Assert.notNull(user, "该员工编号不存在");
        String userNo = user.getUserNo();

//        // 开发人员不保存应用访问记录
//        if ("dev".equals(user.getRemarks())) {
//            return;
//        }

        boolean flag;
        long currentTimeMillis = System.currentTimeMillis();
        String key = "pageLogSecret_" + userNo;
        Object object = redisTemplate.opsForHash().get(key, resourceId);
        if (object != null) {
            long lastAccessMillis = Long.valueOf((String) object);
            long diffMills = currentTimeMillis - lastAccessMillis;

            if (diffMills > 2 * 3600 * 1000) {// 相同页面访问间隔大于2小时
                // 是否距上次访问超过10秒
                flag = this.isMore10s(key, currentTimeMillis);
            } else {
                // 2小时内不保存页面访问记录
                return;
            }
        } else {
            // 第一次访问
            // 是否距上次访问超过10秒
            flag = this.isMore10s(key, currentTimeMillis);
        }
        if (flag) {
            pageLogSecret.setUserNo(userNo);
            pageLogSecret.setLoginType(0);// pc
            String orgCode = user.getOrgCode();
            pageLogSecret.setOrgCode(orgCode);// 组织编号
            pageLogSecretDao.save(pageLogSecret);
        }


    }

    /**
     * 是否距上次访问超过10秒
     *
     * @param key               键
     * @param currentTimeMillis 当前时间毫秒
     * @return boolean
     * <AUTHOR>
     * @create 2023-12-15
     */
    private boolean isMore10s(String key, long currentTimeMillis) {
        boolean flag = false;
        // 获取该用户所有页面访问时间戳
        List<Object> list = redisTemplate.opsForHash().values(key);
        // 获取该用户最近一次的访问时间戳
        long recentAccessTime = list.stream()
                .map(e -> Long.valueOf((String)e))
                .sorted(Comparator.reverseOrder())
                .findFirst().orElse(0L);
        long diffTime = currentTimeMillis - recentAccessTime;
        if (diffTime > 10 * 1000) {// 不同页面访问间隔大于10秒
            flag = true;
        }
        return flag;
    }

    /**
     * 获取厂处组织编号
     *
     * @param orgCode 组织编号
     * @return {@link String }
     * <AUTHOR>
     * @create 2022-11-03
     */
    private Map<String, Object> getOneOrgCode(String orgCode, Integer level) {
        Org org = orgService.findByOrgCode(orgCode);
        String parentOrgCode = org.getParentOrgCode();
        Map<String, Object> map = new HashMap<>();
        if ("*********".equals(parentOrgCode)) {// 父级是板材事业部
            map.put("orgCode", orgCode);
            map.put("level", level);
            return map;
        } else if ("X".equals(parentOrgCode)) {// 根级是总公司
            if ("*********".equals(orgCode)) {// 板材事业部
                map.put("level", 0);
            } else {// 与板材事业部同级的其他部门
                map.put("level", -1);
            }
            map.put("orgCode", "X");
            return map;
        } else {
            return getOneOrgCode(parentOrgCode, level+1);
        }
    }

    private Specification<PageLogSecret> createSpecs(JSONObject json) {
        Specification<PageLogSecret> specs = (root, query, cb) -> {
            List<Predicate> list = new LinkedList<>();

            String resourceId = json.getString("resourceId");// 资源id
            String userNo = json.getString("userNo");// 员工编号
            String orgCode = json.getString("orgCode");// 组织编号
            String loginTimeStart = json.getString("loginTimeStart");//起始日期
            String loginTimeEnd = json.getString("loginTimeEnd");//结束日期

            if (StringUtils.isNotBlank(resourceId)) {
                list.add(cb.equal(root.get("resourceId"), resourceId));
            }
            if (StringUtils.isNotBlank(userNo)) {
                list.add(cb.equal(root.get("userNo"), userNo));
            }
            if (StringUtils.isNotBlank(orgCode)) {
                list.add(cb.equal(root.get("orgCode"), orgCode));
            }
            if (StringUtils.isNotBlank(loginTimeStart)) {
                list.add(cb.greaterThanOrEqualTo(root.get("loginTime").as(String.class), loginTimeStart));
            }
            if (StringUtils.isNotBlank(loginTimeEnd)) {
                list.add(cb.lessThanOrEqualTo(root.get("loginTime").as(String.class), loginTimeEnd));
            }
            return query.where(list.toArray(new Predicate[list.size()])).getRestriction();
        };
        return specs;
    }

    @Override
    public Page<UserAccessNumEO> findUserPageAccessList(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");
        String userNo = jsonObject.getString("userNo");
        Boolean handleUserFlag = jsonObject.getBoolean("handleUserFlag");
        Integer leaderType = jsonObject.getInteger("leaderType");

        String orgCode = jsonObject.getString("orgCode");
        orgCode = StringUtils.isNotBlank(orgCode) ? orgCode : "X";
        // 获取所有子组织列表
        Set<String> allOrgSet = orgService.getAllChildOrgList(orgCode);

        String startTime = StringUtils.isNotBlank(loginTimeStart)? DateTools.getDayStartTime(loginTimeStart) : null;
        String endTime = StringUtils.isNotBlank(loginTimeEnd)? DateTools.getDayEndTime(loginTimeEnd) : null;

        if (leaderType != null) {
            jsonObject.put("pageSize", 50);
        }
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        // 获取所有负责业务员编号
//        Set<String> handleUserNoSet =  handleUserFlag != null && handleUserFlag ?
//                this.getAllHandleUserNo() : null;
        Set<String> leaderUserNoSet = null;
        Set<String> noLeaderUserNoSet = null;
        if (leaderType != null) {
            // 获取可查看的领导编号
            leaderUserNoSet = this.getViewableLeaderUserNo(leaderType);
        } else {
            // 排除所有领导编号
            noLeaderUserNoSet = this.getAllLeaderUserNo();
        }

//        Page<Map<String, Object>> userPageAccessData =
//                    pageLogSecretDao.findUserPageAccessList(startTime, endTime,userNo, allOrgSet, leaderUserNoSet, noLeaderUserNoSet, pageable);
//        long totalElements = userPageAccessData.getTotalElements();
//        List<Map<String, Object>> mapList = userPageAccessData.getContent();

        List<Map<String, Object>> mapList =
                pageLogSecretDao.findUserPageAccessList2(startTime, endTime,userNo, allOrgSet, leaderUserNoSet, noLeaderUserNoSet);

        // List＜Map＞转List＜Entity＞
        List<UserAccessNumEO> dataList = JSON.parseObject(JSON.toJSONString(mapList), new TypeReference<List<UserAccessNumEO>>() {});

        /*if (!"X38000000".equals(orgCode)) {// 非挑选宽板厂部门，则不展示宽厚板厂未访问过的用户页面访问数据
            dataList = dataList.stream()
                    .filter(e -> e.getAccessNum() != 0)
                    .collect(Collectors.toList());
        }*/

        /*if (leaderType != null) {
            Set<String> userNoList = dataList.stream()
                    .map(UserAccessNumEO::getUserNo)
                    .collect(Collectors.toSet());

            List<Map<String, String>> list = userDao.findUserOrgByUserNos(leaderUserNoSet);
            // 获取可查看领导用户列表
//            List<User> userList = this.getViewableLeaderUserList(leaderType);
            for (Map<String, String> map : list) {
                if (!userNoList.contains(map.get("userNo"))) {
                    dataList.add(new UserAccessNumEO(map.get("userNo"), map.get("userName"), map.get("orgCode"),map.get("orgAllName"),null));
                }
            }
        }*/
        // List转Page
        final int start = (int)pageable.getOffset();
        final int end = Math.min((start + pageable.getPageSize()), dataList.size());
        Page<UserAccessNumEO> userAccessNumList = new PageImpl<>(dataList.subList(start, end), pageable, dataList.size());

        return userAccessNumList;
    }

    @Override
    public List<UserDetAccessNumEO> findUserDetPageAccessList(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");
        String userNo = jsonObject.getString("userNo");
        Boolean handleUserFlag = jsonObject.getBoolean("handleUserFlag");
        Integer leaderType = jsonObject.getInteger("leaderType");

        String orgCode = jsonObject.getString("orgCode");
        // 获取所有子组织列表
        Set<String> allOrgSet = orgService.getAllChildOrgList(orgCode);

        String startTime = StringUtils.isNotBlank(loginTimeStart)? DateTools.getDayStartTime(loginTimeStart) : null;
        String endTime = StringUtils.isNotBlank(loginTimeEnd)? DateTools.getDayEndTime(loginTimeEnd) : null;

        // 获取所有负责业务员编号
//        Set<String> handleUserNoSet =  handleUserFlag != null &&handleUserFlag ?
//                this.getAllHandleUserNo() : null;
        Set<String> leaderUserNoSet = null;
        Set<String> noLeaderUserNoSet = null;
        if (leaderType != null) {
            // 获取可查看的领导编号
            leaderUserNoSet = this.getViewableLeaderUserNo(leaderType);
        } else {
            // 排除所有领导编号
            noLeaderUserNoSet = this.getAllLeaderUserNo();
        }
        // 查找用户页面访问数据
        List<Map<String, Object>> mapList =
                pageLogSecretDao.findUserDetPageAccessList(startTime, endTime,userNo, allOrgSet, leaderUserNoSet, noLeaderUserNoSet);

        // List＜Map＞转List＜Entity＞
        List<UserDetAccessNumEO> dataList = JSON.parseObject(JSON.toJSONString(mapList), new TypeReference<List<UserDetAccessNumEO>>() {});
        return dataList;
    }

    @Override
    public Page<Map<String, Object>> findPageAccessListByUserNo(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");
        String userNo = jsonObject.getString("userNo");

        String startTime = StringUtils.isNotBlank(loginTimeStart)? DateTools.getDayStartTime(loginTimeStart) : null;
        String endTime = StringUtils.isNotBlank(loginTimeEnd)? DateTools.getDayEndTime(loginTimeEnd) : null;

        Pageable pageable = ToolsUtil.initPage(jsonObject);

        // 查找用户页面访问数据
        Page<Map<String, Object>> userPageAccessData = pageLogSecretDao.findPageAccessListByUserNo(startTime, endTime, userNo, pageable);

        return userPageAccessData;
    }

    @Override
    public Page<PageAccessNumEO> findEachPageAccessList(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");
        String resourceId = jsonObject.getString("resourceId");
        String serviceNo = jsonObject.getString("serviceNo");
        String pageName = jsonObject.getString("pageName");

        String startTime = StringUtils.isNotBlank(loginTimeStart)? DateTools.getDayStartTime(loginTimeStart) : null;
        String endTime = StringUtils.isNotBlank(loginTimeEnd)? DateTools.getDayEndTime(loginTimeEnd) : null;

        Pageable pageable = ToolsUtil.initPage(jsonObject);

        // 查找用户页面访问数据
        Page<Map<String, Object>> pageAccessList = pageLogSecretDao.findEachPageAccessList(startTime, endTime,
                resourceId, serviceNo, pageName, pageable);

        long totalElements = pageAccessList.getTotalElements();
        List<Map<String, Object>> mapList = pageAccessList.getContent();
        // List＜Map＞转List＜Entity＞
        List<PageAccessNumEO> dataList = JSON.parseObject(JSON.toJSONString(mapList), new TypeReference<List<PageAccessNumEO>>() {});
        // List转Page
        Page<PageAccessNumEO> pageAccessNumList = new PageImpl<>(dataList, pageable, totalElements);

        return pageAccessNumList;
    }

    @Override
    public JSONObject getPageLogLookBoard(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");

        String startTime = StringUtils.isNotBlank(loginTimeStart)? DateTools.getDayStartTime(loginTimeStart) : null;
        String endTime = StringUtils.isNotBlank(loginTimeEnd)? DateTools.getDayEndTime(loginTimeEnd) : null;

        JSONObject json = new JSONObject();

        // 按模块查找页面访问次数
        List<Map<String, Object>> accessNumEOList = pageLogSecretDao.findPageAccessNumByService(startTime, endTime);
        json.put("module", accessNumEOList);

        // 按模块查找每个页面访问次数
        accessNumEOList = pageLogSecretDao.findEachPageAccessNumByService(startTime, endTime);
        json.put("page", accessNumEOList);

        // 获取所有负责业务员编号
        Set<String> handleUserNoSet = this.getAllHandleUserNo();
        // 按组织查找页面负责人访问次数
        accessNumEOList = pageLogSecretDao.findHandleUserAccessNumByOrg(startTime, endTime, handleUserNoSet);
        json.put("owner", accessNumEOList);

        return json;
    }

    @Override
    public JSONObject getPageLogLookBoardByOrg(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");
        String orgCode = jsonObject.getString("orgCode");

        String startTime = StringUtils.isNotBlank(loginTimeStart)? DateTools.getDayStartTime(loginTimeStart) : null;
        String endTime = StringUtils.isNotBlank(loginTimeEnd)? DateTools.getDayEndTime(loginTimeEnd) : null;

        JSONObject json = new JSONObject();

//        String orgCode = "*********";
        // 获取所有子组织列表
        Set<String> allOrgSet = orgService.getAllChildOrgList(orgCode);

        // 按组织查找用户访问次数
        List<Map<String, Object>> list = pageLogSecretDao.findUserAccessNumByOrg(startTime, endTime, allOrgSet);
        json.put("org", list);

        return json;
    }

    @Override
    public Page<Map<String, Object>> getLeaderAccess(JSONObject jsonObject) {

        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");

        String startTime = StringUtils.isNotBlank(loginTimeStart)? DateTools.getDayStartTime(loginTimeStart) : null;
        String endTime = StringUtils.isNotBlank(loginTimeEnd)? DateTools.getDayEndTime(loginTimeEnd) : null;

        List<String> ids = new ArrayList<>();
        Integer leaderType = jsonObject.getInteger("leaderType");
        if (leaderType == null) {
            ids.add("cb7784a2-3808-4390-bb57-5eaac3ccd447");// 事业部高层领导
        }
        ids.add("192a47ff-41a7-41d9-8cc6-f012dd729ed7");// 事业部中层领导（厂处）
        List<String> leaderUserNoList = roleDao.findAllById(ids)
                .stream()
                .map(Role::getUserList)
                .flatMap(users -> users.stream()
                        .sorted(Comparator.comparing(User::getRemarks))
                        .map(User::getUserNo))
                .collect(Collectors.toList());
        Set<String> leaderUserNoSet = new LinkedHashSet<>(leaderUserNoList);

        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<Map<String, Object>> userPageAccessData;
        if (leaderType == null) {
//            userPageAccessData
//                    = pageLogSecretDao.findUserPageAccessList(startTime, endTime, null, null, leaderUserNoSet, null, pageable);

            List<Map<String, Object>> mapList =
                    pageLogSecretDao.findUserPageAccessList2(startTime, endTime, null , null, null, null);
            long totalElements = mapList.size();

            // List转Page
            userPageAccessData = new PageImpl<>(mapList, pageable, totalElements);

        } else {
            userPageAccessData
                    = pageLogSecretDao.findSortUserPageAccessList(startTime, endTime, null, null, leaderUserNoSet, null, pageable);
        }
        return userPageAccessData;
    }

    @Override
    public Page<Map<String, Object>> findUserPageAccessListByResourceId(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");
        String resourceId = jsonObject.getString("resourceId");

        String startTime = StringUtils.isNotBlank(loginTimeStart)? DateTools.getDayStartTime(loginTimeStart) : null;
        String endTime = StringUtils.isNotBlank(loginTimeEnd)? DateTools.getDayEndTime(loginTimeEnd) : null;

        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Set<String> allOrgSet = null;
        String orgCode = jsonObject.getString("orgCode");
        if (StringUtils.isNotBlank(orgCode)) {
            // 获取所有子组织列表
            allOrgSet = orgService.getAllChildOrgList(orgCode);
        }

        // 查找用户页面访问数据
        Page<Map<String, Object>> userPageAccessData = pageLogSecretDao.findUserPageAccessListByResourceId(startTime, endTime, resourceId, allOrgSet, pageable);
        return userPageAccessData;
    }

    /**
     * 获取所有负责业务员编号
     *
     * @return {@link Set }<{@link String }>
     * <AUTHOR>
     * @create 2023-03-27
     */
    private Set<String> getAllHandleUserNo() {
        Set<String> handleUserNoSet = new HashSet<>();
        // 查找所有负责业务员
        resourceService.findAllHandleUser().stream()
                .forEach(e -> {
                    String[] split = e.split(",");
                    for (String s : split) {
                        String[] detHandleUserArr = s.split("\\|");
                        if (detHandleUserArr.length > 0) {
                            String userNo = detHandleUserArr[0];
                            handleUserNoSet.add(userNo);
                        }
                    }
                });
        return handleUserNoSet;
    }


    /**
     * 获取所有领导编号
     *
     * @return {@link Set }<{@link String }>
     * <AUTHOR>
     * @create 2023-05-09
     */
    private Set<String> getAllLeaderUserNo() {
        List<String> ids = Arrays.asList("fcf2c918-842f-44b4-ba2c-fb1503500cda", "cb7784a2-3808-4390-bb57-5eaac3ccd447");
        Set<String> userNoSet = roleDao.findAllById(ids)
                .stream()
                .map(Role::getUserList)
                .flatMap(users -> users.stream())
                .map(User::getUserNo)
                .collect(Collectors.toSet());
        return userNoSet;
    }

    /**
     * 获取可查看的领导编号
     *
     * @param leaderType 领导类型
     * @return {@link Set }<{@link String }>
     * <AUTHOR>
     * @create 2023-05-09
     */
    private Set<String> getViewableLeaderUserNo(Integer leaderType) {

        Set<String> leaderUserNoSet = new HashSet<>();

        String id = "";
        if (leaderType == 1) {
            id = "cb7784a2-3808-4390-bb57-5eaac3ccd447";// 高层角色
        }
        if (leaderType == 2) {
            id = "fcf2c918-842f-44b4-ba2c-fb1503500cda";// 中层角色
        }
        if (leaderType == 3) {
            // E层 主任
            leaderUserNoSet = pageLogSecretDao.findLevelUserNo("主任");
        } else {

            JSONObject json = new JSONObject();
            json.put("id", id);
            Role role = roleService.findOneRoleByID(json);

            if (role != null) {
                leaderUserNoSet = role.getUserList().stream()
                        .map(User::getUserNo)
                        .collect(Collectors.toSet());
            }
        }
        return leaderUserNoSet;
    }


    /**
     * 获取可查看领导用户列表
     *
     * @param leaderType 领导类型
     * @return {@link List }<{@link User }>
     * <AUTHOR>
     * @create 2023-05-10
     */
    private List<User> getViewableLeaderUserList(Integer leaderType) {

        String id = "";
        List<User> userList;
        if (leaderType == 1) {
            id = "cb7784a2-3808-4390-bb57-5eaac3ccd447";// 高层角色
        }
        if (leaderType == 2) {
            id = "fcf2c918-842f-44b4-ba2c-fb1503500cda";// 中层角色
        }
        if (leaderType == 3) {
            // E层 主任
            Set<String> leaderUserNoSet = pageLogSecretDao.findLevelUserNo("主任");
            userList = userDao.findUsersByUserNos(leaderUserNoSet);
        } else {
            JSONObject json = new JSONObject();
            json.put("id", id);
            Role role = roleService.findOneRoleByID(json);
            userList = role != null ? role.getUserList() : null;
        }
        return userList;
    }

    @Override
    public List<Map<String, Object>> getMainHandleUser(JSONObject json) {
        List<Map<String, Object>> list = this.getAllHandleUserNum(json);
        return list;
    }

    @Override
    public Page<Map<String, Object>> getDetailHandleUser(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");
        String userNo = jsonObject.getString("userNo");

        String startTime = StringUtils.isNotBlank(loginTimeStart)? DateTools.getDayStartTime(loginTimeStart) : null;
        String endTime = StringUtils.isNotBlank(loginTimeEnd)? DateTools.getDayEndTime(loginTimeEnd) : null;

        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<Map<String, Object>> list = pageLogSecretDao.findOwnerResources(userNo, startTime, endTime, pageable);
        return list;
    }

    // 查找所有负责业务员负责页面数量
    private List<Map<String, Object>> getAllHandleUserNum(JSONObject jsonObject) {
        JSONArray userNos = jsonObject.getJSONArray("userNos");
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");

        String startTime = StringUtils.isNotBlank(loginTimeStart)? DateTools.getDayStartTime(loginTimeStart) : null;
        String endTime = StringUtils.isNotBlank(loginTimeEnd)? DateTools.getDayEndTime(loginTimeEnd) : null;

        List<Map<String, Object>> list = new ArrayList<>();
        // 查找所有负责业务员负责页面数量
        List<Map<String, Object>> objList = resourceService.findAllHandleUserNum(null);
        for (Map<String, Object> map : objList) {
            String allHandleUser = (String) map.get("handleUser");
            String[] split = allHandleUser.split(",");
            for (String handleUser : split) {
                Map<String, Object> hashMap = new HashMap<>();
                String[] detHandleUserArr = handleUser.split("\\|");
                if (detHandleUserArr.length > 1) {
                    String userNo1 = detHandleUserArr[0];
                    String userName = detHandleUserArr[1];
                    hashMap.put("userNo", userNo1);
                    hashMap.put("userName", userName);
                    hashMap.put("num", Integer.parseInt(map.get("num").toString()));
                    list.add(hashMap);
                }
            }
        }
        Map<String, Integer> collect = list.stream().collect(
                Collectors.groupingBy(item -> item.get("userNo") + "-" + item.get("userName"),
                        Collectors.summingInt(item -> (Integer) item.get("num"))));

        List<Map<String, Object>> resList = new ArrayList<>();
        List<Map<String, Object>> accessNumEOList = pageLogSecretDao.findHandleUserAccessNumByOrg(startTime, endTime);
        for (Map.Entry<String, Integer> entry : collect.entrySet()) {
            Map<String, Object> map = new HashMap<>();
            String key = entry.getKey();
            String[] split = key.split("-");
            map.put("userNo", split[0]);
            map.put("userName", split[1]);
            map.put("num", entry.getValue());

            // 获取所有负责业务员编号
            Set<String> handleUserNoSet = new HashSet<>();
            handleUserNoSet.add(split[0]);
            // 按组织查找页面负责人访问次数
            Integer accessNum = 0;
            List<Map<String, Object>> accessNumEOList2 = accessNumEOList.stream()
                    .filter(e -> handleUserNoSet.contains(e.get("userNo")))
                    .collect(Collectors.toList());
            if (accessNumEOList2 != null && accessNumEOList2.size() > 0) {
                accessNum = ((BigInteger) accessNumEOList2.get(0).get("accessNum")).intValue();
            }
            map.put("accessNum", accessNum);
            resList.add(map);
        }
        // 负责页面数量倒序
        resList = resList.stream()
                .sorted((map1, map2) -> Integer.valueOf(map2.get("num").toString())
                        .compareTo(Integer.valueOf(map1.get("num").toString())))
                .collect(Collectors.toList());

        if (userNos != null && userNos.size() > 0) {
            List<String> userNoList = JSONArray.parseArray(userNos.toJSONString(), String.class);
            resList = resList.stream()
                    .filter(map -> userNoList.contains(map.get("userNo")))
                    .collect(Collectors.toList());
        }
        return resList;
    }

    @Override
    public List<AccessNumEO> findGroupAppByLoginTime(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");
        Map<String, Date> map = DateTools.getStartAndEndDate(loginTimeStart, loginTimeEnd);
        Date loginTimeStartDate = map.get("loginTimeStart");
        Date loginTimeEndDate = map.get("loginTimeEnd");
        // 查询应用访问次数
        List<AccessNumEO> accessNumEOList = pageLogSecretDao.findGroupAppByLoginTime(loginTimeStartDate, loginTimeEndDate);
        return accessNumEOList;
    }


    @Override
    public void exportExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");

        Map<String, Date> map = DateTools.getStartAndEndDate(loginTimeStart, loginTimeEnd);
        Date loginTimeStartDate = map.get("loginTimeStart");
        Date loginTimeEndDate = map.get("loginTimeEnd");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = sdf.format(loginTimeStartDate);
        String endTime = sdf.format(loginTimeEndDate);

        //功能描述：把同一个表格多个sheet测试结果重新输出
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("厂处排行");
        // 设置sheet表头名称
        exportParams.setSheetName("厂处排行");
        // 创建sheet1使用得map
        Map<String, Object> oneExportMap = new HashMap<>();
        // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
        oneExportMap.put("title", exportParams);
        // 模版导出对应得实体类型
        oneExportMap.put("entity", AccessNumEO.class);
        // 查找组织使用人数和访问次数
//        List<AccessNumEO> accessNumEOList = pageLogSecretDao.findGroupOrgByLoginTime(loginTimeStartDate, loginTimeEndDate);
        // 计算组织及所有子组织人数之和
        List<AccessNumEO> accessNumEOList = this.calcOrgNum(startTime, endTime);

        // 获取厂处数据 排除 板材事业部江苏南钢板材销售有限公司、板材事业部金石高新材料项目部
        List<AccessNumEO> dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 1)
                .filter(eo -> !"*********".equals(eo.getCode()) && !"*********".equals(eo.getCode()))
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        oneExportMap.put("data", dataList);

        exportParams = new ExportParams();
        exportParams.setTitle("科室排行");
        exportParams.setSheetName("科室排行");
        Map<String, Object>  twoExportMap = new HashMap<>();
        twoExportMap.put("title", exportParams);
        twoExportMap.put("entity", AccessNumEO.class);

        // 获取科室数据 排除 第一炼钢厂检修车间、综合处JIT+C2M项目部、营销处...、金润...
        dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 2)
                .filter(eo -> !"X73080000".equals(eo.getCode()) && !"X50010800".equals(eo.getCode())
                        && !eo.getCode().contains("X5005") && !eo.getCode().contains("X87"))
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        twoExportMap.put("data", dataList);

        exportParams = new ExportParams();
        exportParams.setTitle("班组排行");
        exportParams.setSheetName("班组排行");
        Map<String, Object>  threeExportMap = new HashMap<>();
        threeExportMap.put("title", exportParams);
        threeExportMap.put("entity", AccessNumEO.class);
        // 获取班组数据
        dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 3)
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        threeExportMap.put("data", dataList);

        exportParams = new ExportParams();
        exportParams.setTitle("用户排行");
        exportParams.setSheetName("用户排行");
        Map<String, Object> userExportMap = new HashMap<>();
        userExportMap.put("title", exportParams);
        userExportMap.put("entity", AccessNumEO.class);
        // 查询用户访问次数
        /*accessNumEOList = pageLogSecretDao.findGroupUserByLoginTime(loginTimeStartDate, loginTimeEndDate)
                .stream()
                .map(eo -> {
                    eo.setOrgName(eo.getOrgName().replace("板材事业部", ""));
                    return eo;
                }).collect(Collectors.toList());*/

        JSONObject paramJson = new JSONObject();
        paramJson.put("pageIndex", 1);
        paramJson.put("pageSize", 5000);
        Pageable pageable = ToolsUtil.initPage(paramJson);

        // 查找用户页面访问数据
//        Page<Map<String, Object>> userPageAccessData =
//                pageLogSecretDao.findUserPageAccessList(startTime, endTime, null , null, null, null, pageable);
//        List<Map<String, Object>> mapList = userPageAccessData.getContent();

        List<Map<String, Object>> mapList =
                pageLogSecretDao.findUserPageAccessList2(startTime, endTime, null , null, null, null);

        accessNumEOList = mapList.stream()
                .map(m -> new AccessNumEO((String) m.get("userNo"),
                        (String) m.get("userName"),
                        Long.valueOf(m.get("accessNum").toString()),
                        ((String) m.get("orgAllName")).replace("板材事业部","")))
                .collect(Collectors.toList());
        userExportMap.put("data", accessNumEOList);

        exportParams = new ExportParams();
        exportParams.setTitle("应用排行");
        exportParams.setSheetName("应用排行");
        Map<String, Object>  appExportMap = new HashMap<>();
        appExportMap.put("title", exportParams);
        appExportMap.put("entity", AccessNumEO.class);
        // 查询应用访问次数
        /*accessNumEOList =  pageLogSecretDao.findGroupAppByLoginTime(loginTimeStartDate, loginTimeEndDate)
                .stream()
                .filter(accessNumEO -> !accessNumEO.getCode().contains("old") && !"res".equals(accessNumEO.getCode()))
                .collect(Collectors.toList());*/

        mapList = pageLogSecretDao.findPageAccessNumByService(startTime, endTime);
        accessNumEOList = mapList.stream()
                .map(m -> new AccessNumEO((String) m.get("code"),
                        Long.valueOf(m.get("num").toString())))
                .collect(Collectors.toList());
        accessNumEOList = this.writeServiceName(accessNumEOList);
        appExportMap.put("data", accessNumEOList);

        // 将sheet使用得map进行包装
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        //后续增加sheet组，则后面继续追加即可;
        sheetsList.add(oneExportMap);
        sheetsList.add(twoExportMap);
        sheetsList.add(threeExportMap);
        sheetsList.add(userExportMap);
        sheetsList.add(appExportMap);

        // 执行方法
        @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
        //设置编码格式
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        //设置内容类型
        response.setContentType("application/octet-stream");
        //设置头及文件命名。
        response.setHeader("Content-Disposition", "attachment;filename="
                + URLEncoder.encode("系统访问次数排行("+loginTimeStart
                +"_"+loginTimeEnd+").xls", StandardCharsets.UTF_8.name()));
        //写出流
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        workBook.write(outputStream);
    }

    @Override
    public void exportLeaderAndOrgExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");

        Map<String, Date> map = DateTools.getStartAndEndDate(loginTimeStart, loginTimeEnd);
        Date loginTimeStartDate = map.get("loginTimeStart");
        Date loginTimeEndDate = map.get("loginTimeEnd");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = sdf.format(loginTimeStartDate);
        String endTime = sdf.format(loginTimeEndDate);

        //功能描述：把同一个表格多个sheet测试结果重新输出
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("领导访问统计表");
        // 设置sheet表头名称
        exportParams.setSheetName("领导访问统计表");
        // 创建sheet1使用得map
        Map<String, Object> oneExportMap = new HashMap<>();
        // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
        oneExportMap.put("title", exportParams);
        // 模版导出对应得实体类型
        oneExportMap.put("entity", LeaderAccessNumEO.class);
        // 查找组织使用人数和访问次数
        JSONObject jsonObj = new JSONObject();
        jsonObj.put("id", "192a47ff-41a7-41d9-8cc6-f012dd729ed7");
        Role role = roleService.findOneRoleByID(jsonObj);
        Optional.ofNullable(role).map(Role::getUserList)
                .ifPresent(users -> {
                    List<String> userNoList = users.stream()
                            .sorted(Comparator.comparing(User::getRemarks))
                            .map(User::getUserNo)
                            .collect(Collectors.toList());
                    List<Map<String, Object>> mapList = pageLogSecretDao.findLeaderAccessNum(startTime, endTime, userNoList);
                    // List＜Map＞转List＜Entity＞
                    List<LeaderAccessNumEO> dataList = JSON.parseObject(JSON.toJSONString(mapList), new TypeReference<List<LeaderAccessNumEO>>() {});
                    oneExportMap.put("data", dataList);
                });

        List<AccessNumEO> accessNumEOList = this.calcOrgNum(startTime, endTime);
        exportParams = new ExportParams();
        exportParams.setTitle("单位访问统计表");
        exportParams.setSheetName("单位访问统计表");
        Map<String, Object>  twoExportMap = new HashMap<>();
        twoExportMap.put("title", exportParams);
        twoExportMap.put("entity", OrgAccessNumEO.class);

        // 获取厂处数据
        List<AccessNumEO> dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 1).collect(Collectors.toList());
        List<AccessNumEO> dataNewList = dataList.stream()
                .filter(eo -> !"X87000000".equals(eo.getCode())
                        && !"X50050000".equals(eo.getCode())
                        && !"*********".equals(eo.getCode())
                        && !"*********".equals(eo.getCode()))
                .sorted(Comparator.comparing(entity -> (double) entity.getNum() / entity.getUseNum(), Comparator.reverseOrder()))
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .collect(Collectors.toList());

        List<Map<String, Object>> cMapList = pageLogSecretDao.findLevelAccessNum(startTime, endTime, "处长");
        List<Map<String, Object>> cMapList2 = pageLogSecretDao.findLevelAccessNum(startTime, endTime, "厂长");
        cMapList.addAll(cMapList2);

        List<Map<String, Object>> eMapList = pageLogSecretDao.findLevelAccessNum(startTime, endTime, "主任");
        List<Map<String, Object>> feedMapList = pageLogSecretDao.findFeedBackNum(startTime, endTime);

        // 查找部门应该访问人数
        List<Map<String, Object>> shouldUseNumList = pageLogSecretDao.findShouldUseNum();

        List<OrgAccessNumEO> combinedList = new ArrayList<>();
        for (AccessNumEO obj1 : dataNewList) {
            String orgCode = obj1.getCode();
            Map<String, Object> obj2 = findObjectByOrgCode(cMapList, orgCode);
            Map<String, Object> obj3 = findObjectByOrgCode(eMapList, orgCode);
            Map<String, Object> obj4 = findObjectByOrgCode(feedMapList, orgCode);

            Long shouldUseNum = shouldUseNumList.stream()
                    .filter(e -> orgCode.contains((String) e.get("subOrgCode")))
                    .map(e -> ((Double) e.get("num")).longValue())
                    .findFirst()
                    .orElse(obj1.getOrgNum());
            OrgAccessNumEO orgAccessNumEO = new OrgAccessNumEO(obj1.getCode(), obj1.getName(),
                    obj1.getOrgNum(), shouldUseNum, obj1.getUseNum(),
                    new BigDecimal(String.valueOf(obj1.getUseNum()*100/shouldUseNum)).setScale(0, BigDecimal.ROUND_HALF_UP),
                    obj1.getNum(),
                    (obj2 != null ? (BigDecimal) obj2.get("accessNum") : 0).longValue(),
                    (obj3 != null ? (BigDecimal) obj3.get("accessNum") : 0).longValue(),
                    (obj4 != null ? (BigDecimal) obj4.get("feedBackNum") : 0).longValue());
            combinedList.add(orgAccessNumEO);
        }

        List<OrgAccessNumEO> collect = combinedList.stream()
                .sorted(Comparator.comparing(e -> e.getNum() / e.getShouldUseNum(), Comparator.reverseOrder()))
                .map(e -> {
                    e.setAvgAccessRank(combinedList.indexOf(e) + 1);
                    return e;
                })
                .sorted(Comparator.comparing(e -> new BigDecimal(e.getFeedBackNum())
                                .divide(new BigDecimal(e.getUseNum()), 4, BigDecimal.ROUND_HALF_UP),
                        Comparator.reverseOrder()))
                .collect(Collectors.toList());

        for (int i = 0; i < collect.size(); i++) {
            collect.get(i).setAvgFeedBackRank(i + 1);
        }
        List<OrgAccessNumEO> collect1 = collect.stream()
                .sorted(Comparator.comparingInt(OrgAccessNumEO::getAvgAccessRank))
                .collect(Collectors.toList());

        twoExportMap.put("data", collect1);

        // 将sheet使用得map进行包装
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        //后续增加sheet组，则后面继续追加即可;
        sheetsList.add(oneExportMap);
        sheetsList.add(twoExportMap);

        // 执行方法
        @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
        //设置编码格式
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        //设置内容类型
        response.setContentType("application/octet-stream");
        //设置头及文件命名。
        response.setHeader("Content-Disposition", "attachment;filename="
                + URLEncoder.encode("领导与单位访问统计("+loginTimeStart
                +"_"+loginTimeEnd+").xls", StandardCharsets.UTF_8.name()));
        //写出流
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        workBook.write(outputStream);
    }

    private Map<String, Object> findObjectByOrgCode(List<Map<String, Object>> list, String orgCode) {
        for (Map<String, Object> obj : list) {
            if (orgCode.contains((String)obj.get("subOrgCode"))) {
                return obj;
            }
        }
        return null;
    }

    @Override
    public JSONObject exportWord2(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");

        Map<String, Date> map = DateTools.getStartAndEndDate(loginTimeStart, loginTimeEnd);
        Date loginTimeStartDate = map.get("loginTimeStart");
        Date loginTimeEndDate = map.get("loginTimeEnd");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = sdf.format(loginTimeStartDate);
        String endTime = sdf.format(loginTimeEndDate);

        JSONObject json = new JSONObject();
        // 汇总所有使用人数和访问次数
        List<AccessNumEO> accessNumEOList = pageLogSecretDao.findSummaryNumByLoginTime2(loginTimeStartDate, loginTimeEndDate);
        json.put("summary", accessNumEOList);

        // 查找组织使用人数和访问次数
//        accessNumEOList = pageLogSecretDao.findGroupOrgByLoginTime(loginTimeStartDate, loginTimeEndDate);
        // 计算组织及所有子组织人数之和
        accessNumEOList = this.calcOrgNum(startTime, endTime);
        // 获取厂处数据
        List<AccessNumEO> dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 1)
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        json.put("org-1", dataList);

        // 获取科室数据
        dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 2)
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        json.put("org-2", dataList);

        // 获取班组数据
        dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 3)
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        json.put("org-3", dataList);


        // 查询用户访问次数
        /*dataList = pageLogSecretDao.findGroupUserByLoginTime(loginTimeStartDate, loginTimeEndDate).stream()
                .map(eo -> {
                    eo.setOrgName(eo.getOrgName().replace("板材事业部", ""));
                    return eo;
                }).collect(Collectors.toList());*/

        JSONObject paramJson = new JSONObject();
        paramJson.put("pageIndex", 1);
        paramJson.put("pageSize", 5000);
        Pageable pageable = ToolsUtil.initPage(paramJson);
        // 查找用户页面访问数据
//        Page<Map<String, Object>> userPageAccessData =
//                pageLogSecretDao.findUserPageAccessList(startTime, endTime, null , null, null, null, pageable);
//        List<Map<String, Object>> mapList = userPageAccessData.getContent();

        List<Map<String, Object>> mapList =
                pageLogSecretDao.findUserPageAccessList2(startTime, endTime, null , null, null, null);

        dataList = mapList.stream()
                .map(m -> new AccessNumEO((String) m.get("userNo"),
                        (String) m.get("userName"),
                        Long.valueOf(m.get("accessNum").toString()),
                        ((String) m.get("orgAllName")).replace("板材事业部","")))
                .collect(Collectors.toList());

        json.put("user", dataList);


        // 查询应用访问次数
        /*dataList = pageLogSecretDao.findGroupAppByLoginTime(loginTimeStartDate, loginTimeEndDate)
                .stream()
                .filter(accessNumEO -> !accessNumEO.getCode().contains("old") && !"res".equals(accessNumEO.getCode()))
                .collect(Collectors.toList());*/

        mapList = pageLogSecretDao.findPageAccessNumByService(startTime, endTime);
        dataList = mapList.stream()
                .map(m -> new AccessNumEO((String) m.get("code"),
                        Long.valueOf(m.get("num").toString())))
                .collect(Collectors.toList());
        dataList = this.writeServiceName(dataList);
        json.put("app", dataList);

        Integer recomHandledNum  = 0;
        Integer bugHandledNum  = 0;
        Integer recomUnHandledNum = 0;
        Integer bugUnHandledNum = 0;
        // 查询问题反馈和合理化建议处理数
        List<Integer> statusList = Arrays.asList(2, 4);
        List<Map<String, Integer>> handledNumList = feedBackService.findCountByStatus(statusList, loginTimeStartDate, loginTimeEndDate);
        for (Map<String, Integer> handledNumMap : handledNumList) {
            if (handledNumMap.get("quesType") == 1) {// 合理化建议
                String num = String.valueOf(handledNumMap.get("num"));
                recomHandledNum = Integer.parseInt(num);
            }
            if (handledNumMap.get("quesType") == 2) {// 问题反馈
                String num = String.valueOf(handledNumMap.get("num"));
                bugHandledNum = Integer.parseInt(num);
            }
        }

        statusList = Arrays.asList(1, 3);
        List<Map<String, Integer>> unHandledNumList = feedBackService.findCountByStatus(statusList, loginTimeStartDate, loginTimeEndDate);
        for (Map<String, Integer> unHandledNumMap : unHandledNumList) {
            if (unHandledNumMap.get("quesType") == 1) {// 合理化建议
                String num = String.valueOf(unHandledNumMap.get("num"));
                recomUnHandledNum = Integer.parseInt(num);
            }
            if (unHandledNumMap.get("quesType") == 2) {// 问题反馈
                String num = String.valueOf(unHandledNumMap.get("num"));
                bugUnHandledNum = Integer.parseInt(num);
            }
        }

        JSONObject jsonObj1 = new JSONObject();
        jsonObj1.put("handledNum", recomHandledNum);
        jsonObj1.put("unHandledNum", recomUnHandledNum);
        jsonObj1.put("totalNum", recomHandledNum + recomUnHandledNum);
        json.put("feedBack-recom", jsonObj1);

        JSONObject jsonObj2 = new JSONObject();
        jsonObj2.put("handledNum", bugHandledNum);
        jsonObj2.put("unHandledNum", bugUnHandledNum);
        jsonObj2.put("totalNum", bugHandledNum + bugUnHandledNum);
        json.put("feedBack-bug", jsonObj2);

        return json;
    }

    /**
     * 计算组织及所有子组织人数之和
     *
     * @param startTime 登录时间开始
     * @param endTime   登录时间结束
     * @return {@link List }<{@link AccessNumEO}>
     * <AUTHOR>
     * @create 2022-11-10
     */
    private List<AccessNumEO> calcOrgNum(String startTime, String endTime) {
        List<AccessNumEO> eoNewList = new ArrayList<>();
        // 查找板材事业部所有组织
        List<Map<String, Object>> mapList = pageLogSecretDao.findOrgUsers(startTime, endTime);
        // List＜Map＞转List＜Entity＞
        List<AccessNumEO> list = JSON.parseObject(JSON.toJSONString(mapList), new TypeReference<List<AccessNumEO>>() {});

        for (AccessNumEO accessNumEO : list) {
            Long orgNum = 0L;
            Long shouldUseNum = 0L;
            Long useNum = 0L;
            Long num = 0L;
            if ("*********".equals(accessNumEO.getParentCode())) {
                String code = accessNumEO.getCode();
                orgNum += accessNumEO.getOrgNum() != null ? accessNumEO.getOrgNum() : 0;
                useNum += accessNumEO.getUseNum() != null ? accessNumEO.getUseNum() : 0;
                num += accessNumEO.getNum() != null ? accessNumEO.getNum() : 0;
                if (orgNum == 0) {// 过滤部门人数为0
                    continue;
                }
                for (AccessNumEO eo : list) {
                    Long orgNum1 = 0L;
                    Long shouldUseNum1 = 0L;
                    Long useNum1 = 0L;
                    Long num1 = 0L;
                    if (code.equals(eo.getParentCode())) {
                        String code1 = eo.getCode();
                        orgNum += eo.getOrgNum() != null ? eo.getOrgNum() : 0;
                        orgNum1 += eo.getOrgNum() != null ? eo.getOrgNum() : 0;
                        shouldUseNum += eo.getShouldUseNum() != null ? eo.getShouldUseNum() : 0;
                        shouldUseNum1 += eo.getShouldUseNum() != null ? eo.getShouldUseNum() : 0;
                        useNum += eo.getUseNum() != null ? eo.getUseNum() : 0;
                        useNum1 += eo.getUseNum() != null ? eo.getUseNum() : 0;
                        num += eo.getNum() != null ? eo.getNum() : 0;
                        num1 += eo.getNum() != null ? eo.getNum() : 0;

                        for (AccessNumEO eo2 : list) {
                            if (code1.equals(eo2.getParentCode())) {
                                orgNum += eo2.getOrgNum() != null ? eo2.getOrgNum() : 0;
                                orgNum1 += eo2.getOrgNum() != null ? eo2.getOrgNum() : 0;
                                shouldUseNum += eo2.getShouldUseNum() != null ? eo2.getShouldUseNum() : 0;
                                shouldUseNum1 += eo2.getShouldUseNum() != null ? eo2.getShouldUseNum() : 0;
                                useNum += eo2.getUseNum() != null ? eo2.getUseNum() : 0;
                                useNum1 += eo2.getUseNum() != null ? eo2.getUseNum() : 0;
                                num += eo2.getNum() != null ? eo2.getNum() : 0;
                                num1 += eo2.getNum() != null ? eo2.getNum() : 0;

                                Long orgNum2 = eo2.getOrgNum();
                                Long shouldUseNum2 = eo2.getShouldUseNum();
                                Long useNum2 = eo2.getUseNum();
                                eo2.setOrgNum(orgNum2);
                                eo2.setShouldUseNum(shouldUseNum2);
                                eo2.setUseNum(useNum2);
                                eo2.setNum(eo2.getNum());
                                eo2.setAvgNum(useNum2 != 0? eo2.getNum()/useNum2 : 0);
                                BigDecimal rate = shouldUseNum2 != 0 ? new BigDecimal(useNum2).multiply(new BigDecimal(100))
                                        .divide(new BigDecimal(shouldUseNum2),0, BigDecimal.ROUND_HALF_UP)
                                        : BigDecimal.ZERO;
                                eo2.setRate(rate);
                                eo2.setLevel(3);
                                eoNewList.add(eo2);
                            }
                        }
                        eo.setOrgNum(orgNum1);
                        eo.setShouldUseNum(shouldUseNum1);
                        eo.setUseNum(useNum1);
                        eo.setNum(num1);
                        eo.setAvgNum(useNum1 != 0? num1/useNum1 : 0);
                        BigDecimal rate = shouldUseNum1 != 0 ? new BigDecimal(useNum1).multiply(new BigDecimal(100))
                                .divide(new BigDecimal(shouldUseNum1),0, BigDecimal.ROUND_HALF_UP)
                                : BigDecimal.ZERO;
                        eo.setRate(rate);
                        eo.setLevel(2);
                        eoNewList.add(eo);
                    }
                }
                accessNumEO.setOrgNum(orgNum);
                accessNumEO.setShouldUseNum(shouldUseNum);
                accessNumEO.setUseNum(useNum);
                accessNumEO.setNum(num);
                accessNumEO.setAvgNum(useNum != 0 ? num/useNum : 0);
                BigDecimal rate = shouldUseNum != 0 ? new BigDecimal(useNum).multiply(new BigDecimal(100))
                        .divide(new BigDecimal(shouldUseNum),0, BigDecimal.ROUND_HALF_UP)
                        : BigDecimal.ZERO;
                accessNumEO.setRate(rate);
                accessNumEO.setLevel(1);
                eoNewList.add(accessNumEO);
            }
        }
        return eoNewList;
    }

    @Override
    public List<AccessNumEO> findAccessNumByLoginTime2(JSONObject jsonObj) {
        String loginTimeStart = jsonObj.getString("loginTimeStart");
        String loginTimeEnd = jsonObj.getString("loginTimeEnd");
        String type = jsonObj.getString("type");

        Map<String, Date> map = DateTools.getStartAndEndDate(loginTimeStart, loginTimeEnd);
        Date loginTimeStartDate = map.get("loginTimeStart");
        Date loginTimeEndDate = map.get("loginTimeEnd");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = sdf.format(loginTimeStartDate);
        String endTime = sdf.format(loginTimeEndDate);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("pageIndex", 1);
        jsonObject.put("pageSize", 5000);
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        List<AccessNumEO> accessNumEOList = new ArrayList<>();
        if (StringUtils.isNotBlank(type) && type.startsWith("org")) {
            // 查询组织访问次数
//            List<AccessNumEO> accessNumEOS = appLogDao.findGroupOrgByLoginTime(loginTimeStartDate, loginTimeEndDate);
            // 层级：1-厂处 2-科室 3-班组
            Integer level = Integer.parseInt(type.substring(type.length() - 1));
            accessNumEOList = this.calcOrgNum(startTime, endTime).stream()
                    .filter(eo -> eo.getLevel() != null && eo.getLevel() == level)
                    .map(eo -> {
                        eo.setName(eo.getName().replace("板材事业部", ""));
                        return eo;
                    })
                    .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                    .collect(Collectors.toList());
        }
        if ("user".equals(type)) {
            // 查询应用用户次数
            /*accessNumEOList = appLogDao.findGroupUserByLoginTime(loginTimeStartDate, loginTimeEndDate)
                    .stream()
                    .filter(eo -> StringUtils.isNotBlank(eo.getOrgName()))
                    .limit(20).collect(Collectors.toList());*/

            // 查找用户页面访问数据
//            Page<Map<String, Object>> userPageAccessData =
//                    pageLogSecretDao.findUserPageAccessList(startTime, endTime, null , null, null, null, pageable);
//            List<Map<String, Object>> mapList = userPageAccessData.getContent();

            List<Map<String, Object>> mapList =
                    pageLogSecretDao.findUserPageAccessList2(startTime, endTime, null , null, null, null);

            accessNumEOList = mapList.stream()
                    .map(m -> new AccessNumEO((String) m.get("userNo"),
                            (String) m.get("userName"),
                            Long.valueOf(m.get("accessNum").toString()),
                            ((String) m.get("orgAllName")).replace("板材事业部","")))
                    .collect(Collectors.toList());
        }
        if("app".equals(type)) {
            // 查询应用访问次数
            /*accessNumEOList = appLogDao.findGroupAppByLoginTime(loginTimeStartDate, loginTimeEndDate)
                    .stream()
                    .filter(accessNumEO -> !accessNumEO.getCode().contains("old") && !"res".equals(accessNumEO.getCode()))
                    .collect(Collectors.toList());*/

            List<Map<String, Object>> mapList = pageLogSecretDao.findPageAccessNumByService(startTime, endTime);
            accessNumEOList = mapList.stream()
                    .map(m -> new AccessNumEO((String) m.get("code"),
                            Long.valueOf(m.get("num").toString())))
                    .collect(Collectors.toList());
            accessNumEOList = this.writeServiceName(accessNumEOList);
        }
        return accessNumEOList;
    }

    @Override
    public void exportUserPageDetExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException {

        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");
        String serviceNo = jsonObject.getString("serviceNo");
        String orgCode = jsonObject.getString("orgCode");

        String startTime = StringUtils.isNotBlank(loginTimeStart)? DateTools.getDayStartTime(loginTimeStart) : null;
        String endTime = StringUtils.isNotBlank(loginTimeEnd)? DateTools.getDayEndTime(loginTimeEnd) : null;
        //功能描述：把同一个表格多个sheet测试结果重新输出
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("用户页面访问详情");
        // 设置sheet表头名称
        exportParams.setSheetName("用户页面访问详情");
        // 创建sheet1使用得map
        Map<String, Object> oneExportMap = new HashMap<>();
        // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
        oneExportMap.put("title", exportParams);
        // 模版导出对应得实体类型
        oneExportMap.put("entity", UserPageAccessEO.class);

        Set<String> allOrgSet = null;
        if (StringUtils.isNotBlank(orgCode)) {
            // 获取所有子组织列表
            allOrgSet = orgService.getAllChildOrgList(orgCode);
        }
        // 查询用户页面访问明细
        List<Map<String, Object>> dataList = pageLogSecretDao.findUserPageAccessDet(startTime, endTime, serviceNo, allOrgSet);
        List<UserPageAccessEO> list = JSON.parseObject(JSON.toJSONString(dataList), new TypeReference<List<UserPageAccessEO>>() {});
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        list = list.stream().map(e -> {
            Date loginTime = e.getLoginTime();
            String accessTime = sdf.format(loginTime);
            e.setAccessTime(accessTime);
            return e;
        }).collect(Collectors.toList());
        oneExportMap.put("data", list);

        // 将sheet使用得map进行包装
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        //后续增加sheet组，则后面继续追加即可;
        sheetsList.add(oneExportMap);

        // 执行方法
        @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
        //设置编码格式
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        //设置内容类型
        response.setContentType("application/octet-stream");
        //设置头及文件命名。
        response.setHeader("Content-Disposition", "attachment;filename="
                + URLEncoder.encode("用户页面访问明细.xls", StandardCharsets.UTF_8.name()));
        //写出流
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        workBook.write(outputStream);
    }

}
