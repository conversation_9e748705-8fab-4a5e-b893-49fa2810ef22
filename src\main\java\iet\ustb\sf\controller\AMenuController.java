package iet.ustb.sf.controller;

import iet.ustb.sf.utils.SsoParamUtil;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Description: TODO
 * @author: qixu
 * @date: 2022年07月14日 11:10
 */
@RestController
@RequestMapping(value = "/menu")
public class AMenuController {
    @RequestMapping(value = "/logout")
    public void logout(HttpServletRequest request, HttpServletResponse response) {
//        ---清除SSO认证并跳转如果只有登录页可用此方法，如果有公共首页可跳转首页-----
        String reqCon = request.getContextPath();
        String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + reqCon + "/";
        String redirUrl = SsoParamUtil.SSOLOGINOUT + "?service=" + basePath + "login/ssoLogin";
        try {
            response.sendRedirect(redirUrl);
        } catch (IOException e) {
            e.printStackTrace();
        }

//		---------清除SSO认证并跳转下面跳转可注释---------
//		List<Company> companies = companyService.findAll();
//		map.put("companies", companies);
//		if(lang!=null&&lang.equals("EN"))
//			return INIT_PAGE_EN;
//		else
//        return INIT_PAGE;
    }
}


