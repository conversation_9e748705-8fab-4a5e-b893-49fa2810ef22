package iet.ustb.sf.vo.domain;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 岗位表
 */
@Data
@Entity
@Table(name = "DU_POST")
public class Post extends BaseEntity {

    /**
     * 岗位编号
     */
    @Column(length = 64, unique = true, nullable = false)
    private String postCode;

    /**
     * 岗位名称
     */
    @Column(length = 64, nullable = false)
    private String postName;

    /**
     * 类型：0-默认
     */
    @Column(length = 64, nullable = false)
    private String postType;

    /**
     * 描述
     */
    @Column(length = 64, nullable = false)
    private String postDesc;

    /**
     * 状态：0-启用,1-禁用
     */
    @Column(length = 64, nullable = false)
    private String status;

    /**
     * 是否HR
     */
    @Column(length = 64, nullable = false)
    private String isHR;

    /**
     * 操作标识：N 新增,U修改,D删除
     */
    @Column(length = 64)
    private String operStus;

    /**
     * 判重项目值域
     */
    private String repeatField;

}
