package iet.ustb.sf.vo.domain;

import lombok.Data;
import org.hibernate.annotations.Proxy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.domain
 * @title: Ds_Resource
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/1009:17
 */

@Entity
@Table(name = "DS_RESOURCE")
@Data
@Proxy(lazy = false)
public class Resource extends BaseEntity {

    //菜单编码
    @Column
    String code;

    //菜单名称
    @Column
    String name;

    //父级ID
    @Column
    String parentId;

    //父级编码
    @Column(name = "parent_code")
    String parentCode;

    //描述
    @Column
    String description;

    //是否展示,0-否,1-是
    @Column
    Integer isShow;

    //菜单类型 menu-菜单,button-按钮,plugin-控件
    @Column
    String type;

    //链接
    @Column
    String url;

    //排序
    @Column
    Integer sort;

    //服务名称
    @Column
    String serviceName;

    //状态,0-禁用,1-启用
    @Column
    Integer status;

    //是否被选中,1-是,0-否
    @Column
    String isSelected;

    @Column
    String ip;

    @Column
    String port;

    /**
     * 插件size，当type为plugin时填写
     * 格式：长:宽
     */
    @Column
    String pluginSize;

    // 菜单图标
    @Column
    String icon;

    //图标地址
    @Transient
    String iconUrl;

    // 桌面图标
    @Column
    String deskIcon;

    //桌面图标地址
    @Transient
    String deskUrl;

    // 负责开发者
    @Column
    String handleDeveloper;

    // 负责业务员
    @Column
    String handleUser;

    //审批人
    @Column
    String approveUser;

    //是否外部链接，0：否，1：是
    @Column(name = "is_external_url")
    Integer isExternalUrl;

    //不持久化该字段
    @Transient
    List<Resource> children;

    @Transient
    List<String> roles;

    //用户编码
    @Transient
    String userNo;

    @Transient
    List<String> idList;

}
