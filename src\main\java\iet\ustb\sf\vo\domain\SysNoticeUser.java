package iet.ustb.sf.vo.domain;

import java.io.Serializable;

/**
 * 公告和用户关系表(SysNoticeUser)实体类
 *
 * <AUTHOR>
 * @since 2025-03-28 17:09:33
 */
public class SysNoticeUser implements Serializable {
    private static final long serialVersionUID = -78694774485498822L;
/**
     * 公告ID
     */
    private String noticeId;
/**
     * 用户账号
     */
    private String userNo;


    public String getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(String noticeId) {
        this.noticeId = noticeId;
    }

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

}

