package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.WarningEvent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: Dr.Monster
 * @Slogan: The never-compromising angry youth.
 * @PackageName: iet.ustb.sf.dao
 * @Date: 2022/08/10/14:15
 * @Description:
 */
public interface WarningEventDao extends JpaSpecificationExecutor<WarningEvent>, JpaRepository<WarningEvent, Serializable> {

    @Query(value = "select  * from Warning_Event where msgID = ?1 and alertType = 'info'", nativeQuery = true)
    List<WarningEvent> findAlertInfosByMsgID(String msgID);

    @Query(value = "select  * from Warning_Event where msgID = ?1 and alertType = 'alert'", nativeQuery = true)
    List<WarningEvent> findAlertsByMsgID(String msgID);

    @Query(value = "select  * from Warning_Event where msgID = ?1 and userNo = ?2 and alertType = 'info'", nativeQuery = true)
    WarningEvent findAlertsByMsgIDAndUserNo(String msgID , String userNo);


    @Query(value = "select  * from Warning_Event where serviceName = ?1 ", nativeQuery = true)
    List<WarningEvent> findAlertsByServiceName(String serviceName);

    @Query(value = "select  * from Warning_Event where alertType = ?1 and serviceName = ?2 ", nativeQuery = true)
    List<WarningEvent> findAlertsByAlertTypeAndServiceName(String alertType, String serviceName);

    @Query(value = "select  * from Warning_Event where alertLevel = ?1 and serviceName = ?2 ", nativeQuery = true)
    List<WarningEvent> findAlertsByAlertLevelAndServiceName(String alertLevel, String serviceName);

    @Query(value = "select  * from Warning_Event where userNo = ?1 and serviceName = ?2 and isOffline = '1' and isPush = '0' and alertType = 'info'", nativeQuery = true)
    List<WarningEvent> getOfflineWaringEvent(String userNo, String serviceName);


    @Query(value = "select count(a.msgid)                                  as total,\n" +
            "       sum(case when a.status = 1 then 1 else 0 end) as done,\n" +
            "       sum(case when a.status = 0 then 1 else 0 end) as unDone\n" +
            "from (select distinct we.msgid, status\n" +
            "      from warning_event we\n" +
            "               join warning_event_body web on we.warningeventbody_id = web.id\n" +
            "      where web.pushroleids like ?1\n" +
            "        and we.createdatetime between ?2 and ?3) a" , nativeQuery = true)
    Map<String , Object> getUndoneRateByRoleID(String roleID , Date createTime , Date endTime);

    @Query(value = "select *\n" +
            "from (\n" +
            "         (select count(a.msgid)                                as total,\n" +
            "                 sum(case when a.status = 1 then 1 else 0 end) as done,\n" +
            "                 sum(case when a.status = 0 then 1 else 0 end) as unDone\n" +
            "          from (select distinct we.msgid, status\n" +
            "                from warning_event we\n" +
            "                         join warning_event_body web on we.warningeventbody_id = web.id\n" +
            "                where web.pushroleids like ?1\n" +
            "                  and we.createdatetime between ?2 and ?3) a) a1 ,\n" +
            "      (select b.isFalse as isFalse\n" +
            "       from (select count(wi.id) as isFalse\n" +
            "             from warning_info wi\n" +
            "             where roleID = ?1\n" +
            "               and status = 2\n" +
            "               and isFalse = 1\n" +
            "               and wi.createDateTime between ?2 and ?3) b) b1\n" +
            "         )" , nativeQuery = true)
    Map<String , Object> getUndoneRateByRoleIDNew(String roleID , Date createTime , Date endTime);

    @Query(value = "select count(id)                                    as total,\n" +
            "       sum(case when status = 0 then 1 else 0 end)  as undone,\n" +
            "       sum(case when status = 2 then 1 else 0 end)  as done,\n" +
            "       sum(case when isFalse = 1 then 1 else 0 end) as isFalse\n" +
            "from warning_info\n" +
            "where roleID = ?1 and createDateTime between ?2 and ?3" , nativeQuery = true)
    Map<String , Object> getUndoneRateByRoleIDNew_1(String roleID , Date createTime , Date endTime);


    @Query(value = "select distinct (moduleCode) , moduleName as Name from warning_info\n" +
            "group by moduleCode , Name" , nativeQuery = true)
    List<Map<String , String>> findModuleInfoList();

    @Query(value = "select distinct (areaName) as Name from warning_event" , nativeQuery = true)
    List<String> findAreaNameList();

    @Query(value = "select distinct (deviceName) as Name from warning_event" , nativeQuery = true)
    List<String> findDeviceNameList();

    @Query(value = "select distinct (pointName) as Name from warning_event" , nativeQuery = true)
    List<String> findPointNameList();

}
