package iet.ustb.sf.vo.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.annotations.Proxy;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * @Author: Dr.Monster
 * @Title: WaringInfo
 * @Date: 23/10/31 10:5917
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */
@Data
@Entity
@Table(name = "Warning_Info")
@Proxy(lazy = false)
public class WarningInfo extends BaseEntity{
    //区域ID
    @Column
    String areaID;

    //区域名称
    @Column
    String areaName;

    //点位ID
    @Column
    String pointID;

    //点位名称
    @Column
    String pointName;

    //设备ID
    @Column
    String deviceID;

    //设备名称
    @Column
    String deviceName;

    //报警内容
    @Column(length = 2048)
    String alertContent;

    //诊断信息
    @Column(length = 2048)
    String diagnosticMessage;

    //报警分析
    @Column(length = 2048)
    String alertAnalysis;

    //处理建议
    @Column(length = 2048)
    String alertAdvice;

    //模块名称
    @Column
    String moduleName;

    //模块代码
    @Column
    String moduleCode;

    //报警级别，1-一级，2-二级，3-三级，0-未评级
    @Column
    int alertLevel;

    //处置情况,0-未处理,1-已挂起,2-已处理,3-持续中,9-审核中
    @Column
    int status = 0;

    //处理人
    @Column
    String dealUser;

    //人工建议
    @Column(length = 2048)
    String humanAdvice;

    //处理时间
    @Column
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date dealTime;

    //是否确认,0-未确认，1-确认
    @Column
    int isConfirm;

    //报警类型名称
    @Column(length = 1024)
    String alertTypeName;

    //报警值
    @Column
    Float alertValue;

    //备注
    @Column(length = 2048)
    String remark = "";

    //0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测，6-趋势校验报,7-故障诊断
    @Column
    Integer warningType;

    //消息ID
    @Column
    String msgID;

    //是否是误报警,0-N,1-Y
    @Column
    Integer isFalse;

    //确认是否是误报警,0-N,1-Y
    @Column
    Integer isConfirmFalse;

    //报警规则ID
    @Column
    String warningRuleID;

    //角色ID，多个角色用英文逗号隔开
    @Column
    String roleID;

    //误报处理反馈
    @Column
    String falseFeedBack;


    @Transient
    String diffTime;

    /**
     * 班次,0，1，2-大夜班，白班，小夜班
     */
    @Column
    String classes;

    /**
     * 班组,0，1，2，3-甲，乙，丙，丁
     */
    @Column
    String team;

}



