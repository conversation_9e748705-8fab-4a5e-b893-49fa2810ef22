package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.DictionaryDtl;
import iet.ustb.sf.service.DictionaryDtlService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数据字典详情
 *
 * <AUTHOR>
 * @create 2023-02-09
 */
@RestController
@RequestMapping("/dictionaryDtl")
@Api(value = "数据字典详情", tags = "数据字典详情")
public class DictionaryDtlController {

    @Autowired
    private DictionaryDtlService dictionaryDtlService;

    @PostMapping("/findAll")
    @ApiOperation(value = "查询所有", notes = "查询所有")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<DictionaryDtl> dictDtlList = dictionaryDtlService.findAll();
            ajaxJson.setData(dictDtlList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findPageByMultiCondition")
    @ApiOperation(value = "按多条件查找分页", notes = "入参：{\"dictId\": \"edbce4b6-d9a8-4936-a5ad-aa114d08e25e\",\"code\": \"a1\",\"value\": \"0.3\"}")
    public AjaxJson findPageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<DictionaryDtl> dictDtlPage = dictionaryDtlService.findPageByMultiCondition(jsonObject);
            ajaxJson.setData(dictDtlPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存", notes = "保存")
    public AjaxJson save(@RequestBody DictionaryDtl dictionary) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            dictionaryDtlService.save(dictionary);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "删除")
    public AjaxJson delete(@RequestBody DictionaryDtl dictionary) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            dictionaryDtlService.delete(dictionary);
            ajaxJson.setData("删除成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findByDictCode")
    @ApiOperation(value = "按字典编码查找字典详情列表", notes = "入参：{\"dictCode\": \"scoreRate\"}")
    public AjaxJson findByDictCode(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            String dictCode = jsonObject.getString("dictCode");
            List<DictionaryDtl> dictDtlList = dictionaryDtlService.findByDictCode(dictCode);
            ajaxJson.setData(dictDtlList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

}
