package iet.ustb.sf.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import iet.ustb.sf.client.DataPlatformClient;
import iet.ustb.sf.config.NacosConfig;
import iet.ustb.sf.dao.ResourceDao;
import iet.ustb.sf.dao.RoleDao;
import iet.ustb.sf.mapper.DsResourceMapper;
import iet.ustb.sf.utils.CodeGenerator;
import iet.ustb.sf.utils.constant.EnumConstant;
import iet.ustb.sf.vo.AjaxJson;
import iet.ustb.sf.vo.CustomExceptionVo;
import iet.ustb.sf.vo.domain.Resource;
import iet.ustb.sf.service.ResourceService;
import iet.ustb.sf.utils.EntityUtils;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.vo.AvailableAndIcon;
import iet.ustb.sf.vo.feign.BDMenuVo;
import io.micrometer.core.instrument.util.StringUtils;
// import javafx.scene.control.Menu;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ResourceServiceImpl implements ResourceService {

    @Autowired
    ResourceDao resourceDao;

    @Autowired
    RoleDao roleDao;

    @Autowired
    DsResourceMapper dsResourceMapper;

    @Autowired
    DataPlatformClient dataPlatformClient;

    @Autowired
    private NacosConfig nacosConfig;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * 新增菜单
     */
    @Override
    public String doCreateResource(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return "";
        }
        Resource resource = ToolsUtil.jsonObjectToEntity(jsonObject, Resource.class);

        //新增的时候生成菜单编码
        resource.setCode(CodeGenerator.generateMenuCode(this,resource.getParentCode()));

        //调用数据中台接口
        if(EnumConstant.IS_NO_FLAG_1.equals(nacosConfig.getApiBd())
                && nacosConfig.getApiBdName().equals(resource.getServiceName())
                && StringUtils.isNotEmpty(resource.getParentCode())){
            saveBDMenu(resource);
        }

        return doSaveResource(resource);
    }

    /**
     * 调用中台接口保存菜单信息
     */
    private void saveBDMenu(Resource resource) {
        BDMenuVo menuVo = new BDMenuVo();
        menuVo.setCode(resource.getCode());
        menuVo.setName(resource.getName());
        if(nacosConfig.getApiBdMenu().equals(resource.getParentCode())){
            menuVo.setParentCode(EnumConstant.IS_NO_FLAG_0);
        }else {
            menuVo.setParentCode(resource.getParentCode());
        }

        menuVo.setDesc(resource.getDescription());
        menuVo.setMenuType(resource.getType());
        menuVo.setIcon(nacosConfig.getMinioUrl() + resource.getIconUrl());
        menuVo.setUrl(resource.getUrl());
        menuVo.setSort(resource.getSort());

        AjaxJson result = dataPlatformClient.saveOrUpdateMenu(menuVo);
        if(!EnumConstant.BD_CODE_200.equals(result.getCode())){
            throw new CustomExceptionVo(result.getCode(),result.getMessage());
        }
    }

    @Transactional
    public String doSaveResource(Resource resource) {
        return resourceDao.save(resource).getId();
    }

    /**
     * 更新菜单信息
     * @param jsonObject
     * @return
     */
    @Override
    @Transactional
    public String doUpdateResource(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return "";
        }
        Resource resource = ToolsUtil.jsonObjectToEntity(jsonObject, Resource.class);

        if(EnumConstant.IS_NO_FLAG_1.equals(nacosConfig.getApiBd())
                && nacosConfig.getApiBdName().equals(resource.getServiceName())
                && !nacosConfig.getApiBdMenu().equals(resource.getCode())){
            saveBDMenu(resource);
        }

        return resourceDao.save(resource).getId();
    }

    /**
     * 删除菜单信息及其关联的角色关系
     * @param resource
     */
    @Override
    @Transactional
    public void doDeleteResource(Resource resource) {

        if (ToolsUtil.isEmpty(resource) || ArrayUtil.isEmpty(resource.getIdList())) {
            return;
        }
        String parentIds = String.join(",", resource.getIdList());
        List<Resource> resourceList = dsResourceMapper.getChildResources(parentIds);

        if (ToolsUtil.isEmpty(resourceList)) {
            return;
        }
        //存在子菜单不允许删除
        if(resourceList.size() > resource.getIdList().size()){
            throw new CustomExceptionVo("500","存在子菜单，不允许删除！");
        }


        if(EnumConstant.IS_NO_FLAG_1.equals(nacosConfig.getApiBd())){
            deleteBDMenus(resourceList);
        }


        List<String> rscIDs = resourceList.stream().map(Resource::getId).collect(Collectors.toList());
        roleDao.deleteRelateByRcsIDs(rscIDs);
        resourceDao.deleteResourcesByRcsIDs(rscIDs);

    }

    /**
     * 调用数据中台接口，删除相关菜单信息
     * @param resourceList
     */
    private void deleteBDMenus(List<Resource> resourceList) {
        BDMenuVo menuVo = new BDMenuVo();
        List<Resource> deleteList = new ArrayList<>();
        resourceList.forEach(resource -> {
           if(nacosConfig.getApiBdName().equals(resource.getServiceName())
                   && StringUtils.isNotEmpty(resource.getParentCode())){
             deleteList.add(resource);
           }
        });
        if(CollUtil.isEmpty(deleteList)){
            return;
        }
        List<String> menus = deleteList.stream().map(Resource::getCode).collect(Collectors.toList());
        menuVo.setMenus(menus);
        AjaxJson result = dataPlatformClient.deleteMenu(menuVo);
        if(!EnumConstant.BD_CODE_200.equals(result.getCode())){
            throw new CustomExceptionVo(result.getCode(),result.getMessage());
        }
    }

    @Override
    public Page<Resource> findAllResource(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);
        Page<Resource> rscPage = resourceDao.findAll(new Specification<Resource>() {
            @Override
            public Predicate toPredicate(Root<Resource> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<Predicate>();
                Predicate[] arr = new Predicate[list.size()];

                String rscName = jsonObject.getString("rscName");
                String serviceName = jsonObject.getString("serviceName");
                String type = jsonObject.getString("type");

                if (!ToolsUtil.isEmpty(rscName)) {
                    list.add(cb.like(root.get("name"), "%" + rscName + "%"));
                }
                if (!ToolsUtil.isEmpty(serviceName)) {
                    list.add(cb.equal(root.get("serviceName"), serviceName));
                }
                if (!ToolsUtil.isEmpty(type)) {
                    list.add(cb.equal(root.get("type"), type));
                }

                list.add(cb.equal(root.get("status"), 1));
                cq.where(list.toArray(arr));
                return null;
            }
        }, pageable);
        return rscPage;
    }

    /**
     *
     * @param resource
     * @return
     */
    @Override
    public List<Resource> findAllOfResource(Resource resource) {
        return resourceDao.findAll();
    }

    /**
     * 查询列表
     * @param resource
     * @return
     */
    @Override
    public List findAllResourceNoPage(Resource resource) {
        return dsResourceMapper.findResource(resource);
    }

    /**
     * 查询列表及图标
     * @param jsonObject
     * @return
     */
    @Override
    public List findAllResourceNoPageWithIcon(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            List<Object[]> objects = resourceDao.findAllAvailableWithIcon();
            //Object转化为实体类List
            List<AvailableAndIcon> availableAndIconList = EntityUtils.castEntity(objects, AvailableAndIcon.class, new AvailableAndIcon());
            return availableAndIconList;

        } else {
            //查询列表及图标
            return findResourceByUserNoAndServiceNameAndTypeWithIcon(jsonObject);


        }
    }

    /**
     * 根据菜单信息查询相关所有菜单数据
     * @param resource
     * @return
     */
    @Override
    public List<Resource> getResourceInfo(Resource resource) {
        List<Resource> resourceList = dsResourceMapper.getResourceInfo(resource);

        return resourceList;
    }

    /**
     * 根据菜单信息查询相关所有菜单数据，并以树结构将数据返回
     * @param resource
     * @return
     */
    @Override
    public List<Resource> getResourceTree(Resource resource) {
        List<Resource> resourceList = dsResourceMapper.getResourceInfo(resource);

        List<Resource> resourcesTree =  buildResourceTree(resourceList);

        return resourcesTree;
    }

    /**
     * 按条件查询列表及图标
     * @param jsonObject
     * @return
     */
    private List findResourceByUserNoAndServiceNameAndTypeWithIcon(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return new ArrayList<>();
        } else {
            StringBuilder sb = new StringBuilder("select distinct  dr.id ,\n" +
                    "dr.code code\n" +
                    ",dr.icon icon\n" +
                    ",dr.isshow isShow\n" +
                    ",dr.name name\n" +
                    ",dr.parentid parentId\n" +
                    ",dr.servicename serviceName\n" +
                    ",dr.sort sort\n" +
                    ",dr.status status\n" +
                    ",dr.type type\n" +
                    ",dr.url url\n" +
                    ",dr.isselected isSelected\n" +
                    ",dr.ip ip\n" +
                    ",dr.port port\n" +
                    ",dr.pluginsize pluginSize\n" +
                    ",dr.icontype iconType\n" +
                    ",dr.deskicon deskIcon\n" +
                    ",dr.handledeveloper handleDeveloper\n" +
                    ",dr.handleuser handleUser\n" +
                    ",img.name imgIconName, CAST(img.resource AS CHAR)  imgIconResource  \n" +
                    "from ds_resource dr\n" +
                    "         left join ds_role_resourcelist drr on dr.id = drr.resourceList_id\n" +
                    "         left join ds_role_userlist dsu on dsu.Role_id = drr.Role_id\n" +
                    "         left join du_user du on dsu.userList_id = du.id\n" +
                    "         left join icon_img img on dr.icon = img.id\n" +
                    " where dr.status = 1 ");
            String userNo = jsonObject.getString("userNo");
            String type = jsonObject.getString("type");
            String serviceName = jsonObject.getString("serviceName");
            if (!ToolsUtil.isEmpty(userNo)) {
                sb.append(" and du.userNo = '" + userNo + "' ");
            }
            if (!ToolsUtil.isEmpty(type)) {
                sb.append(" and dr.type = '" + type + "' ");
            }
            if (!ToolsUtil.isEmpty(serviceName)) {
                sb.append(" and dr.serviceName = '" + serviceName + "' ");
            }

            SQLQuery sqlQuery = entityManager.createNativeQuery(sb.toString()).unwrap(SQLQuery.class);
            Query query = sqlQuery.setResultTransformer(Transformers.aliasToBean(AvailableAndIcon.class));
            List<AvailableAndIcon> list = query.getResultList();
            entityManager.clear();
            return list;


        }
    }

    @Override
    public Resource findResourceByParentID(JSONObject jsonObject) {
        List<Resource> resourceList = resourceDao.findResourceByParentID(jsonObject.getString("parentID"));
        Resource root = null;
        for (Resource item : resourceList) {
            if (ToolsUtil.isEmpty(item.getParentId())) {
                root = item;
                break;
            }
        }
        return buildResource(root, resourceList);
    }


    public Resource buildResource(Resource root, List<Resource> resourceList) {
        List<Resource> children = new ArrayList<>();
        for (Resource resource : resourceList) {
            if (root.getId().equals(resource.getParentId())) {
                children.add(resource);
                buildResource(resource, resourceList);
            }
        }
        root.setChildren(children);
        return root;
    }

    @Override
    public Resource findOneResourceByID(JSONObject jsonObject) {
        return resourceDao.getById(jsonObject.getString("id"));
    }

    /**
     * 根据角色ID查询选中的菜单和未选中的菜单
     * @param jsonObject
     * @return
     */
    @Override
    public List<Resource> filterResourceSelected(JSONObject jsonObject) {

        String roleID = jsonObject.getString("roleID");
        if (ToolsUtil.isEmpty(roleID)) {
            return new ArrayList<>();
        }
        return dsResourceMapper.filterResourceSelected(roleID);
    }


    public boolean checkRscAvailable(Resource resource) {
        return resource.getStatus() == 1;
    }


    /**
     * 修改父级菜单权限，会同时影响子级菜单
     * 修改子级菜单，不会影响父级菜单
     * 子级新增角色时，父级也需要同时增加对应角色
     * 子级删除角色是，父级不需要删除对应角色
     * 同时还要注意角色是否会重复
     **/
    @Override
    @Transactional
    public String relateRole(JSONObject jsonObject) {
        String rscID = jsonObject.getString("id");
        if (ToolsUtil.isEmpty(rscID)) {
            return "";
        }

        //获取当前菜单的ID
        Resource resource = resourceDao.getById(rscID);
        if (ToolsUtil.isEmpty(resource)) {
            return "";
        }

        //获取当前菜单下子级的菜单
        List<Resource> children = findAvaResourceByParentIDWithParent(rscID);

        if (!checkRscAvailable(resource)) {
            return "资源不可用";
        }

        //需要新增角色的菜单
        Set<String> addRscIDs = new HashSet<>();

        //需要删除角色的菜单
        Set<String> delRscIDs = new HashSet<>();

        //查询顶层及顶层的顶层，直到parentID为空
        Resource parent;
        String parentID = rscID;
        while (true) {
            parent = resourceDao.getById(parentID);
            if (ToolsUtil.isEmpty(parent) || ToolsUtil.isEmpty(parent.getParentId())) {
                break;
            }
            parentID = parent.getParentId();
            addRscIDs.add(parentID);
        }

        for (Resource item : children) {
            addRscIDs.add(item.getId());
        }

        for (Resource item : children) {
            delRscIDs.add(item.getId());
        }

        addRscIDs.add(rscID);
        delRscIDs.add(rscID);

        List<String> deleteRoleIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("deleteRoleIDs"), String.class);
        for (String tmpRscID : delRscIDs) {
            releaseRole(tmpRscID, deleteRoleIDs);
        }

        List<String> addRoleIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("addRoleIDs"), String.class);
        for (String tmpRscID : addRscIDs) {
            rebuildRelate(tmpRscID, addRoleIDs);
        }

        return resource.getId();
    }

    @Transactional
    public void releaseRole(String rcsID, List<String> deleteRoleIDs) {
        if (ToolsUtil.isEmpty(deleteRoleIDs) || ToolsUtil.isEmpty(rcsID)) {
            return;
        }
        roleDao.deleteRelateByRcsIDandRoleIDs(rcsID, deleteRoleIDs);
    }


    @Transactional
    public void rebuildRelate(String rcsID, List<String> addRoleIDs) {
        if (ToolsUtil.isEmpty(addRoleIDs) || ToolsUtil.isEmpty(rcsID)) {
            return;
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("insert into ds_role_resourcelist (Role_id, resourceList_id) VALUES ");
        int check = 0;
        for (String roleID : addRoleIDs) {
            int count = findCountByRoleIDAndRscID(roleID, rcsID);
            if (count >= 1) {
                continue;
            }
            stringBuilder.append(" ( ");
            stringBuilder.append("'" + roleID + "'");
            stringBuilder.append(" , ");
            stringBuilder.append("'" + rcsID + "'");
            stringBuilder.append(" ) , ");
            check++;
        }
        if (check == 0) {
            return;
        }
        Query query = this.entityManager.createNativeQuery(ToolsUtil.replaceLast(stringBuilder.toString(), ",", ";"));
        query.executeUpdate();
    }


    public int findCountByRoleIDAndRscID(String roleID, String rscID) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select cast(count(*) AS signed) AS COUNT from ds_role_resourcelist where role_id = '" + roleID
                + "' and resourcelist_id = '" + rscID + "'");
        Query query = this.entityManager.createNativeQuery(stringBuilder.toString());
        List<BigInteger> list = query.getResultList();
        if (ToolsUtil.isEmpty(list)) {
            return 0;
        }
        return list.get(0).intValue();
    }

    //按条件查询列表
    @Override
    public List<Resource> findResourceByUserNoAndServiceNameAndType(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return new ArrayList<>();
        } else {
            StringBuilder sb = new StringBuilder("select distinct dr.*\n" +
                    "from ds_resource dr\n" +
                    "         join ds_role_resourcelist drr on dr.id = drr.resourceList_id\n" +
                    "         join ds_role_userlist dsu on dsu.Role_id = drr.Role_id\n" +
                    "         join du_user du on dsu.userList_id = du.id\n" +
                    " where dr.status = 1 ");
            String userNo = jsonObject.getString("userNo");
            String type = jsonObject.getString("type");
            String serviceName = jsonObject.getString("serviceName");
            if (!ToolsUtil.isEmpty(userNo)) {
                sb.append(" and du.userNo = '" + userNo + "' ");
            }
            if (!ToolsUtil.isEmpty(type)) {
                sb.append(" and dr.type = '" + type + "' ");
            }
            if (!ToolsUtil.isEmpty(serviceName)) {
                sb.append(" and dr.serviceName = '" + serviceName + "' ");
            }

            return (List<Resource>) entityManager.createNativeQuery(sb.toString(),
                    Resource.class).getResultList();
        }
    }


    public List<Resource> findRoots(List<Resource> resourceList) {
        List<Resource> roots = new ArrayList<>();
        Set<String> ids = new HashSet<>();
        for (Resource item : resourceList) {
            ids.add(item.getId());
        }

        for (Resource item : resourceList) {
            if (ToolsUtil.isEmpty(item.getParentId())) {
                roots.add(item);
            } else {
                if (!ids.contains(item.getParentId())) {
                    roots.add(item);
                }
            }
        }
        return roots;
    }


    @Override
    public List<Resource> findRootResource(Resource resource) {
        List<Resource> resourceList = dsResourceMapper.findResource(resource);
        return findRoots(resourceList);
    }

    @Override
    public void batchUpdateResource(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return;
        } else {
            List<Resource> resourceList = ToolsUtil.jsonObjectToEntityList(jsonObject.get("rscList"), Resource.class);
            resourceDao.saveAll(resourceList);
        }
    }

    @Override
    public List<String> findAllHandleUser() {
        return resourceDao.findAllHandleUser();
    }

    @Override
    public List<Map<String, Object>> findAllHandleUserNum(String userNo) {
        return resourceDao.findAllHandleUserNum(userNo);
    }

    @Override
    public Optional<Resource> findById(String id) {
        return resourceDao.findById(id);
    }

    public List<Resource> findAvaResourceByParentIDWithParent(String parentID) {
        String mhT = "\\:=";
        StringBuilder sb = new StringBuilder("SELECT\n" +
                "\tDATA.*\n" +
                "FROM\n" +
                "\t(\n" +
                "\tSELECT\n" +
                "\t\t@ids AS _ids,\n" +
                "\t\t( SELECT @ids" + mhT + "GROUP_CONCAT( id ) FROM ds_resource WHERE FIND_IN_SET( parentId, @ids ) ) AS cids\n" +
                "\tFROM\n" +
                "\t\tds_resource,\n" +
                "\t\t( SELECT @ids" + mhT + "'" + parentID + "') b\n" +
                "\tWHERE\n" +
                "\t\t@ids IS NOT NULL\n" +
                "\t) ID,\n" +
                "\tds_resource DATA\n" +
                "WHERE\n" +
                "\tFIND_IN_SET( DATA.id, ID._ids ) and status = 1");
        List<Resource> resourceList =
                entityManager.createNativeQuery(sb.toString(),
                        Resource.class).getResultList();
        return resourceList;
    }


    public List<Resource> findAllResourceByParentIDWithParent(String parentID) {
        String mhT = "\\:=";
        StringBuilder sb = new StringBuilder("SELECT\n" +
                "\tDATA.*\n" +
                "FROM\n" +
                "\t(\n" +
                "\tSELECT\n" +
                "\t\t@ids AS _ids,\n" +
                "\t\t( SELECT @ids" + mhT + "GROUP_CONCAT( id ) FROM ds_resource WHERE FIND_IN_SET( parentId, @ids ) ) AS cids\n" +
                "\tFROM\n" +
                "\t\tds_resource,\n" +
                "\t\t( SELECT @ids" + mhT + "'" + parentID + "') b\n" +
                "\tWHERE\n" +
                "\t\t@ids IS NOT NULL\n" +
                "\t) ID,\n" +
                "\tds_resource DATA\n" +
                "WHERE\n" +
                "\tFIND_IN_SET( DATA.id, ID._ids )");
        List<Resource> resourceList =
                entityManager.createNativeQuery(sb.toString(),
                        Resource.class).getResultList();
        return resourceList;
    }


    /**
     * 获取下一个一级菜单的编号（查询数据库获取最大编号 + 1）
     */
    public String getNextTopLevelCode() {
        // 查询数据库，返回当前最大编号（如"002"）
        String maxCode = dsResourceMapper.getMaxTopLevelCode();
        int nextCode = (maxCode == null) ? 1 : Integer.parseInt(maxCode) + 1;
        // 转换成三位格式，例如 001, 002, 003...
        return String.format("%03d", nextCode);
    }

    /**
     * 获取某个父菜单下的下一个子菜单编号
     */
    public  String getNextSubLevelCode(String parentCode) {
    // 获取父级层级编号
    String parentLevelCode = parentCode.substring(parentCode.lastIndexOf(EnumConstant.UNDERLINE) + 1);
    // 获取当前最大子菜单编号（如"002-002"）
    String maxSubCode = dsResourceMapper.getMaxSubLevelCode(parentCode);

    // 提取 maxSubCode 最后的部分，并判断是否是数字
    String lastPart = (maxSubCode != null) ? maxSubCode.substring(maxSubCode.lastIndexOf(EnumConstant.HYPHEN) + 1) : null;
    int nextSubCode;

    // 判断 lastPart 是否是纯数字
    if (lastPart != null && lastPart.matches("\\d+")) {
        nextSubCode = Integer.parseInt(lastPart) + 1;
    } else {
        // 如果 maxSubCode 不是数字，默认从 001 开始
        nextSubCode = 1;
    }

    // 生成 "002-001", "002-002"
    return parentLevelCode + EnumConstant.HYPHEN + String.format("%03d", nextSubCode);
    }


    /**
     * 将菜单数据转换成树结构
     * @param resources
     * @return
     */
    public List<Resource> buildResourceTree(List<Resource> resources) {
        Map<String, Resource> resourceMap = resources.stream()
                .filter(r -> r.getId() != null)
                .collect(Collectors.toMap(Resource::getId, resource -> resource));

        List<Resource> rootResources = new ArrayList<>();

        // 遍历菜单，构建树结构
        for (Resource resource : resources) {
            if (StringUtils.isEmpty(resource.getParentId())) {
                rootResources.add(resource);
            } else {
                Resource parent = resourceMap.get(resource.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(resource);
                }
            }
        }
        return rootResources;
    }
}
