package iet.ustb.sf.vo.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * icon图标
 * <AUTHOR>
 * @create 2022-08-15
 */
@Data
@Entity
@Table(name = "icon_img")
@ApiModel(value = "icon图标")
public class IconImg extends BaseEntity {

    @Column(nullable = false)
    @ApiModelProperty(value = "名称")
    private String name;

    /*@Column(nullable = false)
    @ApiModelProperty(value = "资源路径")
    private String path;*/

    @Column(length = 20560, nullable = false)
    @ApiModelProperty(value = "资源(base64加密)")
    private String resource;

    @ApiModelProperty(value = "图标类型：1-桌面图标 2-菜单图标")
    private Integer iconType;

    //文件上传的名称，可以编辑
    @Column(name = "file_name")
    private String fileName;

    @Transient
    private Integer pageIndex;

    @Transient
    private Integer pageSize;

    @Transient
    private MultipartFile file;


}
