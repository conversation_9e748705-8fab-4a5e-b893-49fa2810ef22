package iet.ustb.sf.vo.domain;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 岗级表
 */
@Data
@Entity
@Table(name = "DU_POST_LEVEL")
public class PostLevel extends BaseEntity {

    /**
     * 岗级编号
     */
    @Column(length = 64, unique = true, nullable = false)
    private String postLevelCode;

    /**
     * 岗级名称
     */
    @Column(length = 64, nullable = false)
    private String postLevelName;

    /**
     * 等级
     */
    @Column(length = 64, nullable = false)
    private String ranLev;

    /**
     * 描述
     */
    @Column(nullable = false)
    private String postLevelDesc;

    /**
     * 类型：0-默认
     */
    @Column(length = 64, nullable = false)
    private String postLevelType;

    /**
     * 状态：0-启用,1-禁用
     */
    @Column(length = 64, nullable = false)
    private String status;

    /**
     * 操作标识：N 新增,U修改,D删除
     */
    @Column(length = 64)
    private String operStus;

    /**
     * 判重项目值域
     */
    private String repeatField;

}
