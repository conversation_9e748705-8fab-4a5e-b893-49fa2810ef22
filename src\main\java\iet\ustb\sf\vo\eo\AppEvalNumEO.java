package iet.ustb.sf.vo.eo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 应用评价排行
 *
 * <AUTHOR>
 * @create 2022-11-03
 * @see Serializable
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppEvalNumEO implements Serializable {

    /**
     * 模块
     */
    private String serviceNo;
    /**
     * 模块
     */
    @Excel(name = "模块",width = 20)
    private String serviceName;

    /**
     * 模型名称
     */
    private String modelNo;

    /**
     * 模型名称
     */
    @Excel(name = "模型名称",width = 50)
    private String modelName;

    /**
     * 次数
     */
    @Excel(name = "次数")
    private Long num;
}
