package iet.ustb.sf.client;

import iet.ustb.sf.config.interceptor.annotation.AutoLog;
import iet.ustb.sf.vo.AjaxJson;
import iet.ustb.sf.vo.feign.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(value = "dwops-gateway", path = "/dw/permission/open/zhihuizhuomian")
public interface DataPlatformClient {

    /**
     * 新增/修改用户
     * @param bdUserVo
     * @return
     */
    @AutoLog
    @PostMapping("/saveOrUpdateUser")
    AjaxJson saveOrUpdateUser(BDUserVo bdUserVo);

    /**
     * 批量删除用户，自动级联删除用户与角色的绑定关系
     * @param bdUserVo
     * @return
     */
    @AutoLog
    @PostMapping("/deleteUser")
    AjaxJson deleteUser(BDUserVo bdUserVo);

    /**
     * 新增/修改角色
     * @param bdRoleVo
     * @return
     */
    @AutoLog
    @PostMapping("/saveOrUpdateRole")
    AjaxJson saveOrUpdateRole(BDRoleVo bdRoleVo);

    /**
     * 批量删除角色，自动级联删除用户与角色、角色与菜单的绑定关系
     * @param bdRoleVo
     * @return
     */
    @AutoLog
    @PostMapping("/deleteRole")
    AjaxJson deleteRole(BDRoleVo bdRoleVo);

    /**
     * 新增/修改菜单
     * @param bdMenuVo
     * @return
     */
    @AutoLog
    @PostMapping("/saveOrUpdateMenu")
    AjaxJson saveOrUpdateMenu(BDMenuVo bdMenuVo);

    /**
     * 批量删除菜单，自动级联删除角色与菜单的绑定关系
     * @param bdMenuVo
     * @return
     */
    @AutoLog
    @PostMapping("/deleteMenu")
    AjaxJson deleteMenu(BDMenuVo bdMenuVo);

    /**
     * 以角色为主，给该角色分配菜单权限
     * @return
     */
    @AutoLog
    @PostMapping("/updateRoleMenus")
    AjaxJson updateRoleMenus(BDRoleAndMenuVo bdRoleAndMenuVo);

    /**
     * 以菜单为主，给该菜单关联角色
     * @return
     */
    @AutoLog
    @PostMapping("/updateMenuRoles")
    AjaxJson updateMenuRoles(BDRoleAndMenuVo bdRoleAndMenuVo);

    /**
     * 以用户为主，给用户分配角色
     * @return
     */
    @AutoLog
    @PostMapping("/updateUserRoles")
    AjaxJson updateUserRoles(BDUserAndRoleVo bdUserAndRoleVo);

    /**
     * 以角色为主，给角色关联
     * @return
     */
    @AutoLog
    @PostMapping("/updateRoleUsers")
    AjaxJson updateRoleUsers(BDUserAndRoleVo bdUserAndRoleVo);

}
