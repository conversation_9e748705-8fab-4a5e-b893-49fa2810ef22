package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.OrgDao;
import iet.ustb.sf.dao.UserDao;
import iet.ustb.sf.mapper.DuOrgMapper;
import iet.ustb.sf.mapper.DuUserMapper;
import iet.ustb.sf.utils.CodeGenerator;
import iet.ustb.sf.utils.constant.EnumConstant;
import iet.ustb.sf.vo.CustomExceptionVo;
import iet.ustb.sf.vo.domain.Org;
import iet.ustb.sf.service.OrgService;
import iet.ustb.sf.utils.OrgTreeUtil;
import iet.ustb.sf.utils.ToolsUtil;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.apachecommons.CommonsLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.collection.CollUtil;

import java.util.*;
import java.util.stream.Collectors;


@Service
@CommonsLog
public class OrgServiceImpl implements OrgService {
    @Autowired
    private OrgDao orgDao;

    @Autowired
    private DuUserMapper duUserMapper;

    @Autowired
    private DuOrgMapper duOrgMapper;

    @Override
    public Org save(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return null;
        }
        Org org = ToolsUtil.jsonObjectToEntity(jsonObject, Org.class);

        org.setOrgName(org.getOrgAllName());

        //只有在新增的时候才生成组织编码
        if(StringUtils.isEmpty(org.getOrgCode()) && StringUtils.isEmpty(org.getId())) {
            org.setOrgCode(CodeGenerator.generateOrgCode(this,org.getParentOrgCode()));
            //查询组织编码是否重复
            if(CollUtil.isNotEmpty(duOrgMapper.checkOrgCodesExist(Collections.singletonList(org.getOrgCode()),new ArrayList<>()))) {
                throw new CustomExceptionVo("400","该组织编码已存在，请修改成其他组织编码！");
            }
        }

        org.setOrgCurCode("CNY");
        return orgDao.save(org);
    }

    @Override
    public List<Org> findAll() {
        return orgDao.findAvailableList();
    }

    @Override
    public List<Org> findAvailableList() {
        return orgDao.findAvailableList();
    }

    /**
     * 根据父级编码查询组织信息
     * @param json
     * @return
     */
    @Override
    public List<Map<String, Object>> findListByOrgCode(JSONObject json) {
        return duOrgMapper.findListByOrgCode(json.getString("orgCode"));
    }

    @Override
    @Transactional
    public void doDeleteOrg(JSONObject jsonObject) {
        try {
            if (ToolsUtil.isEmpty(jsonObject.get("id"))) {
                return;
            }
            String id = jsonObject.getString("id");
            Org org = orgDao.getById(id);
            System.out.println(org.getOrgCode());
            List<String> tempList =new ArrayList<>();
            List<Org> orgList = this.findAvailableList();
            List<String> orgCodes = getOrgChild(org.getOrgCode(),tempList,orgList);
            orgCodes.add(org.getOrgCode());
            //判断这些组织下面有没有关联用户，如果有则不允许删除
            if(CollUtil.isNotEmpty(duUserMapper.getUsersByOrgCodes(orgCodes))){
                throw new CustomExceptionVo("400","该组织或下面组织有关联用户，不允许删除！");
            }
            for (String orgCode : orgCodes) {
                //删除组织信息
                orgDao.deleteByOrgCode(orgCode);
            }

        } catch (Exception e) {
            throw new CustomExceptionVo("400",e.getMessage());
        }

    }

    @Override
    public Org findByOrgCode(String orgCode) {
        return orgDao.findByOrgCode(orgCode);
    }

    @Override
    public List<Org> findOrgTreeByOrgCode(JSONObject jsonObject) throws Exception {
            String orgCode = jsonObject.getString("orgCode");

        // 根节点
        List<Org> rootList = new ArrayList<>();
        // 根节点，只获取板材所有厂
        if ("X".equals(orgCode)) {
            rootList.addAll(orgDao.findOrgsByParent("X50000000"));
            rootList.add(orgDao.findByOrgCode("X50000000"));
        } else {
            Org org = orgDao.findByOrgCode(orgCode);
            if (org == null) {
                throw new Exception("组织编号有误，请核实！");
            }
            rootList.add(org);
        }
        // 主体节点，包含根节点
        List<Org> bodyList = orgDao.findAvailableList();

        // 获取组织树
        List<Org> orgList = new OrgTreeUtil(rootList, bodyList).getTree();

        return orgList;
    }


    @Override
    public Org findOrgByUserNo(JSONObject jsonObject) {
        String userNo = jsonObject.getString("userNo");
        if (!ToolsUtil.isEmpty(userNo)) {
            List<Org> orgList = orgDao.findOrgByUserNo(userNo);
            if (!ToolsUtil.isEmpty(orgList)) {
                return orgList.get(0);
            }
        }
        return null;
    }

    @Override
    public List<Org> findOrgsByNames(JSONObject jsonObject) {
        if (!ToolsUtil.isEmpty(jsonObject.get("names"))) {
            List<String> names = (List<String>) jsonObject.get("names");
            return orgDao.findOrgsByNames(names);
        }
        return new ArrayList<>();
    }

    /**
     * 递归查询所有⼦组织编号
     *
     * @param orgCode  组织编号
     * @param tempList 临时清单
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @create 2023-03-27
     */
    private List<String> getOrgChild(String orgCode, List<String> tempList, List<Org> orgList) {
        //根据当前部门id查询所有⼦部门的id
        List<String> orgCodeList = orgList.stream()
                .filter(o -> orgCode.equals(o.getParentOrgCode()))
                .map(Org::getOrgCode)
                //⾃⼰的去数据库查询⽅法
                .collect(Collectors.toList());
        for (String s : orgCodeList) {
            tempList.add(s);
            //递归查询
            getOrgChild(s, tempList, orgList);
        }
        return tempList;
    }

    @Override
    public Set<String> getAllChildOrgList(String orgCode) {
        List<Org> orgList = this.findAvailableList();
        List<String> allOrgList = new ArrayList<>();
        // 将⽗id先存⼊集合
        allOrgList.add(orgCode);
        // 递归查找所有⼦部门id
        allOrgList = this.getOrgChild(orgCode, allOrgList, orgList);
        Set<String> allOrgSet = new HashSet<>(allOrgList);

        return allOrgSet;
    }


    /**
     * 获取下一个一级菜单的编号（查询数据库获取最大编号 + 1）
     */
    public String getNextTopLevelCode() {
        // 查询数据库，返回当前最大编号（如"002"）
        String maxCode = duOrgMapper.getMaxTopLevelCode();
        int nextCode = (maxCode == null) ? 1 : Integer.parseInt(maxCode) + 1;
        // 转换成三位格式，例如 001, 002, 003...
        return String.format("%03d", nextCode);
    }

    /**
     * 获取某个父菜单下的下一个子菜单编号
     */
    public  String getNextSubLevelCode(String parentCode) {
        // 获取父级层级编号
        String parentLevelCode = parentCode.substring(parentCode.lastIndexOf(EnumConstant.UNDERLINE) + 1);
        // 获取当前最大子菜单编号（如"002-002"）
        String maxSubCode = duOrgMapper.getMaxSubLevelCode(parentCode);

        int nextSubCode = (maxSubCode == null) ? 1 : Integer.parseInt(maxSubCode.substring(maxSubCode.lastIndexOf(EnumConstant.HYPHEN) + 1)) + 1;
        // 生成 "002-001", "002-002"
        return parentLevelCode + EnumConstant.HYPHEN + String.format("%03d", nextSubCode);
    }

    /**
     * 获取该组织下面的所有子级组织（包含该组织）
     * @param orgCodes
     * @return
     */
    public List<Org> getAllOrgs(List<String> orgCodes) {
        // 先查出 orgCode 对应的组织
        List<Org> allOrgs = duOrgMapper.checkOrgCodesExist(orgCodes, null);

        // 递归查找所有子级
        Set<String> allOrgCodes = new HashSet<>(orgCodes);
        List<Org> childOrgs = findAllChildren(allOrgs, allOrgCodes);
        allOrgs.addAll(childOrgs);

        return allOrgs;
    }

    /**
     * 递归查询父级组织下面的所有的子级组织
     * @param parentOrgs
     * @param allOrgCodes
     * @return
     */
    public List<Org> findAllChildren(List<Org> parentOrgs, Set<String> allOrgCodes) {
        if (parentOrgs.isEmpty()) return Collections.emptyList();

        // 查找子级
        List<String> parentCodes = parentOrgs.stream().map(Org::getOrgCode).collect(Collectors.toList());
        List<Org> children = duOrgMapper.checkOrgCodesExist(null, parentCodes);

        if (!children.isEmpty()) {
            allOrgCodes.addAll(children.stream().map(Org::getOrgCode).collect(Collectors.toSet()));
            // 递归查找子级
            children.addAll(findAllChildren(children, allOrgCodes)); 
        }
        return children;
    }
}
