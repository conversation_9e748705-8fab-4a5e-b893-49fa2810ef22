package iet.ustb.sf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import iet.ustb.sf.vo.domain.IconImg;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * icon图标
 * <AUTHOR>
 * @create 2022-08-15
 */
public interface IconImgService {

    /**
     * 上传图片（批量）
     * @param files
     * @param iconType
     * @throws IOException
     */
    void uploadImg(MultipartFile[] files, Integer iconType) throws Exception;

    /**
     * 查询所有图片
     * @return
     */
    List<IconImg> findAllImg();

    /**
     * 根据id查询
     * @param id
     * @return
     */
    IconImg findById(String id) throws Exception;

    /**
     * 删除
     * @param id
     */
    void deleteById(String id) throws Exception;

    /**
     * 分页查询图标信息
     * @param iconImg
     * @return
     */
    IPage<IconImg> getIconImgByPage(IconImg iconImg);

    /**
     * 更新图标信息
     * @param iconImg
     * @return
     */
    void updateIconImg(IconImg iconImg);


}
