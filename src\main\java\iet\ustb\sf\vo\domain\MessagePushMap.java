package iet.ustb.sf.vo.domain;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @Author: Dr.Monster
 * @Title: MessagePushMap
 * @Date: 24/01/02 16:2532
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */

@Data
@Entity
@Table(name = "Message_Push_Map")
public class MessagePushMap extends BaseEntity{

    //推送的人,多个人用,分割
    @Column
    String userNos;

    //统计对应角色下的报警信息,多个角色用,分割
    @Column(length = 4096)
    String checkRoleIDs;

    //推送的角色,多个角色用,分割
    @Column(length = 4096)
    String pushRoleIDs;

    //推送模式，0-userNo ， 1-roleIDs
    @Column
    Integer pushType = 0;

    @Column(length = 1024)
    String remark;

    //统计模式，0-厂，1-车间，2-岗位，3-模块,4-组织机构
    @Column
    int summaryType = 0;

    //车间或厂名(summaryType=0,1)时用
    @Column(length = 4096)
    String factoryName;

    @Column
    String parentID;

    @Column(length = 4096)
    String moduleCodes;

    @Column(length = 4096)
    String orgCodes;
}
