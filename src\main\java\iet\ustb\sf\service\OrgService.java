package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.Org;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface OrgService {
    /**
     * 保存
     *
     * @param org
     * @return
     * <AUTHOR>
     * @create 2022-07-01 11:33
     */
    Org save(JSONObject jsonObject);

    /**
     * 查询所有
     *
     * @return
     * <AUTHOR>
     * @create 2022-07-01 11:33
     */
    List<Org> findAll();

    /**
     * 查找可用列表
     *
     * @return {@link List }<{@link Org }>
     * <AUTHOR>
     * @create 2023-09-22
     */
    List<Org> findAvailableList();

    /**
     * 根据组织编号懒加载组织列表
     *
     * @param json
     * @return
     * <AUTHOR>
     * @create 2022-07-01 11:33
     */
    List<Map<String, Object>> findListByOrgCode(JSONObject json);

    /**
     * 根据组织编号查询组织信息
     *
     * @param orgCode
     * @return
     * <AUTHOR>
     * @create 2022-07-01 11:34
     */
    Org findByOrgCode(String orgCode);

    /**
     * 根据组织编号查询所有组织信息
     *
     * @param jsonObject
     * @return
     * <AUTHOR>
     * @create 2022-07-14 10:16
     */
    List<Org> findOrgTreeByOrgCode(JSONObject jsonObject) throws Exception;


    Org findOrgByUserNo(JSONObject jsonObject);

    List<Org> findOrgsByNames(JSONObject jsonObject);

    /**
     * 获取所有子组织列表
     *
     * @param orgCode 机构代码
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @create 2023-09-22
     */
    Set<String> getAllChildOrgList(String orgCode);

    void doDeleteOrg(JSONObject jsonObject);
}
