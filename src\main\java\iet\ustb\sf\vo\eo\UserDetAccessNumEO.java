package iet.ustb.sf.vo.eo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户详细访问次数排行
 *
 * <AUTHOR>
 * @create 2022-11-03
 * @see Serializable
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDetAccessNumEO implements Serializable {

    /**
     * 工号
     */
    @Excel(name = "工号")
    private String userNo;
    /**
     * 名称
     */
    @Excel(name = "员工名称")
    private String userName;
    /**
     * 业务编号
     */
    @Excel(name = "业务编号")
    private String serviceName;
    /**
     * 部门编码
     */
//    @Excel(name = "部门编号")
    private String orgCode;
    /**
     * 部门名称
     */
//    @Excel(name = "部门名称", width = 50)
    private String orgAllName;

    /**
     * 父集菜单
     */
    @Excel(name = "父集菜单", width = 30)
    private String parentName;

    /**
     * 页面名称
     */
    @Excel(name = "页面名称", width = 50)
    private String name;
    /**
     * 访问次数
     */
    @Excel(name = "访问次数")
    private Long accessNum;
    /**
     * 访问次数
     */
    @Excel(name = "累计访问次数", width = 15)
    private Long totNum;
}
