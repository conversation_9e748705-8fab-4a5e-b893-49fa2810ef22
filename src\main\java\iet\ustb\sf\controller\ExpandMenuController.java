package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.service.ExpandMenuService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.controller
 * @title: ExpandMenuController
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/2919:24
 */
@RestController
@RequestMapping("/expandMenu")
@Api(value = "拓展菜单管理", tags = "拓展菜单管理")
public class ExpandMenuController {

    @Autowired
    ExpandMenuService expandMenuService;

    @ResponseBody
    @PostMapping("/doSaveExpandMenu")
    @ApiOperation(value = "保存", notes = "保存,{}", produces = "application/json")
    public AjaxJson doSaveExpandMenu(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(expandMenuService.doSaveExpandMenu(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/doUpdateExpandMenu")
    @ApiOperation(value = "更新", notes = "更新,{}", produces = "application/json")
    public AjaxJson doUpdateExpandMenu(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(expandMenuService.doUpdateExpandMenu(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/findExpandMenuByID")
    @ApiOperation(value = "根据ID查询", notes = "根据ID查询,{}", produces = "application/json")
    public AjaxJson findExpandMenuByID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(expandMenuService.findExpandMenuByID(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/findExpandMenuByUserNoAndRscID")
    @ApiOperation(value = "根据userNo和resourceID查询", notes = "根据userNo和resourceID查询,{}", produces = "application/json")
    public AjaxJson findExpandMenuByUserNoAndRscID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(expandMenuService.findExpandMenuByUserNoAndRscID(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }
}
