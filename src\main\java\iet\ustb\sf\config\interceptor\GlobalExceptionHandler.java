package iet.ustb.sf.config.interceptor;

import iet.ustb.sf.vo.CustomExceptionVo;
import iet.ustb.sf.vo.ErrorResponseVo;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(CustomExceptionVo.class)
    public ResponseEntity<Object> handleCustomException(CustomExceptionVo ex) {
        // 可以返回一个自定义的错误响应对象
        ErrorResponseVo errorResponse = new ErrorResponseVo(false, ex.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    // 如果需要处理其他类型的异常
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleGeneralException(Exception ex) {
        ErrorResponseVo errorResponse = new ErrorResponseVo("500", "Internal Server Error");
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
