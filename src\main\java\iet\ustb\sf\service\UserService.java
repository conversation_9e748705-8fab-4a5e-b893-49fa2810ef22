package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.User;
import iet.ustb.sf.vo.PageVo;
import iet.ustb.sf.vo.UserVo;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.service
 * @title: UserService
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/1011:05
 */
public interface UserService {

    //新增
    String doCreateUser(JSONObject jsonObject);

    //修改
    String doUpdaterUser(JSONObject jsonObject);

    //删除
    void doDeleteUser(JSONObject jsonObject);

    //查询
    Page<User> findAllUser(JSONObject jsonObject);

    User findOneUserByID(JSONObject jsonObject);

    User findOneUserByUserNo(JSONObject jsonObject);

    PageVo<User> filterUserSelected(JSONObject jsonObject);

    String relateRole(JSONObject jsonObject);

    /**
     * 根据用户编号查询用户和组织详细信息
     *
     * @param jsonObject
     * @return
     * <AUTHOR>
     * @create 2022-07-01 10:17
     */
    JSONObject findUserAndOrgByUserNo(JSONObject jsonObject);

    /**
     * 根据组织编号查询该组织下所有用户信息
     *
     * @param jsonObject
     * @return
     * <AUTHOR>
     * @create 2022-07-06 16:48
     */
    JSONArray findUsersByOrgCode(JSONObject jsonObject);


    /**
     * <AUTHOR>
     * @Date 10:19 2022/8/15
     * @Params [jsonObject]
     * @Return java.util.List<iet.ustb.sf.domain.User>
     * @Remark
     **/
    List<User> findUserInSameOrgAndRole(JSONObject jsonObject);

    /**
     * 保存
     * @param User
     * @return
     */
    String doSaveUser(User User);

    /**
     * 按用户编号集合查找用户组织基本信息
     *
     * @param userNos 用户列表
     * @return {@link List }<{@link Map }<{@link String }, {@link String }>>
     * <AUTHOR>
     * @create 2023-04-12
     */
    List<Map<String, String>> findUserOrgByUserNos(Set<String> userNos);


    Map<String , List<UserVo>> findUserListByRoleIDs(JSONObject jsonObject);

    User findUserByUserNo(String userNo);
}
