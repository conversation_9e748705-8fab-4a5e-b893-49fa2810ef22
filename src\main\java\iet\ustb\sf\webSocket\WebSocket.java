package iet.ustb.sf.webSocket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.*;
import iet.ustb.sf.service.ClassQueryService;
import iet.ustb.sf.service.WarningRuleService;
import iet.ustb.sf.utils.SpringContextUtil;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.vo.domain.*;
import lombok.extern.apachecommons.CommonsLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Component
@ServerEndpoint("/ws/{serviceName}/{userNo}")  // 接口路径 ws://localhost:9701/ws/res/000002;
@CommonsLog
public class WebSocket {

    private static ConcurrentHashMap<WebSocket, Object> concurrentHashMap = new ConcurrentHashMap<>(1 << 30);

    private static Set<WebSocket> webSockets = concurrentHashMap.newKeySet();

//    private static CopyOnWriteArraySet<WebSocket> webSockets = new CopyOnWriteArraySet<>();

    /**
     * 用户No -- session map serviceName , userNo , session
     */
    private static Map<String, Map<String, Session>> sessionPool = new HashMap<>();

    @Autowired
    public UserDao userDao;
    @Autowired
    public RoleDao roleDao;
    @Autowired
    public OrgDao orgDao;
    @Autowired
    public WarningEventDao warningEventDao;
    @Autowired
    public WarningEventBodyDao warningEventBodyDao;
    @Autowired
    WarningInfoDao warningInfoDao;
    @Autowired
    WarningRuleService warningRuleService;
    @Autowired
    ClassQueryService classQueryService;

    public Session session;

    /**
     * 链接成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "serviceName") String serviceName, @PathParam(value = "userNo") String userNo) {
//        System.out.println("onOpen:---" + serviceName + "---" + userNo + new Date().toString());
        try {
            this.session = session;
//            this.session.setMaxIdleTimeout(60*60*1000);
            webSockets.add(this);

            Map<String, Session> sessionMap = null;
            if (!ToolsUtil.isEmpty(sessionPool.get(serviceName))) {
                sessionMap = sessionPool.get(serviceName);
            }
            if (ToolsUtil.isEmpty(sessionMap)) {
                sessionMap = new HashMap<>();
            }
            sessionMap.put(userNo, session);
            sessionPool.put(serviceName, sessionMap);

            ExecutorService threadPool = Executors.newCachedThreadPool();
            threadPool.execute(() -> {
                pushOfflineMessage(serviceName, userNo);
            });

//            pushOfflineMessage(serviceName, userNo);

        } catch (Exception e) {
            System.out.println("onOpenException:--------:");
            e.printStackTrace();
            System.out.println("-------------------------");
            onClose(serviceName, userNo);
        }
    }

    /**
     * 链接关闭调用的方法
     */
    @OnClose
    public void onClose(@PathParam(value = "serviceName") String serviceName, @PathParam(value = "userNo") String userNo) {
//        System.out.println("onClose:" + new Date().toString() + "---" + serviceName + "---" + userNo);
        try {
            Map<String, Session> sessionMap = sessionPool.get(serviceName);
            if (ToolsUtil.isEmpty(serviceName)) {
                return;
            }
            sessionMap.remove(userNo);
            webSockets.remove(this);
            // System.out.println("Websocket Closed:" + new Date() + "---" + serviceName + ":" + userNo);
        } catch (Exception e) {
            System.out.println("onCloseExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        } finally {
            webSockets.remove(this);
        }
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message
     */
    @OnMessage
    public void onMessage(String message) {
//        System.out.println("onMessage:" + new Date().toString());
        try {
            if (ToolsUtil.isEmpty(message)) {
                return;
            }
//            System.out.println("message......" + message);
            JSONObject jsonObject = JSONObject.parseObject(message);
            if (ToolsUtil.isEmpty(jsonObject)) {
                return;
            }
            String serviceName = jsonObject.getString("serviceName");
            String userNo = jsonObject.getString("userNo");
            if (ToolsUtil.isEmpty(serviceName) || ToolsUtil.isEmpty(userNo)) {
                return;
            }
            Map<String, Session> sessionMap = sessionPool.get(serviceName);
            if (!ToolsUtil.isEmpty(sessionMap)) {
                Session session = sessionMap.get(userNo);
                if (ToolsUtil.isEmpty(session)) {
                    return;
                }
                if (session.isOpen()) {
                    synchronized (session) {
                        session.getBasicRemote().sendText("Code200:HeartBeatBack");
                    }
                }
            } else {
                return;
            }
        } catch (Exception e) {
            System.out.println("onMessageExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }
    }

    /**
     * 发送错误时的处理
     *
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {

        try{
            for(Map.Entry<String, Map<String, Session>> sessionEntry : sessionPool.entrySet()){
                String serviceName = sessionEntry.getKey();
                Map<String , Session> sessionMap = sessionEntry.getValue();
                if(ToolsUtil.isEmpty(sessionMap)){
                    continue;
                }
                for(Map.Entry<String , Session> sessionEntry1 : sessionMap.entrySet()){
                    String userNo = sessionEntry1.getKey();
                    Session session1 = sessionEntry1.getValue();
                    if(!ToolsUtil.isEmpty(session1) &&
                            !ToolsUtil.isEmpty(session) &&
                            session.equals(session1)){
                        System.out.println("serviceName:" + serviceName + "---" + "UserNo:" + userNo);
                        System.out.println("----------------");
                        break;
                    }
                }
            }
        }catch (Exception e){
            System.out.println("onErrorException-----------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }


        System.out.println("onError--------------------");
        error.printStackTrace();
        System.out.println("---------------------------");
    }


    /**
     * 发消息给所有人
     *
     * @param message
     */
    public void sendAllMessage(String message) {
//        System.out.println("sendAllMessage:" + new Date().toString());
        try {
            for (WebSocket webSocket : webSockets) {
                if (!ToolsUtil.isEmpty(webSocket) &&
                        !ToolsUtil.isEmpty(webSocket.session) &&
                        webSocket.session.isOpen()) {
                    webSocket.session.getAsyncRemote().sendText(message);
                }
            }

        } catch (Exception e) {
            System.out.println("sendAllMessageExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }

    }

    /**
     * 发消息给单个用户
     *
     * @param jsonObject
     */
    public void setMessageToUser(JSONObject jsonObject) {
//        System.out.println("setMessageToUser:" + new Date().toString());
        try {
            showMessage(jsonObject.toJSONString());

            String serviceName = jsonObject.getString("serviceName");
            String userNo = jsonObject.getString("userNo");
            String message = jsonObject.getString("message");
            if (ToolsUtil.isEmpty(jsonObject) || ToolsUtil.isEmpty(userNo) || ToolsUtil.isEmpty(serviceName) || ToolsUtil.isEmpty(message)) {
                return;
            }

            WarningEvent warningEvent = JSONObject.parseObject(message, WarningEvent.class);
            if (ToolsUtil.isEmpty(sessionPool.get(serviceName)) || ToolsUtil.isEmpty(sessionPool.get(serviceName).get(userNo))) {
                saveOfflineMessage(message);
            } else {
                Session session = sessionPool.get(serviceName).get(userNo);
                if (session != null && session.isOpen()) {

                    session.getAsyncRemote().sendText(message);
                    warningEvent.setIsOffline("0");
                    warningEvent.setIsPush("1");
                    warningEventBodyDao.save(warningEvent.getWarningEventBody());
                    warningEventDao.save(warningEvent);

                } else {
                    saveOfflineMessage(message);
                }
            }

        } catch (Exception e) {
            System.out.println("setMessageToUserExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }

    }

    /**
     * 发消息给当前角色下的所有用户
     *
     * @param jsonObject
     */
    public void setMessageToRole(JSONObject jsonObject) {
//        System.out.println("setMessageToRole:" + new Date().toString());
        try {
            showMessage(jsonObject.toJSONString());

            String serviceName = jsonObject.getString("serviceName");
            String roleID = jsonObject.getString("roleID");
            if (ToolsUtil.isEmpty(jsonObject) || ToolsUtil.isEmpty(roleID)) {
                return;
            }

            if(ToolsUtil.isEmpty(roleID)){
                return;
            }

            Role role = roleDao.getById(roleID);
            String message = jsonObject.getString("message");
            if (ToolsUtil.isEmpty(role) && !ToolsUtil.isEmpty(role.getUserList())) {
                return;
            }
            List<User> userList = role.getUserList();

            sendMessageToUsers(serviceName, userList, message);
        } catch (Exception e) {
            System.out.println("setMessageToRoleExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }

    }


    /**
     * 发消息给当前角色下的所有用户(多角色)
     *
     * @param jsonObject
     */
    public String setMessageToRoles(JSONObject jsonObject) {
//        System.out.println("setMessageToRoles:" + new Date().toString());
        try {

            log.warn("setMessageToRoles" + jsonObject.toJSONString() + "\n");
            String serviceName = jsonObject.getString("serviceName");
            List<String> roleIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("roleIDs"), String.class);
            if (ToolsUtil.isEmpty(jsonObject) || ToolsUtil.isEmpty(roleIDs)) {
                return "";
            }
            roleIDs = roleIDs.stream().distinct().collect(Collectors.toList());
            String message = jsonObject.getString("message");
            List<User> userList = null;
            for (String roleID : roleIDs) {
                userList = new ArrayList<>();
                if(ToolsUtil.isEmpty(roleID)){
                    continue;
                }
                Role role = roleDao.getById(roleID);
                if (!ToolsUtil.isEmpty(role) && !ToolsUtil.isEmpty(role.getUserList())) {
                    userList.addAll(role.getUserList());
                }
            }
            userList = userList.stream().distinct().collect(Collectors.toList());
            return sendMessageToUsers(serviceName, userList, message);
        } catch (Exception e) {
            System.out.println("setMessageToRolesExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
            return "";
        }
    }


    public void setMessageToRoles(String serviceName, List<String> roleIDs, String message) {
//        System.out.println("setMessageToRoles:" + new Date().toString());
        try {

            List<User> userList = new ArrayList<>();
            for (String roleID : roleIDs) {
                Role role = roleDao.getById(roleID);
                if (!ToolsUtil.isEmpty(role) && !ToolsUtil.isEmpty(role.getUserList())) {
                    userList.addAll(role.getUserList());
                }
            }
            userList = userList.stream().distinct().collect(Collectors.toList());
            sendMessageToUsers(serviceName, userList, message);
        } catch (Exception e) {
            System.out.println("setMessageToRolesExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }


    }

    public void setMessageToUser(String serviceName, String userNo, String message) {
//        System.out.println("setMessageToUser:" + new Date().toString());
        try {

            List<User> userList = new ArrayList<>();

            sendMessageToUsers(serviceName, userList, message);
        } catch (Exception e) {
            System.out.println("setMessageToUserExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }

    }

    public void setMessageToRoles(String serviceName, String roleIDs, String message) {
//        System.out.println("setMessageToRoles:" + new Date().toString());
        try {

            List<User> userList = new ArrayList<>();

            for (String roleID : roleIDs.split(",")) {
                Role role = roleDao.getById(roleID);
                if (!ToolsUtil.isEmpty(role) && !ToolsUtil.isEmpty(role.getUserList())) {
                    userList.addAll(role.getUserList());
                }
            }
            sendMessageToUsers(serviceName, userList, message);
        } catch (Exception e) {
            System.out.println("setMessageToRolesExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }

    }

    /**
     * 发消息给当前组织下的所有用户
     *
     * @param jsonObject
     */
    public void setMessageToOrg(JSONObject jsonObject) {
//        System.out.println("setMessageToOrg:" + new Date().toString());
        try {

            showMessage(jsonObject.toJSONString());

            String orgCode = jsonObject.getString("orgCode");
            String serviceName = jsonObject.getString("serviceName");
            if (ToolsUtil.isEmpty(jsonObject) || ToolsUtil.isEmpty(orgCode)) {
                return;
            }

            List<User> userList = userDao.findUsersByOrgCode(orgCode);
            String message = jsonObject.getString("message");

            sendMessageToUsers(serviceName, userList, message);
        } catch (Exception e) {
            System.out.println("setMessageToOrgExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }


    }


    public void setMessageToSelectedUsers(JSONObject jsonObject) {
//        System.out.println("setMessageToSelectedUsers:" + new Date().toString());
        try {

            String serviceName = jsonObject.getString("serviceName");
            List<String> userNos = ToolsUtil.jsonObjectToEntityList(jsonObject.get("userNos"), String.class);
            if (ToolsUtil.isEmpty(jsonObject) || ToolsUtil.isEmpty(userNos)) {
                return;
            }
            String message = jsonObject.getString("message");
            sendMessageToUsers(serviceName, userNos, message);
        } catch (Exception e) {
            System.out.println("setMessageToSelectedUsersExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }


    }

    public void setMessageToSameOrgAndRoleUser(JSONObject jsonObject) {
//        System.out.println("setMessageToSameOrgAndRoleUser:" + new Date().toString());
        try {

            String serviceName = jsonObject.getString("serviceName");
            String userNo = jsonObject.getString("userNo");
            String message = jsonObject.getString("message");

            if (ToolsUtil.isEmpty(jsonObject) || ToolsUtil.isEmpty(userNo) || ToolsUtil.isEmpty(serviceName)) {
                return;
            }
            List<User> users = userDao.findUserInSameOrgAndRole(userNo);
            List<String> userNos = new ArrayList<>(users.size() + 1);
            if (!ToolsUtil.isEmpty(users)) {
                for (User user : users) {
                    userNos.add(user.getUserNo());
                }
            }
            sendMessageToUsers(serviceName, userNos, message);
        } catch (Exception e) {
            System.out.println("setMessageToSameOrgAndRoleUserExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }

    }


    public String sendMessageToUsers(String serviceName, List<User> userList, String message) {
//        System.out.println("sendMessageToUsers:" + new Date().toString());
        try {
            List<WarningEvent> warningEventList = new ArrayList<>(userList.size() + 1);
            List<WarningEventBody> warningEventBodyList = new ArrayList<>(userList.size() + 1);
            Map<String, Session> sessionMap = null;

            if(!ToolsUtil.isEmpty(sessionPool) && !ToolsUtil.isEmpty(sessionPool.get(serviceName))){
                sessionMap = sessionPool.get(serviceName);
            }

            WarningEvent warningEvent;
            WarningEventBody warningEventBody;

            WarningEvent warningEventT = JSONObject.parseObject(message, WarningEvent.class);
            WarningEventBody warningEventBodyT = warningEventT.getWarningEventBody();

            String messageID = "";
            String messageInfo = warningEventBodyT.getMsg();


            String warningRuleID = warningEventT.getWarningRuleID();

            if(0 == warningRuleService.checkRuleExist(warningRuleID)){
                return "";
            }

            if(0 == warningRuleService.checkPushMode(warningRuleID)){
                return "";
            }


            Map<String , String> result = classQueryService.queryClasses(warningEventT.getCreateDateTime());


            Session session;
            for (User user : userList) {
                warningEvent = new WarningEvent();
                warningEventBody = new WarningEventBody();
                warningEvent = JSONObject.parseObject(message, WarningEvent.class);
                warningEventBody = warningEvent.getWarningEventBody();

                warningEvent.setId(UUID.randomUUID().toString());

                warningEvent.setClasses(result.get("classes"));
                warningEvent.setTeam(result.get("team"));

                messageID = warningEvent.getMsgID();

                if (!ToolsUtil.isEmpty(sessionMap)) {
                    session = sessionMap.get(user.getUserNo());
                    if (session != null && session.isOpen()) {
                        try{
                            session.getAsyncRemote().sendText(message);
                        }catch (Exception e){

                        }
                        warningEvent.setIsOffline("0");
                        warningEvent.setIsPush("1");
                    }
                } else {
                    warningEvent.setIsOffline("1");
                    warningEvent.setIsPush("0");
                }
                warningEvent.setUserNo(user.getUserNo());

                warningEventBody.setId(UUID.randomUUID().toString());

                warningEventList.add(warningEvent);
                warningEventBodyList.add(warningEventBody);
            }

            if(ToolsUtil.isEmpty(warningEventT.getWarningType()) || 5 != warningEventT.getWarningType()){
                warningEventBodyDao.saveAll(warningEventBodyList);
                warningEventDao.saveAll(warningEventList);
            }

            log.warn(JSON.toJSONString(warningEventBodyList));
            log.warn(JSON.toJSONString(warningEventList));

//            if((!ToolsUtil.isEmpty(warningEventT.getWarningType()) && 5 == warningEventT.getWarningType())
//                    && (!ToolsUtil.isEmpty(warningEventT.getServiceName()) && "ems".equals(warningEventT.getServiceName()))){
//                new Thread(new Runnable() {
//                    @Override
//                    public void run() {
//                        if(!ToolsUtil.isEmpty(messageInfo)){
//                            weComMessagePushService.sendToUsers(userList , messageInfo);
//                        }
//                    }
//                }).start();
//            }

            return messageID;
        } catch (Exception e) {
            log.warn(e.getMessage());
            return "";
        }
    }


    public void sendMessageToUsers(String serviceName, List<String> userNos, String message, String... arg) {
//        System.out.println("sendMessageToUsers:" + new Date().toString());
        try {
            List<WarningEvent> warningEventList = new ArrayList<>(userNos.size() + 1);
            List<WarningEventBody> warningEventBodyList = new ArrayList<>(userNos.size() + 1);
            Map<String, Session> sessionMap = sessionPool.get(serviceName);

            WarningEvent warningEvent = new WarningEvent();
            WarningEventBody warningEventBody = new WarningEventBody();
            Session session;
            for (String userNo : userNos) {
                warningEvent = JSONObject.parseObject(message, WarningEvent.class);
                if (!ToolsUtil.isEmpty(sessionMap)) {
                    session = sessionMap.get(userNo);
                    if (session != null && session.isOpen()) {
                        session.getAsyncRemote().sendText(message);
                        warningEvent.setIsOffline("0");
                        warningEvent.setIsPush("1");
                    }
                } else {
                    warningEvent.setIsOffline("1");
                    warningEvent.setIsPush("0");
                }
                warningEvent.setUserNo(userNo);
                warningEventBody = warningEvent.getWarningEventBody();
                warningEventBody.setId(UUID.randomUUID().toString());
                warningEventList.add(warningEvent);
                warningEventBodyList.add(warningEventBody);
            }
//            weComMessagePushService.sendToUsersByUserNo(userNos , warningEventBody.getMsg());
            warningEventBodyDao.saveAll(warningEventBodyList);
            warningEventDao.saveAll(warningEventList);
        } catch (Exception e) {
            System.out.println("sendMessageToUsersExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }
    }

    public void saveOfflineMessage(String message) {
//        System.out.println("saveOfflineMessage:" + new Date().toString());
        try {

            WarningEvent warningEvent = JSONObject.parseObject(message, WarningEvent.class);
            warningEvent.setIsOffline("1");
            warningEvent.setIsPush("0");
            WarningEventBody warningEventBody = warningEvent.getWarningEventBody();
            warningEventBodyDao.save(warningEventBody);
            warningEventDao.save(warningEvent);
        } catch (Exception e) {
            System.out.println("saveOfflineMessageExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }


    }

    public List<WarningEvent> getOfflineWaringEvent(String userNo, String serviceName) {
//        System.out.println("getOfflineWaringEvent:" + new Date().toString());
        try {
            if (ToolsUtil.isEmpty(warningEventDao)) {
                warningEventDao = SpringContextUtil.getBean(WarningEventDao.class);
            }
            return warningEventDao.getOfflineWaringEvent(userNo, serviceName);
        } catch (Exception e) {
            System.out.println("getOfflineWaringEventExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
            return new ArrayList<>();
        }

    }


    public void pushOfflineMessage(String serviceName, String userNo) {
//        System.out.println("pushOfflineMessage:" + new Date().toString());
        try {
            if (!ToolsUtil.isEmpty(serviceName) && !ToolsUtil.isEmpty(userNo)) {
                List<WarningEvent> warningEvents = getOfflineWaringEvent(userNo, serviceName);
                if (ToolsUtil.isEmpty(sessionPool.get(serviceName)) || ToolsUtil.isEmpty(sessionPool.get(serviceName).get(userNo))) {
                    return;
                }
                Session session = sessionPool.get(serviceName).get(userNo);

                for (WarningEvent warningEvent : warningEvents) {
                    if (session != null && session.isOpen()) {
                        session.getAsyncRemote().sendText(JSON.toJSONString(warningEvent));
                        warningEvent.setIsPush("1");
                        warningEventDao.save(warningEvent);
                    }
                }
            } else {
                return;
            }
        } catch (Exception e) {
            System.out.println("pushOfflineMessageExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }
    }

    /**
     * <AUTHOR>
     * @Date 15:28 2022/8/24
     * @Params [userNo]
     * @Return boolean
     * @Remark
     **/
    public boolean checkUserIsOnline(String serviceName, String userNo) {
//        System.out.println("checkUserIsOnline:" + new Date().toString());
        try {
            if (ToolsUtil.isEmpty(userNo)) {
                return false;
            }
            Map<String, Session> stringSessionMap = sessionPool.get(serviceName);
            if (ToolsUtil.isEmpty(stringSessionMap)) {
                return false;
            }
            Session session = stringSessionMap.get(userNo);
            if (session != null && session.isOpen()) {
                return true;
            }
            return false;
        } catch (Exception e) {
            System.out.println("checkUserIsOnlineExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
            return false;
        }


    }


    public void showMessage(String message) {
        // System.out.println("showMessage:" + new Date().toString());
        // System.out.println("Date:" + new Date() + "----------------");
        // System.out.println("Print Message:" + message + "\n\n\n");
    }


    public void notifyUser(String serviceName, String userNo, String message) {
//        System.out.println("notifyUser:" + new Date().toString());
        try {
            if (ToolsUtil.isEmpty(userNo)) {
                return;
            }
            Map<String, Session> sessionMap = sessionPool.get(serviceName);
            Session session;
            if (!ToolsUtil.isEmpty(sessionMap)) {
                session = sessionMap.get(userNo);
                if (session != null && session.isOpen()) {
                    session.getAsyncRemote().sendText(message);
                }
            }
        } catch (Exception e) {
            System.out.println("notifyUserExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }
    }


    public void notifyUser(String serviceName, List<String> userNos, String message) {
//        System.out.println("notifyUser:" + new Date().toString());
        try {
            for (String userNo : userNos) {
                if (ToolsUtil.isEmpty(userNo)) {
                    return;
                }
                Map<String, Session> sessionMap = sessionPool.get(serviceName);
                Session session;
                if (!ToolsUtil.isEmpty(sessionMap)) {
                    session = sessionMap.get(userNo);
                    if (session != null && session.isOpen()) {
                        session.getAsyncRemote().sendText(message);
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("notifyUserExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }
    }


    public void notifyUsers(String serviceName, List<User> userList, String message) {
//        System.out.println("notifyUsers:" + new Date().toString());
        try {
            Map<String, Session> sessionMap = sessionPool.get(serviceName);
            Session session;
            for (User user : userList) {
                if (!ToolsUtil.isEmpty(sessionMap)) {
                    session = sessionMap.get(user.getUserNo());
                    if (session != null && session.isOpen()) {
                        session.getAsyncRemote().sendText(message);

                    }
                }
            }
        } catch (Exception e) {
            System.out.println("notifyUsersExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }
    }


    public void notifyRoles(String serviceName, String roleIDs, String message) {
//        System.out.println("notifyRoles:" + new Date().toString());
        try {
            List<User> userList = new ArrayList<>();
            for (String roleID : roleIDs.split(",")) {
                Role role = roleDao.getById(roleID);
                if (!ToolsUtil.isEmpty(role) && !ToolsUtil.isEmpty(role.getUserList())) {
                    userList.addAll(role.getUserList());
                }
            }
            notifyUsers(serviceName, userList, message);
        } catch (Exception e) {
            System.out.println("notifyRolesExpection---------");
            e.printStackTrace();
            System.out.println("---------------------------");
        }
    }


    public void finishAlert(JSONObject jsonObject){

        System.out.println("finishAlert..." + jsonObject.toJSONString());

        if (!ToolsUtil.isEmpty(jsonObject) && !ToolsUtil.isEmpty(jsonObject.get("msgID"))) {
            String msgID = jsonObject.getString("msgID");
            //额外用户
            String userNos = jsonObject.getString("userNos");
            List<String> userList = new ArrayList<>();
            if(!ToolsUtil.isEmpty(userNos)){
                userList = Arrays.asList(userNos.split(","));
            }
            List<WarningEvent> warningEvents = warningEventDao.findAlertsByMsgID(msgID);
            if(ToolsUtil.isEmpty(warningEvents)){
                return;
            }
            String serviceName = warningEvents.get(0).getServiceName();

            for(WarningEvent warningEvent : warningEvents){
                String userNo = warningEvent.getUserNo();
                if(!checkUserIsOnline(serviceName , userNo)){
                    warningEvent.setIsPush("0");
                    warningEvent.setIsOffline("1");
                }else{
                    warningEvent.setIsPush("1");
                    warningEvent.setIsOffline("0");
                }
                warningEvent.setStatus("1");
                WarningEventBody warningEventBody = warningEvent.getWarningEventBody();
                warningEventBodyDao.save(warningEventBody);
                warningEventDao.save(warningEvent).getId();
            }

            String message = "报警问题已解决";
            if(!ToolsUtil.isEmpty(warningEvents.get(0).getWarningEventBody().getPushRoleIDs())){
                notifyRoles(serviceName, warningEvents.get(0).getWarningEventBody().getPushRoleIDs(), message);
            }
            if(!ToolsUtil.isEmpty(userList)){
                notifyUser(serviceName , userList , message);
            }

            if(!ToolsUtil.isEmpty("warningInfo")){
                WarningInfo warningInfo = JSONObject.parseObject(jsonObject.getString("warningInfo") , WarningInfo.class);
                List<WarningInfo> warningInfoList = warningInfoDao.findWariningByMsgID(msgID);
                if(!ToolsUtil.isEmpty(warningInfoList)){
                    for(WarningInfo item : warningInfoList){
                        item.setStatus(2);
                        warningInfoDao.save(item);
                    }
                }
                updateWarningInfo(warningInfo);
            }
        }
    }

    public void finishAlertTmp(JSONObject jsonObject){

        System.out.println("finishAlertTmp..." + jsonObject.toJSONString());

        if (!ToolsUtil.isEmpty(jsonObject) && !ToolsUtil.isEmpty(jsonObject.get("msgID"))) {
            String msgID = jsonObject.getString("msgID");
            //额外用户
            String userNos = jsonObject.getString("userNos");
            List<String> userList = new ArrayList<>();
            if(!ToolsUtil.isEmpty(userNos)){
                userList = Arrays.asList(userNos.split(","));
            }
            List<WarningEvent> warningEvents = warningEventDao.findAlertsByMsgID(msgID);
            if(ToolsUtil.isEmpty(warningEvents)){
                return;
            }
            String serviceName = warningEvents.get(0).getServiceName();

            for(WarningEvent warningEvent : warningEvents){
                String userNo = warningEvent.getUserNo();
                if(!checkUserIsOnline(serviceName , userNo)){
                    warningEvent.setIsPush("0");
                    warningEvent.setIsOffline("1");
                }else{
                    warningEvent.setIsPush("1");
                    warningEvent.setIsOffline("0");
                }
                warningEvent.setStatus("9");
                WarningEventBody warningEventBody = warningEvent.getWarningEventBody();
                warningEventBodyDao.save(warningEventBody);
                warningEventDao.save(warningEvent).getId();
            }

            String message = "报警问题已解决";
            if(!ToolsUtil.isEmpty(warningEvents.get(0).getWarningEventBody().getPushRoleIDs())){
                notifyRoles(serviceName, warningEvents.get(0).getWarningEventBody().getPushRoleIDs(), message);
            }
            if(!ToolsUtil.isEmpty(userList)){
                notifyUser(serviceName , userList , message);
            }

            if(!ToolsUtil.isEmpty("warningInfo")){
                WarningInfo warningInfo = JSONObject.parseObject(jsonObject.getString("warningInfo") , WarningInfo.class);
                List<WarningInfo> warningInfoList = warningInfoDao.findWariningByMsgID(msgID);
                if(!ToolsUtil.isEmpty(warningInfoList)){
                    for(WarningInfo item : warningInfoList){
                        item.setStatus(9);
                        warningInfoDao.save(item);
                    }
                }
                updateWarningInfo(warningInfo);
            }
        }
    }

    public void updateWarningInfo(WarningInfo warningInfo){
        warningInfoDao.save(warningInfo);
    }

}
