//package iet.ustb.sf.service;
//
//import com.alibaba.fastjson.JSONObject;
//import iet.ustb.sf.domain.User;
//
//import java.util.List;
//
///**
// * @Author: Dr.Monster
// * @Title: WeComMessagePushService
// * @Date: 23/08/21 14:3209
// * @Slogan: The never-compromising angry youth
// * @Remark:$
// */
//public interface WeComMessagePushService {
//
//    String getAccessToken();
//    void sendMessage(JSONObject jsonObject);
//    void sendToUsersByTextMessage(JSONObject jsonObject);
//
//    void sendToRolesByTextMessage(JSONObject jsonObject);
//    void sendToUsers(List<User> userList , String message);
//    void sendToUsersByUserNosByTextMessage(List<String> userNos , String message);
//
//    void sendToUsersByUserNosByTextCard(List<String> userNos);
//    void sendToRoles(JSONObject jsonObject);
//    void sendToTags(JSONObject jsonObject);
//    void sendKbToUsers();
//}
