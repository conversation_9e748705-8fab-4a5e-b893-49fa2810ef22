package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.Comment;
import iet.ustb.sf.service.CommentService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 问题反馈评论
 *
 * <AUTHOR>
 * @create 2022-10-26
 */
@RestController
@RequestMapping("/comment")
@Api(value = "问题反馈评论", tags = "问题反馈评论")
public class CommentController {

    @Autowired
    private CommentService commentService;

    @Autowired

    @PostMapping("/findAll")
    @ApiOperation(value = "查询所有", notes = "查询所有")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<Comment> commentList = commentService.findAll();
            ajaxJson.setData(commentList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findPageByMultiCondition")
    @ApiOperation(value = "按多条件查找分页", notes = "入参：{\"title\": \"文字乱码\"}")
    public AjaxJson findPageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<Comment> commentPage = commentService.findPageByMultiCondition(jsonObject);
            ajaxJson.setData(commentPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findTreeByFeedBackId")
    @ApiOperation(value = "通过问题反馈id查找树", notes = "入参：{\"feedBackId\": \"134123413\"}")
    public AjaxJson findTreeByFeedBackId(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<Comment> commentPage = commentService.findTreeByFeedBackId(jsonObject);
            ajaxJson.setData(commentPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存", notes = "保存")
    public AjaxJson save(@RequestBody Comment comment) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            commentService.save(comment);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

}
