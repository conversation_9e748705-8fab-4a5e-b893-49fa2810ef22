package iet.ustb.sf.vo.domain;

import lombok.Data;
import org.hibernate.annotations.Proxy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.domain
 * @title: DashConfig
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/2710:49
 */
@Data
@Entity
@Table(name = "DashConfig")
@Proxy(lazy = false)
public class DashConfig extends BaseEntity {

    //userID
    @Column
    String userNo;

    //配置
    @Column(columnDefinition = "MediumText")
    String config;
}
