package iet.ustb.sf.vo.domain;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 附件
 *
 * <AUTHOR>
 * @create 2022-10-27
 * @see BaseEntity
 */
@Data
@Entity
@Table(name = "attach")
public class Attach extends BaseEntity {

    /**
     * 业务编号
     */
    private String serviceNo;
    /**
     * 附件名称
     */
    private String name;
    /**
     * 附件路径
     */
    private String path;
    /**
     * 附件大小(单位KB)，MultipartFile.getSize()方法获取的大小的单位是B，所有此处得除以1024
     */
    private Double fileSize;
    /**
     * 附件类型
     */
    private String fileType;
    /**
     * 附件描述
     */
    private String description;
    /**
     * 附件所属业务类型
     */
    private String relatedType;
    /**
     * 和业务关联的业务ID
     */
    private String relatedId;
}
