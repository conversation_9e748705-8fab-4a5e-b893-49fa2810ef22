package iet.ustb.sf.vo.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;

/**
 * 上线进度管理
 *
 * <AUTHOR>
 * @create 2023-02-13
 * @see BaseEntity
 */
@Data
@Entity
@Table(name = "ONLINE_MGT")
@ApiModel(value = "上线进度管理")
public class OnlineMgt extends BaseEntity {

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    private Integer module;
    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private String parentId;
    /**
     * 功能开发
     */
    @ApiModelProperty(value = "功能开发")
    private Integer functionDev;
    /**
     * 内部测试
     */
    @ApiModelProperty(value = "内部测试")
    private Integer internalTest;
    /**
     * 外部试用
     */
    @ApiModelProperty(value = "外部试用")
    private Integer externalTrial;
    /**
     * 功能培训
     */
    @ApiModelProperty(value = "功能培训")
    private Integer functionTrain;
    /**
     * 功能上线
     */
    @ApiModelProperty(value = "功能上线")
    private Integer functionOnline;
    /**
     * 综合评分
     */
    @ApiModelProperty(value = "综合评分")
    private Integer comprehensiveScore;
    /**
     * 甘特数据
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "ganttData", columnDefinition = "BLOB")
    @ApiModelProperty(value = "甘特数据")
    private String ganttData;

}
