package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.*;
import iet.ustb.sf.service.MasterDataService;
import iet.ustb.sf.utils.XMLUtil;
import iet.ustb.sf.vo.domain.*;
import lombok.extern.apachecommons.CommonsLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@CommonsLog
public class MasterDataServiceImpl implements MasterDataService {
    /**测试地址*/
//    private static final String WSDL_URL = "http://172.18.248.232/cwbase/ser/vice/zjgl/GetDatasFromMDM.asmx?wsdl";
    /**
     * 正式地址
     */
    private static final String WSDL_URL = "http://zsj.nisco.cn/cwbase/service/zjgl/GetDatasFromMDM.asmx?wsdl";
    /**
     * 命名空间
     */
    private static final String NAME_SPACE = "http://tempuri.org/";
    /**
     * 获取组织方法
     */
    private static final String ORG_METHOD = "GetOrgInfoFromMDM";
    /**
     * 获取用户方法
     */
    private static final String USER_METHOD = "GetUserInfoFromMDM";
    /**
     * 获取岗位方法
     */
    private static final String POST_METHOD = "GetJobTypeInfoFromMDM";
    /**
     * 获取用户方法
     */
    private static final String POST_LEVEL_METHOD = "GetRankInfoFromMDM";

    @Autowired
    private OrgDao orgDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private UserPostDao userPostDao;
    @Autowired
    private UserPostLevelDao userPostLevelDao;
    @Autowired
    private PostDao postDao;
    @Autowired
    private PostLevelDao postLevelDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncMaster(String startDate, String endDate) {
        JSONArray jsonArray;
        // 获取入参XML
        String inXml = XMLUtil.getInXml(startDate, endDate);

        // 同步组织
        jsonArray = getMasterDataByWsdl(inXml, ORG_METHOD);
        saveByMethod(jsonArray, ORG_METHOD);

        // 同步用户
        jsonArray = getMasterDataByWsdl(inXml, USER_METHOD);
        saveByMethod(jsonArray, USER_METHOD);

        // 同步岗位
        jsonArray = getMasterDataByWsdl(inXml, POST_METHOD);
        saveByMethod(jsonArray, POST_METHOD);

        // 同步岗级
        jsonArray = getMasterDataByWsdl(inXml, POST_LEVEL_METHOD);
        saveByMethod(jsonArray, POST_LEVEL_METHOD);

//        log.info("execution complete!!!");
    }

    /**
     * 调用webservice接口获取主数据
     *
     * @param inXml
     * @param localPart
     */
    @Transactional(rollbackFor = Exception.class)
    public JSONArray getMasterDataByWsdl(String inXml, String localPart) {
        // 调用webservice接口
        Object[] obj = XMLUtil.callXmlService(WSDL_URL, NAME_SPACE, localPart, inXml);

        // 获取json字符串
        String jsonStr = XMLUtil.assemblyXml(obj);
//        log.info("主数据出参：" + jsonStr);

        // 若出参为空,或json对象,则封装为json数组
        if ("".equals(jsonStr) || jsonStr.startsWith("{")) {
            jsonStr = "[" + jsonStr + "]";
        }
        return JSONArray.parseArray(jsonStr);
    }


    /**
     * 根据webservice方法判断写入数据
     *
     * @param jsonArray
     * @param method
     */
    private void saveByMethod(JSONArray jsonArray, String method) {
        if (jsonArray.isEmpty()) {
//            log.info(method + "--> No Data!!!");
            return;
        }
        jsonArray.forEach(obj -> {
            JSONObject jsonObj = (JSONObject) obj;
            if (ORG_METHOD.equals(method)) {// 组织
                saveOrg(jsonObj);
            }
            if (USER_METHOD.equals(method)) {// 用户
                saveUserAndPostAndLevel(jsonObj);
            }
            if (POST_METHOD.equals(method)) {// 岗位
                savePost(jsonObj);
            }
            if (POST_LEVEL_METHOD.equals(method)) {// 岗级
                savePostLevel(jsonObj);
            }
        });
    }

    /**
     * 保存组织
     *
     * @param json
     */
    private void saveOrg(JSONObject json) {
        Org org = new Org();
        org.setId(json.getString("NM"));
        String orgCode = json.getString("ORGCODE");
        Org orgVo = orgDao.findByOrgCode(orgCode);
        if (orgVo != null && !orgVo.getId().equals(org.getId())) {
            orgDao.deleteById(orgVo.getId());
        } else {
            org.setOrgCode(orgCode);
            org.setOrgName(json.getString("ORGCODE"));
            org.setParentOrgCode(json.getString("PNODECODE"));
            org.setOrgType(json.getString("USERPREDEF_11"));
            org.setOrgAllName(json.getString("ORAALLNAME"));
            org.setOrgCurCode(json.getString("USERPREDEF_12"));
            org.setStatus(json.getString("ISHISTORY"));
            org.setOrgDesc(json.getString("ORGDESC"));
            org.setFax(json.getString("FAX"));
            org.setWebAddr(json.getString("WEBADDR"));
            org.setOrgLegalPerson(json.getString("ORGFRDB"));
            org.setIsLegalPerson(json.getString("USERPREDEF_26"));
            org.setPostCode(json.getString("POSTCODE"));
            org.setRemarks(json.getString("REMARKS"));
            org.setOperStus(json.getString("USERPREDEF_14"));
            org.setErpSpecField(json.getString("USERPREDEF_16"));
            org.setAccOrgCode(json.getString("USERPREDEF_17"));
            org.setAccSysCode(json.getString("USERPREDEF_18"));
            org.setRepeatField(json.getString("USERPREDEF_13"));
        }

        Org resOrg = orgDao.save(org);
//        log.info("resOrg = " + resOrg);
    }


    /**
     * 保存用户、岗位、岗级
     *
     * @param jsonObj
     */
    public void saveUserAndPostAndLevel(JSONObject jsonObj) {

        saveUser(jsonObj);// 保存用户

        // 用户组织岗位
        JSONObject userPostJsonObj = jsonObj.getJSONObject("O_CHILDS1");
        Object obj = userPostJsonObj.get("O_CHILD");
        if (obj instanceof JSONArray) {// 当json数组
            JSONArray userPostJsonArray = userPostJsonObj.getJSONArray("O_CHILD");
            userPostJsonArray.forEach(postObj -> {
                JSONObject json = (JSONObject) postObj;
                saveUserPost(json);// 保存用户岗位
            });
        } else if (obj instanceof JSONObject) {// 当json对象
            JSONObject json = userPostJsonObj.getJSONObject("O_CHILD");
            saveUserPost(json);// 保存用户岗位
        }

        // 用户岗级
        JSONObject userPostLevJsonObj = jsonObj.getJSONObject("O_CHILDS2");
        obj = userPostLevJsonObj.get("O_CHILD");

        if (obj instanceof JSONArray) {// 当json数组
            JSONArray userPostLevJsonArray = userPostLevJsonObj.getJSONArray("O_CHILD");
            userPostLevJsonArray.forEach(postLevObj -> {
                JSONObject json = (JSONObject) postLevObj;
                saveUserPostLevel(json);// 保存用户岗级
            });
        } else if (obj instanceof JSONObject) {// 当json对象
            JSONObject json = userPostLevJsonObj.getJSONObject("O_CHILD");
            saveUserPostLevel(json);// 保存用户岗级
        }
    }

    /**
     * 保存用户
     *
     * @param json
     */
    private void saveUser(JSONObject json) {
        User user = new User();
        user.setId(json.getString("MDMZGZD_NM"));
        String orgId = json.getString("MDMZGZD_HRDWNM");
        Optional<Org> org = orgDao.findById(orgId);
        user.setOrgCode(org.isPresent() ? org.get().getOrgCode() : "");
        user.setUserNo(json.getString("MDMZGZD_ZGBH"));
        user.setUserName(json.getString("MDMZGZD_ZGXM"));
        user.setSex(json.getString("MDMZGZD_ZGXB"));
        user.setMobPhone(json.getString("MDMZGZD_MOBILE"));
        user.setCerType(json.getString("USERPREDEF_16"));
        user.setIdNum(json.getString("USERPREDEF_21"));
        user.setLoginId(json.getString("MDMZGZD_ZH"));
        user.setBirthDay(json.getString("MDMZGZD_CSRQ"));
        user.setNationality(json.getString("USERPREDEF_17"));
        user.setEmail(json.getString("MDMZGZD_EMAIL"));
        user.setFolk(json.getString("USERPREDEF_18"));
        user.setPoliticsName(json.getString("USERPREDEF_19"));
        user.setAcademicDeg(json.getString("USERPREDEF_20"));
        user.setWechatNo(json.getString("MDMZGZD_WECHAT"));
        user.setTel(json.getString("MDMZGZD_TEL"));
        user.setRemarks(json.getString("MDMZGZD_NOTE"));
        user.setStatus(json.getString("MDMZGZD_TYBZ"));
        user.setOperStus(json.getString("USERPREDEF_3"));
        user.setWorkStatus(json.getString("USERPREDEF_4"));
        user.setJobNo(json.getString("USERPREDEF_6"));
        user.setEmpCategory(json.getString("USERPREDEF_7"));
        user.setPayCompId(json.getString("USERPREDEF_8"));
        user.setRepeatField(json.getString("USERPREDEF_5"));

        User resUser = userDao.save(user);
//        log.info("resUser = " + resUser);
    }

    /**
     * 保存用户岗位
     *
     * @param json
     */
    private void saveUserPost(JSONObject json) {
        UserPost userPost = new UserPost();
        userPost.setId(json.getString("MDMRYZZGW_V4_GUID"));
        userPost.setUserId(json.getString("MDMRYZZGW_V4_ZBNM"));
        userPost.setOrgCode(json.getString("MDMRYZZGW_V4_UDEF1"));
        userPost.setPostCode(json.getString("MDMRYZZGW_V4_UDEF3"));
        userPost.setIsMainPost(json.getString("MDMRYZZGW_V4_UDEF5"));
        userPost.setMainPostCode(json.getString("MDMRYZZGW_V4_UDEF6"));
        userPost.setStatus(json.getString("MDMRYZZGW_V4_UDEF7"));
        userPost.setOperStus(json.getString("MDMRYZZGW_V4_UDEF8"));
        userPost.setRepeatField(json.getString("MDMRYZZGW_V4_PCXMZY"));

        UserPost resUserPost = userPostDao.save(userPost);
//        log.info("resUserPost = " +resUserPost);
    }

    /**
     * 保存用户岗级
     *
     * @param json
     */
    private void saveUserPostLevel(JSONObject json) {
        UserPostLevel userPostLevel = new UserPostLevel();
        userPostLevel.setId(json.getString("MDMRYGJ_V4_GUID"));
        userPostLevel.setUserId(json.getString("MDMRYGJ_V4_ZBNM"));
        userPostLevel.setOrgCode(json.getString("MDMRYGJ_V4_UDEF1"));
        userPostLevel.setDefaultOrgCode(json.getString("MDMRYGJ_V4_UDEF2"));
        userPostLevel.setPostLevelCode(json.getString("MDMRYGJ_V4_UDEF3"));
        userPostLevel.setStatus(json.getString("MDMRYGJ_V4_UDEF5"));
        userPostLevel.setOperStus(json.getString("MDMRYGJ_V4_UDEF6"));
        userPostLevel.setRepeatField(json.getString("MDMRYGJ_V4_PCXMZY"));

        UserPostLevel resUserLevelPost = userPostLevelDao.save(userPostLevel);
//        log.info("resUserLevelPost = " +resUserLevelPost);
    }

    /**
     * 保存岗位
     *
     * @param json
     */
    private void savePost(JSONObject json) {
        Post post = new Post();
        post.setId(json.getString("MDMGW_V4_GUID"));
        post.setPostCode(json.getString("MDMGW_V4_UDEF1"));
        post.setPostName(json.getString("MDMGW_V4_UDEF2"));
        post.setPostType(json.getString("MDMGW_V4_UDEF3"));
        post.setPostDesc(json.getString("MDMGW_V4_UDEF4"));
        post.setStatus(json.getString("MDMGW_V4_UDEF5"));
        post.setIsHR(json.getString("MDMGW_V4_UDEF6"));
        post.setOperStus(json.getString("MDMGW_V4_UDEF7"));
        post.setRepeatField(json.getString("MDMGW_V4_PCXMZY"));

        Post resPost = postDao.save(post);
//        log.info("resPost = " +resPost);
    }

    /**
     * 保存岗级
     *
     * @param json
     */
    private void savePostLevel(JSONObject json) {
        PostLevel postLevel = new PostLevel();
        postLevel.setId(json.getString("MDMGJ_V4_GUID"));
        postLevel.setPostLevelCode(json.getString("MDMGJ_V4_UDEF1"));
        postLevel.setPostLevelName(json.getString("MDMGJ_V4_UDEF2"));
        postLevel.setRanLev(json.getString("MDMGJ_V4_UDEF3"));
        postLevel.setPostLevelDesc(json.getString("MDMGJ_V4_UDEF4"));
        postLevel.setPostLevelType(json.getString("MDMGJ_V4_UDEF7"));
        postLevel.setStatus(json.getString("MDMGJ_V4_UDEF5"));
        postLevel.setOperStus(json.getString("MDMGJ_V4_UDEF6"));
        postLevel.setRepeatField(json.getString("MDMGJ_V4_PCXMZY"));

        PostLevel resPostLevel = postLevelDao.save(postLevel);
//        log.info("resPostLevel = " +resPostLevel);
    }

}
