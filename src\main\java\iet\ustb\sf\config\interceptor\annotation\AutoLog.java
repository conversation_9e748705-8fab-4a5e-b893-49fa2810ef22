package iet.ustb.sf.config.interceptor.annotation;

import iet.ustb.sf.utils.constant.EnumConstant;

import java.lang.annotation.*;

/**
 * 系统日志注解
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AutoLog {

    /**
     * 日志内容
     *
     * @return
     */
    String value() default "";

    /**
     * 日志类型
     *
     * @return 0:操作日志;33
     */
    int logType() default 0;

    /**
     * 操作日志类型
     *
     * @return 1.普通 2.调用 3.被调 4.自定义
     */
    int operateType() default 1;

}
