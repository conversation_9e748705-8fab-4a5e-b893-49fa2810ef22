package iet.ustb.sf.vo.domain;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 协调事宜
 *
 * <AUTHOR>
 * @create 2022-12-16
 * @see BaseEntity
 */
@Data
@Entity
@Table(name = "COORDINATE_MATTER")
@ApiModel(value = "协调事宜")
public class CoordinateMatter extends BaseEntity {

    /**
     * 协调事项
     */
    @Excel(name = "协调事项")
    @Column(nullable = false, length = 1000)
    @ApiModelProperty(value = "协调事项")
    private String matter;

    /**
     * 事项类型 ：1-协调事项 2-会议要求 3-遗留事项 4-风险事项
     */
    @Excel(name = "类别",replace = {"1_协调事项","2_会议要求","3_遗留事项","4_风险事项"})
    @Column(nullable = false, length = 2)
    @ApiModelProperty(value = "事项类型 ：1-协调事项 2-会议要求 3-遗留事项 4-风险事项")
    private Integer matterType;

    /**
     * 提出单位
     */
    @Excel(name = "提出单位")
    @Column(length = 64)
    @ApiModelProperty(value = "提出单位")
    private String presenterUnit;

    /**
     * 提出人
     */
    @Excel(name = "提出人")
    @Column(nullable = false, length = 64)
    @ApiModelProperty(value = "提出人")
    private String presenter;

    /**
     * 责任单位
     */
    @Excel(name = "责任单位")
    @Column(length = 64)
    @ApiModelProperty(value = "责任单位")
    private String handleUnit;

    /**
     * 责任人
     */
    @Excel(name = "责任人")
    @Column(length = 64)
    @ApiModelProperty(value = "责任人")
    private String handlePerson;

    /**
     * 处理结果
     */
    @Excel(name = "处理结果")
    @Column(length = 1000)
    @ApiModelProperty(value = "处理结果")
    private String handleResults;

    /**
     * 计划完成日期
     */
    @Excel(name = "计划完成日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划完成日期")
    private Date planCompleteDate;

    /**
     * 任务日期
     */
    @Excel(name = "任务日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    @ApiModelProperty(value = "任务日期")
    private Date taskDate;

    /**
     * 处理状态：1-是 2-否 3-进行中
     */
    @Excel(name = "处理状态",replace = {"1-是","2-否","3-进行中"})
    @Column(length = 2)
    @ApiModelProperty(value = "处理状态：1-是 2-否 3-进行中")
    private Integer handleStatus;

    /**
     * 优先级：值越小，优先级越高
     */
    @ApiModelProperty(value = "优先级：值越小，优先级越高")
    private Integer priority;

    @Transient
    @ApiModelProperty(value = "备用字段")
    private String spareField;

}
