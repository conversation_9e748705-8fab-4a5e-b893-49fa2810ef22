//package iet.ustb.sf.schedule;
//
//
//import com.alibaba.fastjson.JSONObject;
//import iet.ustb.sf.service.WeComMessagePushService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//import org.springframework.web.client.RestClientException;
//
//@Component
//@EnableScheduling
//public class KBTask {
//
//    @Value("${spring.profiles.active}")
//    String activeModel;
//
//    @Autowired
//    WeComMessagePushService weComMessagePushService;
//
//    @Scheduled(cron = "0 0 7 * * ?")
//    public void sendKbToUsers(){
//        weComMessagePushService.sendKbToUsers();
//    }
//
//}
