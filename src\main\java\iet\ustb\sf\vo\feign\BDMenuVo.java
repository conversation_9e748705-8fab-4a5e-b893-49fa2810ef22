package iet.ustb.sf.vo.feign;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BDMenuVo {
    //菜单编码
    private String code;

    //菜单名称
    private String name;

    //父级菜单编码，顶级菜单的父级编码用0表示
    private String parentCode;

    //备注
    private String desc;

    //菜单图标url
    private String icon;

    //menu-菜单,button-按钮
    private String menuType;

    //链接
    private String url;

    //排序，数值小的排在前面
    private Integer sort;

    //删除所需要
    private List<String> menus;

}
