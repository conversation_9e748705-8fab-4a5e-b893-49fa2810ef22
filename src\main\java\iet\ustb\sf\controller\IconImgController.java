package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import iet.ustb.sf.vo.domain.IconImg;
import iet.ustb.sf.service.IconImgService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.*;
import lombok.extern.apachecommons.CommonsLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 图标管理
 *
 * <AUTHOR>
 * @create 2022-08-15
 */
@CommonsLog
@RestController
@RequestMapping("/iconImg")
@Api(value = "图标管理", tags = "图标管理")
public class IconImgController {

    @Autowired
    private IconImgService fileService;

    /**
     * 上传图标（批量）
     *
     * @param files
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/uploadImg", headers = "content-type=multipart/form-data")
    @ApiOperation(value = "上传图标", notes = "上传图标")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", name = "files", value = "多个图片", allowMultiple = true, dataType = "__file")
    })
    public AjaxJson uploadImg(@RequestPart @ApiParam(value = "files") MultipartFile[] files,
                              Integer iconType) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            fileService.uploadImg(files, iconType);
            ajaxJson.setData("上传成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    /**
     * 查询所有图片
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/listImg")
    @ApiOperation(value = "查询所有图标", notes = "查询所有图标")
    public AjaxJson listImg() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<IconImg> iconImgList = fileService.findAllImg();
            ajaxJson.setData(iconImgList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    /**
     * 根据id查询
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/findById")
    @ApiOperation(value = "根据id查询", notes = "根据id查询")
    public AjaxJson findById(@RequestBody JSONObject json) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            IconImg iconImg = fileService.findById(json.getString("id"));
            ajaxJson.setData(iconImg);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    /**
     * 根据id删除
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/delete")
    @ApiOperation(value = "根据id删除", notes = "{\"id\": \"7e3fdff1-9b31-4e32-9c75-47179a34c306\"}")
    public AjaxJson delete(@RequestBody JSONObject json) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            fileService.deleteById(json.getString("id"));
            ajaxJson.setData("删除成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    /**
     * 分页查询图标信息
     * @param iconImg
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/getIconImgByPage")
    public AjaxJson getIconImgByPage(@RequestBody IconImg iconImg) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            IPage<IconImg> iconImgList = fileService.getIconImgByPage(iconImg);
            ajaxJson.setData(iconImgList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    /**
     * 更新图标信息
     * @param file
     * @param iconImg
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/updateIconImg", headers = "content-type=multipart/form-data")
    public AjaxJson updateIconImg(@RequestPart(value ="file", required = false) MultipartFile file,@RequestPart("iconImg")IconImg iconImg) {
        iconImg.setFile(file);
        AjaxJson ajaxJson = new AjaxJson();
        try {
            fileService.updateIconImg(iconImg);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


}




