package iet.ustb.sf.controller;


import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.service.MenuApplyService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 菜单申请页面
 */
@RestController
@RequestMapping("/menu")
public class MenuApplyController {

    @Autowired
    MenuApplyService menuApplyService;

    @ResponseBody
    @PostMapping("/save")
    @ApiOperation(value = "新增", notes = "{\n" +
            "    \"userNo\":\"111\",\n" +
            "    \"userName\":\"AAA\",\n" +
            "    \"ownRscList\":\"1,2,3,4\",\n" +
            "    \"newRscList\":\"5,6,7,8\",\n" +
            "    \"status\":0,\n" +
            "    \"remark\":\"save\"\n" +
            "}", produces = "application/json")
    public AjaxJson save(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(menuApplyService.save(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/update")
    @ApiOperation(value = "修改状态,已完成", notes = "{\n" +
            "    \"id\":\"e5f6616c-da1f-4556-9b6b-f1a55f1653cf\",\n" +
            "    \"userNo\":\"111\",\n" +
            "    \"userName\":\"AAA\",\n" +
            "    \"ownRscList\":\"1,2,3,4\",\n" +
            "    \"newRscList\":\"5,6,7,8,9\",\n" +
            "    \"status\":1,\n" +
            "    \"remark\":\"update\"\n" +
            "}}", produces = "application/json")
    public AjaxJson update(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(menuApplyService.update(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/find")
    @ApiOperation(value = "update", notes = "{\n" +
            "\t\"userNo\": \"\",\n" +
            "\t\"userName\": \"\",\n" +
            "\t\"status\": \"\",\n" +
            "\t\"startTime\": \"\",\n" +
            "\t\"endTime\": \"\",\n" +
            "\t\"pageIndex\": \"\",\n" +
            "\t\"pageSize\": \"\"\n" +
            "}", produces = "application/json")
    public AjaxJson find(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(menuApplyService.find(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }



    @ResponseBody
    @PostMapping("/delete")
    @ApiOperation(value = "修改状态,已完成", notes = "{\n" +
            "    \"id\":\"e5f6616c-da1f-4556-9b6b-f1a55f1653cf\"\n" +
            "}}", produces = "application/json")
    public AjaxJson delete(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(menuApplyService.delete(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

}
