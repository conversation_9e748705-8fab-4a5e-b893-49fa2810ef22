package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.TaskTracking;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * 任务进度跟踪
 *
 * <AUTHOR>
 * @create 2024-04-01
 */
public interface TaskTrackingService {

    /**
     * 查找所有
     *
     * @return {@link List }<{@link TaskTracking }>
     * <AUTHOR>
     * @create 2024-04-01
     */
    List<TaskTracking> findAll();

    /**
     * 按多条件查找全部
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link TaskTracking }>
     * <AUTHOR>
     * @create 2024-04-01
     */
    List<TaskTracking> findAllByMultiCondition(JSONObject jsonObject);

    /**
     * 按多条件查找
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link TaskTracking }>
     * <AUTHOR>
     * @create 2024-04-01
     */
    Page<TaskTracking> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param TaskTracking
     * <AUTHOR>
     * @create 2024-04-01
     */
    void save(TaskTracking TaskTracking);

    /**
     * 批量保存
     *
     * @param TaskTrackings
     * <AUTHOR>
     * @create 2024-04-01
     */
    void saveAll(List<TaskTracking> TaskTrackings);

    /**
     * 删除
     *
     * @param TaskTracking
     * <AUTHOR>
     * @create 2024-04-01
     */
    void delete(TaskTracking TaskTracking) throws Exception;

    /**
     * 推送超时任务到到企业微信
     * @Author: 023958 茆荣伟
     * @Create: 2024-04-01 15:30
     */
    void sendMessageToWecom();

    /**
     * 获取任务评分统计排行
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * @Author: 023958 茆荣伟
     * @Create: 2024-04-07 01:01
     */
    List<Map<String, Object>> findTaskScoreStatistics();
}
