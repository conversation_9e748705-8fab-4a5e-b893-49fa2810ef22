package iet.ustb.sf.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import iet.ustb.sf.vo.domain.SysNotice;
import iet.ustb.sf.vo.domain.SysNoticeUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 公告和用户关系表(SysNoticeUser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-28 17:09:33
 */
public interface SysNoticeUserMapper {

    /**
     * 查询指定行数据
     *
     * @param sysNoticeUser 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<SysNoticeUser> queryAllByLimit(SysNoticeUser sysNoticeUser, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param sysNoticeUser 查询条件
     * @return 总行数
     */
    long count(SysNoticeUser sysNoticeUser);

    /**
     * 新增数据
     *
     * @param sysNoticeUser 实例对象
     * @return 影响行数
     */
    int insert(SysNoticeUser sysNoticeUser);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<SysNoticeUser> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<SysNoticeUser> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<SysNoticeUser> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<SysNoticeUser> entities);

    /**
     * 根据用户账号获取公告
     * @param sysNotice
     * @return
     */
    List<SysNotice> getNoticeByUserNo(SysNotice sysNotice);

    /**
     *
     * @param page
     * @param sysNotice
     * @return
     */
    IPage<SysNotice> getNoticeByPage(Page<SysNotice> page, SysNotice sysNotice);

    void deleteByNoticeId(@Param("noticeId")  String noticeId);

}

