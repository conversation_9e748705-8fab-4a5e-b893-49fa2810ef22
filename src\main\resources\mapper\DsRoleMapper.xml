<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="iet.ustb.sf.mapper.DsRoleMapper">

    <resultMap type="iet.ustb.sf.vo.domain.Role" id="DsRoleMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="isDef" column="isdef" jdbcType="VARCHAR"/>
        <result property="roleCode" column="rolecode" jdbcType="VARCHAR"/>
        <result property="roleName" column="rolename" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="createDateTime" column="createdatetime" jdbcType="TIMESTAMP"/>
        <result property="createUserNo" column="createuserno" jdbcType="VARCHAR"/>
        <result property="updateDateTime" column="updatedatetime" jdbcType="TIMESTAMP"/>
        <result property="updateUserNo" column="updateuserno" jdbcType="VARCHAR"/>
        <result property="moduleName" column="moduleName" jdbcType="VARCHAR"/>
        <result property="orgID" column="orgID" jdbcType="VARCHAR"/>
        <result property="roleType" column="roleType" jdbcType="INTEGER"/>
        <result property="moduleCode" column="moduleCode" jdbcType="VARCHAR"/>
        <result property="isNewRole" column="isNewRole" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into resource.ds_role(description, isdef, rolecode, rolename, status, type, createdatetime, createuserno, updatedatetime, updateuserno, moduleName, orgID, roleType, moduleCode, isNewRole)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.description}, #{entity.isDef}, #{entity.roleCode}, #{entity.roleName}, #{entity.status}, #{entity.type}, #{entity.createDateTime}, #{entity.createUserNo}, #{entity.updateDateTime}, #{entity.updateUserNo}, #{entity.moduleName}, #{entity.orgID}, #{entity.roleType}, #{entity.moduleCode}, #{entity.isNewRole})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into resource.ds_role(description, isdef, rolecode, rolename, status, type, createdatetime, createuserno, updatedatetime, updateuserno, moduleName, orgID, roleType, moduleCode, isNewRole)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.description}, #{entity.isDef}, #{entity.roleCode}, #{entity.roleName}, #{entity.status}, #{entity.type}, #{entity.ccreateDateTime}, #{entity.createUserNo}, #{entity.updateDateTime}, #{entity.updateUserNo}, #{entity.moduleName}, #{entity.orgID}, #{entity.roleType}, #{entity.moduleCode}, #{entity.isNewRole})
        </foreach>
        on duplicate key update
description = values(description) , isdef = values(isdef) , rolecode = values(rolecode) , rolename = values(rolename) , status = values(status) , type = values(type) , createdatetime = values(createdatetime) , createuserno = values(createuserno) , updatedatetime = values(updatedatetime) , updateuserno = values(updateuserno) , moduleName = values(moduleName) , orgID = values(orgID) , roleType = values(roleType) , moduleCode = values(moduleCode) , isNewRole = values(isNewRole)     </insert>

    <!-- 删除角色 -->
    <delete id="deleteByID" parameterType="iet.ustb.sf.vo.domain.Role">
        DELETE FROM resource.ds_role
        WHERE
            id = #{id}
    </delete>

    <!-- 删除角色和菜单的关联关系 -->
    <delete id="deleteResByRoleID" parameterType="iet.ustb.sf.vo.domain.Role">
        DELETE FROM resource.ds_role_resourcelist

        <where>
            <if test="id != null and id != ''">
                role_id = #{id}
            </if>

            <if test="resourceId != null and resourceId != ''">
                resourcelist_id = #{resourceId}
            </if>

        </where>
    </delete>

    <!-- 删除角色和用户的关联关系 -->
    <delete id="deleteUserByRoleID" parameterType="iet.ustb.sf.vo.domain.Role">
        DELETE FROM resource.ds_role_userlist

        <where>
            <if test="id != null and id != ''">
                role_id = #{id}
            </if>

            <if test="userId != null and userId != ''">
                userlist_id = #{userId}
            </if>

        </where>
    </delete>

    <select id="getRoleInfo" resultType="iet.ustb.sf.vo.domain.Role">
        select
            r.*
        from
            resource.ds_role r
        left join resource.ds_role_userlist ru on ru.role_id = r.id

        <where>
            <if test="id != null and id != ''">
                 and r.id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and ru.userlist_id = #{userId}
            </if>

        </where>>

    </select>
</mapper>

