<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="iet.ustb.sf.mapper.DuUserMapper">

    <resultMap type="iet.ustb.sf.vo.domain.User" id="DuUserMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="userNo" column="userno" jdbcType="VARCHAR"/>
        <result property="userName" column="username" jdbcType="VARCHAR"/>
        <result property="academicDeg" column="academicdeg" jdbcType="VARCHAR"/>
        <result property="birthDay" column="birthday" jdbcType="VARCHAR"/>
        <result property="cerType" column="certype" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="empCategory" column="empcategory" jdbcType="VARCHAR"/>
        <result property="folk" column="folk" jdbcType="VARCHAR"/>
        <result property="idNum" column="idnum" jdbcType="VARCHAR"/>
        <result property="jobNo" column="jobno" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="wechatNo" column="wechatno" jdbcType="VARCHAR"/>
        <result property="loginId" column="loginid" jdbcType="VARCHAR"/>
        <result property="spareField" column="sparefield" jdbcType="VARCHAR"/>
        <result property="isModifiedPwd" column="ismodifiedpwd" jdbcType="VARCHAR"/>
        <result property="isSelected" column="isselected" jdbcType="VARCHAR"/>
        <result property="repeatField" column="repeatfield" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="workStatus" column="workstatus" jdbcType="VARCHAR"/>
        <result property="tel" column="tel" jdbcType="VARCHAR"/>
        <result property="sex" column="sex" jdbcType="VARCHAR"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="politicsName" column="politicsname" jdbcType="VARCHAR"/>
        <result property="payCompId" column="paycompid" jdbcType="VARCHAR"/>
        <result property="orgCode" column="orgcode" jdbcType="VARCHAR"/>
        <result property="operStus" column="operstus" jdbcType="VARCHAR"/>
        <result property="nationality" column="nationality" jdbcType="VARCHAR"/>
        <result property="mobPhone" column="mobphone" jdbcType="VARCHAR"/>
        <result property="userID" column="userID" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into resource.du_user(userno, username, academicdeg, birthday, certype, email, empcategory, folk, idnum, jobno, password, wechatno, loginid, sparefield, ismodifiedpwd, isselected, repeatfield, status, workstatus, tel, sex, remarks, politicsname, paycompid, orgcode, operstus, nationality, mobphone, userID)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.userno}, #{entity.username}, #{entity.academicdeg}, #{entity.birthday}, #{entity.certype}, #{entity.email}, #{entity.empcategory}, #{entity.folk}, #{entity.idnum}, #{entity.jobno}, #{entity.password}, #{entity.wechatno}, #{entity.loginid}, #{entity.sparefield}, #{entity.ismodifiedpwd}, #{entity.isselected}, #{entity.repeatfield}, #{entity.status}, #{entity.workstatus}, #{entity.tel}, #{entity.sex}, #{entity.remarks}, #{entity.politicsname}, #{entity.paycompid}, #{entity.orgcode}, #{entity.operstus}, #{entity.nationality}, #{entity.mobphone}, #{entity.userid})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into resource.du_user(userno, username, academicdeg, birthday, certype, email, empcategory, folk, idnum, jobno, password, wechatno, loginid, sparefield, ismodifiedpwd, isselected, repeatfield, status, workstatus, tel, sex, remarks, politicsname, paycompid, orgcode, operstus, nationality, mobphone, userID)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userno}, #{entity.username}, #{entity.academicdeg}, #{entity.birthday}, #{entity.certype}, #{entity.email}, #{entity.empcategory}, #{entity.folk}, #{entity.idnum}, #{entity.jobno}, #{entity.password}, #{entity.wechatno}, #{entity.loginid}, #{entity.sparefield}, #{entity.ismodifiedpwd}, #{entity.isselected}, #{entity.repeatfield}, #{entity.status}, #{entity.workstatus}, #{entity.tel}, #{entity.sex}, #{entity.remarks}, #{entity.politicsname}, #{entity.paycompid}, #{entity.orgcode}, #{entity.operstus}, #{entity.nationality}, #{entity.mobphone}, #{entity.userid})
        </foreach>
        on duplicate key update
        userno = values(userno) , username = values(username) , academicdeg = values(academicdeg) , birthday = values(birthday) , certype = values(certype) , email = values(email) , empcategory = values(empcategory) , folk = values(folk) , idnum = values(idnum) , jobno = values(jobno) , password = values(password) , wechatno = values(wechatno) , loginid = values(loginid) , sparefield = values(sparefield) , ismodifiedpwd = values(ismodifiedpwd) , isselected = values(isselected) ,  repeatfield = values(repeatfield) , status = values(status) , workstatus = values(workstatus) , tel = values(tel) , sex = values(sex) , remarks = values(remarks) , politicsname = values(politicsname) , paycompid = values(paycompid) , orgcode = values(orgcode) , operstus = values(operstus) , nationality = values(nationality) , mobphone = values(mobphone) , userID = values(userID)
        </insert>

    <select id="getUsersByOrgCodes" resultMap="DuUserMap">
        SELECT * FROM resource.du_user
        WHERE orgcode IN
        <foreach collection="orgCodes" item="orgCode" open="(" separator="," close=")">
            #{orgCode}
        </foreach>
        and operStus != 'D'
    </select>

    <select id="findAllUser" resultType="iet.ustb.sf.vo.domain.User">
        SELECT
            u.*,
            o.orgallname as orgName
        FROM
            resource.du_user u
        left join resource.du_org o on u.orgcode = o.orgcode
        WHERE
            u.status = "0"
            and u.operstus != 'D'
            <if test="params.orgCode != null and params.orgCode.size() > 0">
                AND u.orgCode IN
                <foreach collection="params.orgCode" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="params.userNo != null and params.userNo != ''">
                AND u.userNo LIKE CONCAT('%', #{params.userNo}, '%')
            </if>
            <if test="params.userName != null and params.userName != ''">
                AND u.userName LIKE CONCAT('%', #{params.userName}, '%')
            </if>
            <if test="params.sex != null and params.sex != ''">
                AND u.sex = #{params.sex}
            </if>
            <if test="params.empCategory != null and params.empCategory != ''">
                AND u.empCategory = #{params.empCategory}
            </if>


        ORDER BY u.createdatetime DESC
    </select>

    <select id="getUserInfo" resultType="iet.ustb.sf.vo.domain.User">
        SELECT
        *
        FROM
        resource.du_user
        <where>
            <if test="status != null and status != ''">
                AND status = #{userNo}
            </if>
            <if test="operStus != null and operStus != ''">
                AND operstus = #{operStus}
            </if>
            <if test="loginId != null and loginId != ''">
                AND loginid = #{loginId}
            </if>c
            <if test="userNo != null and userNo != ''">
                AND userNo LIKE CONCAT('%', #{userNo}, '%')
            </if>
            <if test="userName != null and userName != ''">
                AND userName LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="sex != null and sex != ''">
                AND sex = #{sex}
            </if>
            <if test="empCategory != null and empCategory != ''">
                AND empCategory = #{empCategory}
            </if>
        </where>

        ORDER BY id DESC
    </select>

    <select id="getNoDelUserInfo" resultType="iet.ustb.sf.vo.domain.User">
        SELECT
        *
        FROM
        resource.du_user
        WHERE
             status = "0"
            AND operstus != "D"
            <if test="loginId != null and loginId != ''">
                AND loginid = #{loginId}
            </if>
            <if test="userNo != null and userNo != ''">
                AND userNo = #{userNo}
            </if>
            <if test="userName != null and userName != ''">
                AND userName LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="sex != null and sex != ''">
                AND sex = #{sex}
            </if>
            <if test="empCategory != null and empCategory != ''">
                AND empCategory = #{empCategory}
            </if>

        ORDER BY id DESC
    </select>


</mapper>

