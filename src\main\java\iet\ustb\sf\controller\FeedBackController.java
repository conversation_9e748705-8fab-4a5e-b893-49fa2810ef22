package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.FeedBack;
import iet.ustb.sf.service.FeedBackService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 问题反馈
 *
 * <AUTHOR>
 * @create 2022-10-26
 */
@RestController
@RequestMapping("/feedBack")
@Api(value = "问题反馈", tags = "问题反馈")
public class FeedBackController {

    @Autowired
    private FeedBackService feedBackService;

    @PostMapping("/findAll")
    @ApiOperation(value = "查询所有", notes = "查询所有")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<FeedBack> feedBackList = feedBackService.findAll();
            ajaxJson.setData(feedBackList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findPageByMultiCondition")
    @ApiOperation(value = "按多条件查找分页", notes = "入参：{\"title\": \"文字乱码\"}")
    public AjaxJson findPageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<FeedBack> feedBackPage = feedBackService.findPageByMultiCondition(jsonObject);
            ajaxJson.setData(feedBackPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportUserFeedBackExcel")
    @ApiOperation(value = "导出用户反馈统计Excel", notes = "入参：{\"startDate\": \"2023-08-01\", \"endDate\": \"2023-08-31\"}")
    public AjaxJson exportUserFeedBackExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            feedBackService.exportUserFeedBackExcel(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存", notes = "保存")
    public AjaxJson save(@RequestBody FeedBack feedBack) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            feedBackService.save(feedBack);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "入参：{\"id\":\"f356e461-0565-4ed4-bed1-29fb810bb160\"}")
    public AjaxJson delete(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            feedBackService.delete(jsonObject);
            ajaxJson.setData("删除成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

}
