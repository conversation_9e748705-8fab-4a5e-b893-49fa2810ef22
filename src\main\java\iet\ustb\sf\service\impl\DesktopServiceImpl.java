package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.*;
import iet.ustb.sf.vo.domain.DashConfig;
import iet.ustb.sf.vo.domain.Desktop;
import iet.ustb.sf.vo.domain.DesktopConfig;
import iet.ustb.sf.service.DesktopService;
import iet.ustb.sf.utils.ToolsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.service.impl
 * @title: DesktopServiceImpl
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/2516:22
 */
@Service
@Transactional
public class DesktopServiceImpl implements DesktopService {

    @Autowired
    DesktopDao desktopDao;

//    @Qualifier(value = "defaultDesktop")
//    @Autowired
//    Desktop defaultDesktop;

    @Qualifier(value = "defaultDesktopList")
    @Autowired
    List<Desktop> defaultDesktopList;

    @Autowired
    DesktopConfigDao desktopConfigDao;

    @Autowired
    DashConfigDao dashConfigDao;

    @Override
    public List<Desktop> getDefaultDesktop(JSONObject jsonObject) {
        return defaultDesktopList;
    }

    @Override
    public List<Desktop> getDesktopByUserNo(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return null;
        } else {
            return desktopDao.findDesktopByUserNo(jsonObject.getString("userNo"));
        }
    }

    @Override
    public Desktop getDesktopByIDAndUserNo(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return null;
        } else {
            return desktopDao.findDesktopByIDAndUserNo(jsonObject.getString("desktopID"), jsonObject.getString("userNo"));
        }
    }

    @Override
    public List<DesktopConfig> getDesktopConfigsByUserNo(JSONObject jsonObject) {
        if (!ToolsUtil.isEmpty(jsonObject.getString("userNo"))) {
            List<DesktopConfig> desktopConfigList = desktopConfigDao.findConfigsByUserNo(jsonObject.getString("userNo"));
            return desktopConfigList;
        }
        return new ArrayList<>();
    }

    @Override
    public String createDesktop(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return "";
        } else {
            Desktop desktop = ToolsUtil.jsonObjectToEntity(jsonObject, Desktop.class);
            desktop.setDpCheck(3);
            desktop.setDeleteTag(0);
            return desktopDao.save(desktop).getId();
        }
    }


    @Override
    public String deleteDesktop(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return "";
        } else {
            Desktop desktop = desktopDao.getById(jsonObject.getString("deskID"));
            desktop.setDeleteTag(1);
            return desktopDao.save(desktop).getId();
        }
    }

    @Override
    public String saveDesktopConfig(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return "";
        } else {
            String userNo = jsonObject.getString("userNo");
            String desktopID = jsonObject.getString("desktopID");
            String config = jsonObject.getString("config");

            if (!ToolsUtil.isEmpty(userNo) && !ToolsUtil.isEmpty(desktopID) && !ToolsUtil.isEmpty(config)) {
                DesktopConfig cfg = desktopConfigDao.findOneByUserNoAndDesktopID(userNo, desktopID);
                if (!ToolsUtil.isEmpty(cfg)) {
                    cfg.setUserNo(userNo);
                    cfg.setDesktopID(desktopID);
                    cfg.setConfig(config);
                    return desktopConfigDao.save(cfg).getId();
                } else {
                    DesktopConfig desktopConfig = new DesktopConfig();
                    desktopConfig.setUserNo(userNo);
                    desktopConfig.setDesktopID(desktopID);
                    desktopConfig.setConfig(config);
                    return desktopConfigDao.save(desktopConfig).getId();
                }
            } else {
                return "";
            }
        }
    }

    @Override
    public DesktopConfig getDesktopConfig(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return null;
        } else {
            DesktopConfig desktopConfig = desktopConfigDao.findOneByUserNoAndDesktopID(jsonObject.getString("userNo"), jsonObject.getString("desktopID"));
            return desktopConfig;
        }
    }

    @Override
    public String saveDashConfig(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return "";
        } else {
            DashConfig dashConfig = new DashConfig();
            String userNo = jsonObject.getString("userNo");
            List<JSONObject> jsonObjects = (List<JSONObject>) jsonObject.get("config");
            dashConfig.setUserNo(userNo);
            dashConfig.setConfig(JSON.toJSONString(jsonObjects));
            return dashConfigDao.save(dashConfig).getId();
        }
    }

    @Override
    public DashConfig getDashConfig(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return null;
        } else {
            DashConfig dashConfig = dashConfigDao.findOneByIDAndUserNo(jsonObject.getString("id"), jsonObject.getString("userNo"));
            return dashConfig;
        }
    }
}
