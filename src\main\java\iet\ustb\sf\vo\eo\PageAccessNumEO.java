package iet.ustb.sf.vo.eo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 页面访问次数排行
 *
 * <AUTHOR>
 * @create 2022-11-03
 * @see Serializable
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageAccessNumEO implements Serializable {

    /**
     * 资源id
     */
    private String resourceId;
    /**
     * 应用名称
     */
    @Excel(name = "应用名称")
    private String code;
    /**
     * 父集菜单
     */
    @Excel(name = "父集菜单", width = 50)
    private String parentName;
    /**
     * 页面名称
     */
    @Excel(name = "页面名称", width = 50)
    private String name;
    /**
     * 访问次数
     */
    @Excel(name = "访问次数")
    private Long num;
}
