package iet.ustb.sf.vo.domain;


import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 组织表
 */
@Data
@Entity
@Table(name = "DU_ORG")
public class Org extends BaseEntity {

    /**
     * 组织编号
     */
    @Column(length = 64, unique = true, nullable = false)
    private String orgCode;

    /**
     * 组织名称
     */
    @Column(length = 64, nullable = false)
    private String orgName;

    /**
     * 父级编号
     */
    @Column(length = 64)
    private String parentOrgCode;

    /**
     * 组织类型
     */
    @Column(length = 64, nullable = false)
    private String orgType;

    /**
     * 组织全称
     */
    @Column(nullable = false)
    private String orgAllName;

    /**
     * 预算币种编号
     */
    @Column(length = 64, nullable = false)
    private String orgCurCode;

    /**
     * 是否停用状态 0-可用,1-不可用
     */
    @Column(length = 64, nullable = false)
    private String status;

    /**
     * 简介
     */
    private String orgDesc;

    /**
     * 传真
     */
    private String fax;

    /**
     * 网址
     */
    private String webAddr;

    /**
     * 法人代表
     */
    private String orgLegalPerson;

    /**
     * 是否企业法人
     */
    private String isLegalPerson;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 操作标识 N 新增,U修改,D删除
     */
    private String operStus;

    /**
     * erp专用字段
     */
    private String erpSpecField;

    /**
     * 核算组织编码
     */
    private String accOrgCode;

    /**
     * 总账系统代码
     */
    @Column(length = 64)
    private String accSysCode;

    /**
     * 组织层级
     */
    @Column(length = 64)
    private String orgLevel;

    /**
     * 判重项目值域
     */
    private String repeatField;

    @Transient
    private List<Org> children;
}
