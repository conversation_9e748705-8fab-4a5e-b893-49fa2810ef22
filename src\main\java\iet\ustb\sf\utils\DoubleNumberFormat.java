package iet.ustb.sf.utils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: Dr.Monster
 * @Slogan: The never-compromising angry youth.
 * @PackageName: iet.ustb.vbt.serializer
 * @Date: 2023/03/03/08:50
 * @Description:
 */
public class DoubleNumberFormat extends JsonSerializer<Double> {
    public DoubleNumberFormat() {
    }

    public void serialize(Double value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value != null) {
            BigDecimal decimal = new BigDecimal(value);
            //四舍五入，小数点后两位
            gen.writeNumber(decimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        } else {
            gen.writeNumber(value);
        }
    }
}