//package iet.ustb.sf.task;
//
//import com.alibaba.fastjson.JSONObject;
//import iet.ustb.sf.utils.DateTools;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.kafka.core.KafkaTemplate;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
///**
// * @Author: xibing
// * @Date: 2022-08-26 13:46
// * @PackageName:iet.ustb.sf.task
// * @ClassName: KafkaTask
// * @Description: kafka的定时任务
// * @Version: 1.0
// */
//@Slf4j
//@Component
//public class KafkaTask {
//    @Autowired
//    private KafkaTemplate<String, Object> kafkaTemplate;
//
//    /**
//     * 定时任务测试，每两秒执行
//     */
////    @Scheduled(cron = "0/5 * * * * ?")
//    public void test() throws InterruptedException {
//
//        log.info("---" + DateTools.getFullNowDateTime());
////        System.exit(0);
////        log.info(DateTools.idmGetLastDay());
////        String value = "{\"date\":\"2022-10-24 11:45:00\",\"matId\":\"202209\",\"id\":\"IDM_MAT_LAST_MONTH\",\"type\":\"UPDATE\"}";
////        String value1 = "{\"date\":\"2022-09-21 00:00:00\",\"matId\":\"202209\",\"id\":\"IDM_MAT_LAST_MONTH\"}";
////        String value2 = "{\"date\":\"2022-09-21 23:59:59\",\"matId\":\"20220921\",\"id\":\"IDM_MAT_DAY_END\"}";
////        kafkaTemplate.send("IDM_DATE_CHANGE", value);
////        kafkaTemplate.send("IDM_DATE_CHANGE", value2);
//
////        String value1 = DateTools.getFullNowDateTimeHourStart();
////        System.out.println(value1);
////        String value2 = DateTools.getFullNowDateTimeHourEnd();
////        System.out.println(value2);
//    }
//
//    /**
//     * 小时开始事件
//     */
//    @Scheduled(cron = "0 0 0/1 * * ?")
//    public void hourStart(){
//        //开始时间 6
//        String hourStartTime = DateTools.getFullNowDateTimeHourStart();
//        JSONObject hourStart = new JSONObject();
//        hourStart.put("id", "IDM_MAT_HOUT_START");
//        hourStart.put("matId", hourStartTime);
//        hourStart.put("date", DateTools.getFullNowDateTime());
//
//        JSONObject hourStartBody = new JSONObject();
//        hourStartBody.put("date", DateTools.getFullNowDateTime());
//        hourStart.put("body", hourStartBody);
//        //结束时间 5
//        String hourEndTime = DateTools.getFullNowDateTimeHourEnd();
//        JSONObject hourEnd = new JSONObject();
//        hourEnd.put("id", "IDM_MAT_HOUR_END");
//        hourEnd.put("matId", hourEndTime);
//        hourEnd.put("date", DateTools.getFullNowDateTime());
//
//        JSONObject hourEndBody = new JSONObject();
//        hourEndBody.put("date", DateTools.getFullNowDateTime());
//        hourEnd.put("body", hourEndBody);
//
//        kafkaTemplate.send("IDM_DATE_CHANGE", hourStart.toString());
//        kafkaTemplate.send("IDM_DATE_CHANGE", hourEnd.toString());
//    }
//
//    /**
//     * 小时结束事件
//     */
//    @Scheduled(cron = "59 59 0/1 * * ?")
//    public void hourEnd(){
//        String hourStartTime = DateTools.getFullNowDateTimeHourStart();
//        JSONObject hourStart = new JSONObject();
//        hourStart.put("id", "IDM_MAT_HOUR_END");
//        hourStart.put("matId", hourStartTime);
//        hourStart.put("date", DateTools.getFullNowDateTime());
//
//        JSONObject body = new JSONObject();
//        body.put("date", DateTools.getFullNowDateTime());
//        hourStart.put("body", body);
//        //结束时间 5
//        String hourEndTime = DateTools.getFullNowDateTimeHourEnd1();
//        JSONObject hourEnd = new JSONObject();
//        hourEnd.put("id", "IDM_MAT_HOUR_START");
//        hourEnd.put("matId", hourEndTime);
//        hourEnd.put("date", DateTools.getFullNowDateTime());
//
//        JSONObject body1 = new JSONObject();
//        body1.put("date", DateTools.getFullNowDateTime());
//        hourEnd.put("body", body1);
//
//        kafkaTemplate.send("IDM_DATE_CHANGE", hourStart.toString());
//        kafkaTemplate.send("IDM_DATE_CHANGE", hourEnd.toString());
//    }
//
//    /**
//     * 日开始事件
//     */
//    @Scheduled(cron = "0 0 0 * * ?")
//    public void dayStart(){
//        String data = DateTools.idmGetCurrentYear()+DateTools.idmGetIntCurrentMonth()+DateTools.idmGetCurrentDay();
//        JSONObject valueMap = new JSONObject();
//        log.info("IDM_MAT_DAY_START:" + data);
//        valueMap.put("id", "IDM_MAT_DAY_START");
//        valueMap.put("matId", data);
//        valueMap.put("date", DateTools.getFullNowDateTime());
//
//        JSONObject body = new JSONObject();
//        body.put("date", DateTools.getFullNowDateTime());
//        valueMap.put("body", body);
//        kafkaTemplate.send("IDM_DATE_CHANGE", valueMap.toString());
//    }
//
//    /**
//     * 每天发送上个月事件
//     */
//    @Scheduled(cron = "0 30 0 * * ?")
//    public void lastMonth(){
//        String data = DateTools.lastMonth();
//        JSONObject valueMap = new JSONObject();
//        log.info("IDM_MAT_LAST_MONTH:" + data);
//        valueMap.put("id", "IDM_MAT_LAST_MONTH");
//        valueMap.put("type", "UPDATE");
//        valueMap.put("matId", data);
//        valueMap.put("date", DateTools.getFullNowDateTime());
//
//        JSONObject body = new JSONObject();
//        body.put("date", DateTools.getFullNowDateTime());
//        valueMap.put("body", body);
//        kafkaTemplate.send("IDM_DATE_CHANGE", valueMap.toString());
//    }
//
//    /**
//     * 日结束事件
//     */
//    @Scheduled(cron = "0 30 0 * * ?")
//    public void dayEnd(){
////        String data = DateTools.idmGetCurrentYear()+DateTools.idmGetIntCurrentMonth()+DateTools.idmGetCurrentDay();
//        String data = DateTools.idmGetLastDay();
//        JSONObject valueMap = new JSONObject();
//        log.info("IDM_MAT_DAY_END:" + data);
//        valueMap.put("id", "IDM_MAT_DAY_END");
//        valueMap.put("matId", data);
//        valueMap.put("date", DateTools.getLastDateTimeLastSencond());
//
//        JSONObject body = new JSONObject();
//        body.put("date", DateTools.getLastDateTimeLastSencond());
//        valueMap.put("body", body);
//        kafkaTemplate.send("IDM_DATE_CHANGE", valueMap.toString());
//    }
//
//    /**
//     * 月开始事件
//     */
//    @Scheduled(cron = "0 0 0 1 * ?")
//    public void monthStart(){
//        String data = DateTools.idmGetCurrentYear()+DateTools.idmGetIntCurrentMonth();
//        JSONObject valueMap = new JSONObject();
//        log.info("IDM_MAT_MONTH_START:" + data);
//        valueMap.put("id", "IDM_MAT_MONTH_START");
//        valueMap.put("matId", data);
//        valueMap.put("date", DateTools.getFullNowDateTime());
//
//        JSONObject body = new JSONObject();
//        body.put("date", DateTools.getFullNowDateTime());
//        valueMap.put("body", body);
//        kafkaTemplate.send("IDM_DATE_CHANGE", valueMap.toString());
//    }
//
//    /**
//     * 月结束事件
//     */
//    @Scheduled(cron = "0 30 0 1 * ?")
//    public void monthEnd(){
//        String data = DateTools.idmGetLastMonth();
//        JSONObject valueMap = new JSONObject();
//        log.info("IDM_MAT_MONTH_END:" + data);
//        valueMap.put("id", "IDM_MAT_MONTH_END");
//        valueMap.put("matId", data);
//        valueMap.put("date", DateTools.getFullNowDateTime());
//
//        JSONObject body = new JSONObject();
//        body.put("date", DateTools.getFullNowDateTime());
//        valueMap.put("body", body);
//        kafkaTemplate.send("IDM_DATE_CHANGE", valueMap.toString());
//    }
//
//    /**
//     * 每月当天dd
//     */
//    @Scheduled(cron = "0 0 1 * * ?")
//    public void monthDay(){
//        String data = DateTools.idmGetCurrentYear()+DateTools.idmGetIntCurrentMonth();
//        JSONObject valueMap = new JSONObject();
//        log.info("IDM_MAT_MONTH_DAY:" + data);
//        valueMap.put("id", "IDM_MAT_MONTH_DAY");
//        valueMap.put("matId", data);
//        valueMap.put("date", DateTools.getFullNowDateTime());
//
//        JSONObject body = new JSONObject();
//        body.put("date", DateTools.getFullNowDateTime());
//        valueMap.put("body", body);
//        kafkaTemplate.send("IDM_DATE_CHANGE", valueMap.toString());
//    }
//}
