package iet.ustb.sf.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import iet.ustb.sf.client.PermissionSystemClient;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * token的工具类
 * 使用jwt生成/验证token(jwt JSON Web Token)
 * jwt由三部分组成: 头部(header).载荷(payload).签证(signature)
 * <p>
 * 1.header头部承载两部分信息：
 * {
 * “type”: “JWT”, 声明类型,这里是jwt
 * “alg”: “HS256” 声明加密的算法 通常直接使用 HMAC SHA256
 * }
 * 将头部进行base64加密, 构成了第一部分
 * <p>
 * 2.payload载荷就是存放有效信息的地方
 * (1).标准中注册的声明
 * (2).公共的声明 (一般不建议存放敏感信息)
 * (3).私有的声明 (一般不建议存放敏感信息)
 * 将其进行base64加密,得到Jwt的第二部分
 * <p>
 * 3.signature签证信息由三部分组成：
 * (1).header (base64后的)
 * (2).payload (base64后的)
 * (3).secret
 * 需要base64加密后的header和base64加密后的payload连接组成的字符串,
 * 然后通过header中声明的加密方式进行加盐secret组合加密,构成了jwt的第三部分
 */
@Slf4j
public class JwtUtil {
    /**
     * token的失效时间
     */
    private final static long TIME_OUT = 24 * 60 * 60 * 1000;

    /**
     * token的密钥
     */
//    private final static String SECRET = "iet_admin";
    private final static String SECRET = "abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    /**
     * 生成token
     * 动态获取权限系统userid和时间戳，与权限系统token格式保持一致
     *
     * @param userId 用户ID
     * @param userName 用户名
     * @return String token字符串
     */
    public static String token(String userId, String userName) {
        String token = null;
        try {
            // 动态计算时间戳
            long currentTime = System.currentTimeMillis() / 1000;
            long expireTime = currentTime + (TIME_OUT / 1000);
            Date date = new Date(System.currentTimeMillis() + TIME_OUT);

            // 通过SpringContextUtil获取权限系统客户端，动态获取真实userid
            Long realUserId;
            try {
                PermissionSystemClient client = SpringContextUtil.getBean(PermissionSystemClient.class);
                if (client != null) {
                    realUserId = client.getUserIdByUserInfo(userId, userName);
                    log.debug("成功获取权限系统用户ID: {}, userId: {}, userName: {}", realUserId, userId, userName);
                } else {
                    realUserId = 1938155631131365376L; // 降级值
                    log.warn("SpringContextUtil获取PermissionSystemClient为null，使用默认userid: {}", realUserId);
                }
            } catch (Exception e) {
                realUserId = 1938155631131365376L; // 降级值
                log.warn("获取权限系统用户ID失败，使用默认userid: {}，错误: {}", realUserId, e.getMessage());
            }

            Algorithm algorithm = Algorithm.HMAC256(SECRET);
            Map<String, Object> headers = new HashMap<>();
            headers.put("type", "jwt");
            headers.put("alg", "HS256");

            // 生成token，使用动态获取的userid和时间戳
            token = JWT.create()
                    .withClaim("userId", userId)
                    .withClaim("userName", userName)
                    .withClaim("userid", realUserId)  // 动态获取的真实userid
                    .withClaim("iat", currentTime)    // 动态计算的签发时间
                    .withClaim("exp", expireTime)     // 动态计算的过期时间
                    .withExpiresAt(date)
                    .withHeader(headers)
                    .sign(algorithm);

            log.debug("JWT token生成成功，userId: {}, userName: {}, realUserId: {}, iat: {}, exp: {}",
                     userId, userName, realUserId, currentTime, expireTime);

        } catch (IllegalArgumentException | JWTCreationException e) {
            log.error("JWT token生成失败，userId: {}, userName: {}, 错误: {}", userId, userName, e.getMessage(), e);
            e.printStackTrace();
        }
        return token;
    }

    /**
     * token验证
     *
     * @param token token
     * @return String
     */
    public static boolean verify(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(SECRET);
            JWTVerifier jwtVerifier = JWT.require(algorithm).build();
            DecodedJWT decodedJWT = jwtVerifier.verify(token);
            // 客户端可以解密 所以一般不建议存放敏感信息
//            log.info("userId:" + decodedJWT.getClaim("userId").asString());
//            log.info("userName:" + decodedJWT.getClaim("userName").asString());
            return true;
        } catch (IllegalArgumentException | JWTVerificationException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 通过令牌获取用户编号
     *
     * @param token 令牌
     * @return {@link String }
     * <AUTHOR>
     * @create 2022-10-26
     */
    public static String getUserNoByToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(SECRET);
            JWTVerifier jwtVerifier = JWT.require(algorithm).build();
            DecodedJWT decodedJWT = jwtVerifier.verify(token);
            // 获取用户编号
            String userNo = decodedJWT.getClaim("userId").asString();
            return userNo;
        } catch (IllegalArgumentException | JWTVerificationException e) {
//            log.info("Exception：" + e);
        }
        return null;
    }


    /**
     * 测试
     *
     * @param args args
     */
    public static void main(String[] args) {
        String token = JwtUtil.token("023958", "123456");
        System.out.println(token);
        boolean v = JwtUtil.verify(token);
        System.out.println(v);
//        boolean v1 = JwtUtil.verify("eyJ0eXBlIjoiand0IiwiYWxnIjoiSFMyNTYiLCJ0eXAiOiJKV1QifQ.eyJwYXNzd29yZCI6IjEyMzQ1NiIsImV4cCI6MTYzMzk0MDI5NywidXNlcm5hbWUiOiJhZG1pbiJ9.-FZ41DVN3ycBhvOCNRcaf3rgoggvFK4gu1XGyH4xgAA");
//        System.out.println(v1);
    }
}
