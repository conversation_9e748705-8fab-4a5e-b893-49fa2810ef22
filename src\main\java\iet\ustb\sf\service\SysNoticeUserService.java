package iet.ustb.sf.service;

import iet.ustb.sf.vo.domain.SysNoticeUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

/**
 * 公告和用户关系表(SysNoticeUser)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-28 17:09:36
 */
public interface SysNoticeUserService {

    /**
     * 分页查询
     *
     * @param sysNoticeUser 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    Page<SysNoticeUser> queryByPage(SysNoticeUser sysNoticeUser, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param sysNoticeUser 实例对象
     * @return 实例对象
     */
    SysNoticeUser insert(SysNoticeUser sysNoticeUser);



}
