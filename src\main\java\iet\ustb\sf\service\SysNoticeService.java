package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import iet.ustb.sf.vo.domain.SysNotice;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 系统公告
 *
 * <AUTHOR>
 * @create 2024-02-18
 */
public interface SysNoticeService {

    /**
     * 查找所有
     *
     * @return {@link List }<{@link SysNotice }>
     * <AUTHOR>
     * @create 2024-02-18
     */
    List<SysNotice> findAll();

    /**
     * 按多条件查找全部
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link SysNotice }>
     * <AUTHOR>
     * @create 2024-02-18
     */
    List<SysNotice> findAllByMultiCondition(JSONObject jsonObject);

    /**
     * 按多条件查找
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link SysNotice }>
     * <AUTHOR>
     * @create 2024-02-18
     */
    Page<SysNotice> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 根据用户账号获取公告
     * @param sysNotice
     * @return
     */
    List<SysNotice> getNoticeByUserNo(SysNotice sysNotice);

    /**
     * 保存
     *
     * @param sysNotice
     * @return {@link SysNotice }
     * <AUTHOR>
     * @create 2024-02-20
     */
    SysNotice save(SysNotice sysNotice);

    /**
     * 批量保存
     *
     * @param sysNotices
     * <AUTHOR>
     * @create 2024-02-18
     */
    void saveAll(List<SysNotice> sysNotices);

    /**
     * 删除
     *
     * @param sysNotice
     * <AUTHOR>
     * @create 2024-02-18
     */
    void delete(SysNotice sysNotice) throws Exception;

    /**
     * 保存公告已阅读的用户
     * @param sysNotice
     */
    void saveNoticeByUser(SysNotice sysNotice);

    /**
     * 分页查询所有历史公告
     * @param sysNotice
     * @return
     */
    IPage<SysNotice> getNoticeByPage(SysNotice sysNotice);

}
