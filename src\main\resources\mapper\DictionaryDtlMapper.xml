<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="iet.ustb.sf.mapper.DictionaryDtlMapper">

    <resultMap type="iet.ustb.sf.vo.domain.DictionaryDtl" id="DictionaryDtlMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createDateTime" column="createdatetime" jdbcType="TIMESTAMP"/>
        <result property="createUserNo" column="createuserno" jdbcType="VARCHAR"/>
        <result property="updateDateTime" column="updatedatetime" jdbcType="TIMESTAMP"/>
        <result property="updateUserNo" column="updateuserno" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="value" column="value" jdbcType="VARCHAR"/>
        <result property="value1" column="value1" jdbcType="VARCHAR"/>
        <result property="value2" column="value2" jdbcType="VARCHAR"/>
        <result property="value3" column="value3" jdbcType="VARCHAR"/>
        <result property="dictId" column="dict_id" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into resource.dictionary_dtl(createdatetime, createuserno, updatedatetime, updateuserno, code, description, value, value1, value2, value3, dict_id)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.createDateTime}, #{entity.createUserNo}, #{entity.updateDateTime}, #{entity.updateUserNo}, #{entity.code}, #{entity.description}, #{entity.value}, #{entity.value1}, #{entity.value2}, #{entity.value3}, #{entity.dictId})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into resource.dictionary_dtl(createdatetime, createuserno, updatedatetime, updateuserno, code, description, value, value1, value2, value3, dict_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.createDateTime}, #{entity.createUserNo}, #{entity.updateDateTime}, #{entity.updateUserNo}, #{entity.code}, #{entity.description}, #{entity.value}, #{entity.value1}, #{entity.value2}, #{entity.value3}, #{entity.dictId})
        </foreach>
        on duplicate key update
createdatetime = values(createdatetime) , createuserno = values(createuserno) , updatedatetime = values(updatedatetime) , updateuserno = values(updateuserno) , code = values(code) , description = values(description) , value = values(value) , value1 = values(value1) , value2 = values(value2) , value3 = values(value3) , dict_id = values(dict_id)      </insert>


    <select id="getBasicDataInfo" resultType="iet.ustb.sf.vo.domain.DictionaryDtl">
        select
            d.code as parentCode,
            dt.*
        from resource.dictionary_dtl dt
        left join resource.dictionary d on dt.dict_id = d.id
        <where>
            d.code is not null
            <if test="type != null and type != ''">
                and d.code = #{type}
            </if>
            <if test="code != null and code != ''">
                and dt.code = #{code}
            </if>
            <if test="name != null and name != ''">
                and d.name = #{name}
            </if>

        </where>

    </select>

    <delete id="deleteByDicInfo"  parameterType="iet.ustb.sf.vo.domain.DictionaryDtl">
        DELETE FROM resource.dictionary_dtl
        where
            dict_id = #{dictId}
    </delete>
</mapper>

