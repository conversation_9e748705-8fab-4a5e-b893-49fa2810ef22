package iet.ustb.sf.vo.eo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 单位访问统计
 *
 * <AUTHOR>
 * @create 2022-11-03
 * @see Serializable
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrgAccessNumEO implements Serializable {

    /**
     * 部门编号
     */
    @Excel(name = "部门编号")
    private String orgCode;
    /**
     * 部门名称
     */
    @Excel(name = "部门名称", width = 25)
    private String orgName;
    /**
     * 部门人数
     */
    @Excel(name = "部门人数", type=10)
    private Long orgNum;
    /**
     * 应访问人数
     */
    @Excel(name = "应访问人数", type=10)
    private Long shouldUseNum;
    /**
     * 访问人数
     */
    @Excel(name = "实际访问人数", type=10, width = 15)
    private Long useNum;
    /**
     * 占比(%)
     */
    @Excel(name = "占比(%)", type=10)
    private BigDecimal rate;
    /**
     * 访问总次数
     */
    @Excel(name = "访问总次数", type=10)
    private Long num;
    /**
     * C层人均访问次数
     */
    @Excel(name = "C层人均访问次数", type=10, width = 20)
    private Long clevelAvgNum;
    /**
     * E层人均访问次数
     */
    @Excel(name = "E层人均访问次数", type=10, width = 20)
    private Long elevelAvgNum;
    /**
     * 问题反馈次数
     */
    @Excel(name = "问题反馈次数", type=10, width = 20)
    private Long feedBackNum;
    /**
     * 当月（人均）访问次数排名
     */
    @Excel(name = "当月（人均）访问次数排名", type=10, width = 20)
    private Integer avgAccessRank;
    /**
     * 当月（人均）问题反馈次数排名
     */
    @Excel(name = "当月（人均）问题反馈次数排名", type=10, width = 20)
    private Integer avgFeedBackRank;

    public OrgAccessNumEO(String orgCode, String orgName, Long orgNum,  Long shouldUseNum, Long useNum, BigDecimal rate, Long num, Long clevelAvgNum, Long elevelAvgNum, Long feedBackNum) {
        this.orgCode = orgCode;
        this.orgName = orgName;
        this.orgNum = orgNum;
        this.shouldUseNum = shouldUseNum;
        this.useNum = useNum;
        this.rate = rate;
        this.num = num;
        this.clevelAvgNum = clevelAvgNum;
        this.elevelAvgNum = elevelAvgNum;
        this.feedBackNum = feedBackNum;
    }
}
