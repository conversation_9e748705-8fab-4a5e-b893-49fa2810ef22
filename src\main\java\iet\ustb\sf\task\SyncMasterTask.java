//package iet.ustb.sf.schedule;
//
//import com.alibaba.fastjson.JSONObject;
//import iet.ustb.sf.dao.UserDao;
//import iet.ustb.sf.domain.User;
//import iet.ustb.sf.service.MasterDataService;
//import iet.ustb.sf.service.WeComMessagePushService;
//import iet.ustb.sf.utils.ToolsUtil;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//import org.springframework.web.client.RestTemplate;
//
//import java.text.SimpleDateFormat;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 同步主数据
// * <AUTHOR>
// * @create 2022-08-27
// */
//@Component
//@EnableScheduling
//public class SyncMasterTask {
//
//    @Autowired
//    private MasterDataService masterDataService;
//
//    @Autowired
//    WeComMessagePushService weComMessagePushService;
//
//    @Autowired
//    UserDao userDao;
//
//    @Autowired
//    RestTemplate restTemplate;
//
//    @Scheduled(cron = "0 30 00 * * ?")
//    public void syncMaster() {
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
//
//        Calendar calendar = Calendar.getInstance();// 获取当前日期
//        calendar.set(Calendar.HOUR_OF_DAY, 00);
//        calendar.set(Calendar.MINUTE, 00);
//        calendar.set(Calendar.SECOND, 00);
//        calendar.add(Calendar.DAY_OF_MONTH, -1);// 前一天
//        String beginDate = sdf.format(calendar.getTime());
//
//        // 一天的结束时间 23:59:59
//        calendar.set(Calendar.HOUR_OF_DAY,23);
//        calendar.set(Calendar.MINUTE,59);
//        calendar.set(Calendar.SECOND,59);
//        calendar.set(Calendar.MILLISECOND,999);
//        String endDate = sdf.format(calendar.getTime());
//
//        masterDataService.syncMaster(beginDate, endDate);
//    }
//
//
//    /**
//     * 同步企业微信的用户ID到数据库中
//     */
////    @Scheduled(cron = "0 0 0 * * ? ")
////    public void syncUserID(){
////        try{
////            List<User> userList = userDao.findAll();
////            if(ToolsUtil.isEmpty(userList)){
////                return;
////            }
////            String token = weComMessagePushService.getAccessToken();
////            String getUserIDUrl = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserid?access_token=" + token;
////            JSONObject phone = new JSONObject();
////            for(User user : userList){
////                if(ToolsUtil.isEmpty(user.getMobPhone())){
////                    continue;
////                }
////                phone.put("mobile" , user.getMobPhone());
////                JSONObject res = restTemplate.postForObject(getUserIDUrl, phone, JSONObject.class);
////                String userID = (String) res.get("userid");
////                if(!ToolsUtil.isEmpty(user)){
////                    user.setUserID(userID);
////                    userDao.saveAndFlush(user);
////                }
////            }
////        }catch (Exception e){
////
////        }
////    }
//}
