package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.CoordinateMatter;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 协调事宜Service
 *
 * <AUTHOR>
 * @create 2022-12-16
 */
public interface CoordinateMatterService {

    /**
     * 查找所有内容
     *
     * @return {@link List }<{@link CoordinateMatter }>
     * <AUTHOR>
     * @create 2022-12-16
     */
    List<CoordinateMatter> findAll();

    /**
     * 按多条件查找分页
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link CoordinateMatter }>
     * <AUTHOR>
     * @create 2022-12-16
     */
    Page<CoordinateMatter> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param coordinateMatter
     * @return {@link CoordinateMatter }
     * <AUTHOR>
     * @create 2022-12-16
     */
    CoordinateMatter save(CoordinateMatter coordinateMatter);

    /**
     * 删除
     *
     * @param jsonObject json对象
     * <AUTHOR>
     * @create 2022-12-16
     */
    void deleteByIds(JSONObject jsonObject);

    /**
     * 导出excel
     *
     * @param jsonObject json对象
     * @param response   回答
     * <AUTHOR>
     * @create 2022-12-16
     */
    void exportExcel(JSONObject jsonObject, HttpServletResponse response) throws Exception;

    /**
     * 置顶
     *
     * @param jsonObject json对象
     * <AUTHOR>
     * @create 2022-12-22
     */
    void topping(JSONObject jsonObject);

    /**
     * 按事项类型分组统计次数
     *
     * @param jsonObject json对象
     * @return {@link String }
     * <AUTHOR>
     * @create 2022-12-22
     */
    String findGroupByMatterType(JSONObject jsonObject);
}
