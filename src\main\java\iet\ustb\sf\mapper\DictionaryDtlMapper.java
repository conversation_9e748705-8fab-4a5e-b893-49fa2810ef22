package iet.ustb.sf.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import iet.ustb.sf.vo.domain.BasicDataConfig;
import iet.ustb.sf.vo.domain.DictionaryDtl;
import org.apache.ibatis.annotations.Param;
/**
 * (DictionaryDtl)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-17 08:19:57
 */
public interface DictionaryDtlMapper extends BaseMapper<DictionaryDtl> {

    /**
    * 批量新增数据（MyBatis原生foreach方法）
    *
    * @param entities List<DictionaryDtl> 实例对象列表
    * @return 影响行数
    */
    int insertBatch(@Param("entities") List<DictionaryDtl> entities);

    /**
    * 批量新增或按主键更新数据（MyBatis原生foreach方法）
    *
    * @param entities List<DictionaryDtl> 实例对象列表
    * @return 影响行数
    * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
    */
    int insertOrUpdateBatch(@Param("entities") List<DictionaryDtl> entities);

    /**
     * 查询数据字典信息
     * @param config
     * @return
     */
    List<DictionaryDtl> getBasicDataInfo(BasicDataConfig config);

    /**
     * 根据父级ID删除
     * @param dictionaryDtl
     * @return
     */
    int deleteByDicInfo(DictionaryDtl dictionaryDtl);

}

