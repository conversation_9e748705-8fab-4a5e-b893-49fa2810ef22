package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.Attach;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface AttachDao extends JpaSpecificationExecutor<Attach>, JpaRepository<Attach, String> {

    /**
     * 按业务id查找
     *
     * @param relatedId 业务id
     * @return {@link List }<{@link Attach }>
     * <AUTHOR>
     * @create 2022-10-27
     */
    @Query(value = "from Attach where relatedId = ?1 order by createDateTime")
    List<Attach> findByRelatedId(String relatedId);

    /**
     * 按业务id集合查找
     *
     * @param relatedIdList 相关id列表
     * @return {@link List }<{@link Attach }>
     * <AUTHOR>
     * @create 2023-04-12
     */
    @Query(value = "from Attach where relatedId in ?1 order by createDateTime")
    List<Attach> findByRelatedIds(List<String> relatedIdList);

}
