package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.AppLog;
import iet.ustb.sf.vo.eo.AccessNumEO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface AppLogService {

    /**
     * 查找所有
     *
     * @return {@link List }<{@link AppLog }>
     * <AUTHOR>
     * @create 2022-09-21
     */
    List<AppLog> findAll();

    /**
     * 按多条件查找全部
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link AppLog }>
     * <AUTHOR>
     * @create 2022-11-02
     */
    List<AppLog> findAllByMultiCondition(JSONObject jsonObject);

    /**
     * 按多条件查找
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link AppLog }>
     * <AUTHOR>
     * @create 2022-09-21
     */
    Page<AppLog> findByMultiCondition(JSONObject jsonObject);

    /**
     * 按登录时间查找不同组织/人员/应用访问次数
     *
     * @param jsonObj json对象
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-03
     */
    List<AccessNumEO> findAccessNumByLoginTime(JSONObject jsonObj);

    /**
     * 导出excel
     *
     * @param jsonObject json对象
     * @param response   回答
     * <AUTHOR>
     * @create 2022-11-08
     * @return
     */
    void exportExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException;

    /**
     * 导出领导和单位访问统计
     *
     * @param jsonObject json对象
     * @param response   回答
     * <AUTHOR>
     * @create 2023-08-25
     */
    void exportLeaderAndOrgExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException;

    /**
     * 导出word
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-08
     */
    JSONObject exportWord(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param appLog 应用程序日志
     * @param request
     * <AUTHOR>
     * @create 2022-09-21
     */
    void save(AppLog appLog, HttpServletRequest request);

    /**
     * 查询应用访问次数
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2023-03-23
     */
    List<AccessNumEO> findGroupAppByLoginTime(JSONObject jsonObject);
}
