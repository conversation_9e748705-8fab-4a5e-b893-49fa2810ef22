package iet.ustb.sf.vo.domain;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 页面日志
 */
@Data
@Entity
@Table(name = "PAGE_LOG_SECRET")
public class PageLogSecret extends BaseEntity {

    /**
     * 资源id
     */
    @Column(length = 64, nullable = false)
    private String resourceId;

    /**
     * 员工编号
     */
    @Column(length = 64, nullable = false)
    private String userNo;

    /**
     * 组织编号
     */
    @Column(length = 64)
    private String orgCode;

    /**
     * 厂处组织编号
     */
    @Column(length = 64)
    private String oneOrgCode;

    /**
     * 登录时间
     */
    @CreatedDate
    @Column(updatable = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date loginTime;

    /**
     * 登出时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date logoutTime;

    /**
     * 登录类型（0-pc 1-mobile 2-email 3-other 4-iphone 5-ipad 6-androidPhone 7-androidPad ）
     */
    private Integer loginType;

    /**
     * 登出类型（0-正常退出 1-在线 null-非正常退出）
     */
    private Integer logoutType;

    /**
     * 在线时长（秒）
     */
    private Integer onlineDuration;

    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 登录城市
     */
    private String loginCity;

    /**
     * 层级：1-厂处 2-科室 3-班组
     */
    private Integer level;
}
