package iet.ustb.sf.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import iet.ustb.sf.vo.domain.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * (DuUser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-06 14:22:58
 */
public interface DuUserMapper extends BaseMapper<User> {

    /**
    * 批量新增数据（MyBatis原生foreach方法）
    *
    * @param entities List<DuUser> 实例对象列表
    * @return 影响行数
    */
    int insertBatch(@Param("entities") List<User> entities);

    /**
    * 批量新增或按主键更新数据（MyBatis原生foreach方法）
    *
    * @param entities List<DuUser> 实例对象列表
    * @return 影响行数
    * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
    */
    int insertOrUpdateBatch(@Param("entities") List<User> entities);

    /**
     * 根据orgcode查询用户信息
     * @param orgCodes
     * @return
     */
    List<User> getUsersByOrgCodes(@Param("orgCodes") List<String> orgCodes);

    /**
     * 查询所有用户
     * @param params
     * @return
     */
    List<User> findAllUser(@Param("params") Map<String, Object> params);

    /**
     * 获取用户相关信息
     * @param user
     * @return
     */
    List<User> getUserInfo(User user);

    /**
     * 获取未删除的用户相关信息
     * @param user
     * @return
     */
    List<User> getNoDelUserInfo(User user);

}

