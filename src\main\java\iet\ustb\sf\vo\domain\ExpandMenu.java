package iet.ustb.sf.vo.domain;

import lombok.Data;
import org.hibernate.annotations.Proxy;
import org.springframework.beans.factory.annotation.Autowired;

import javax.persistence.*;
import java.util.List;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.domain
 * @title: ExpandMenu 拓展菜单
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/2918:56
 */

@Data
@Entity
@Table(name = "ExpandMenu")
@Proxy(lazy = false)
public class ExpandMenu extends BaseEntity {
    @Column
    String rscID;

    @Autowired
    String userNo;

    @OneToMany(fetch = FetchType.EAGER)
    List<Resource> resourceList;
}
