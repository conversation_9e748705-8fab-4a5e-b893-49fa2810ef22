package iet.ustb.sf.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 鉴权拦截器配置
 *
 * <AUTHOR>
 * @create 2022-10-26
 * @see WebMvcConfigurer
 */
@Configuration
public class AuthInterceptorConfig implements WebMvcConfigurer {

    @Autowired
    private AppTokenInterceptor appTokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        InterceptorRegistration registration = registry.addInterceptor(appTokenInterceptor);
        registration.addPathPatterns(
                "/**");
        registration.excludePathPatterns(
                "/**/login/**",
                "/swagger-resources/**",
                "/webjars/**",
                "/v3/**",
                "/**/attach/downloadFileById/**",// 下载附件接口，解决图片img标签无法传token问题
                "/**/role/findAllRole/**",// 角色KPI指标管理
                "/**/role/findAllRoleNoPage/**",
                "/**/org/findListByOrgCode/**",
                "/**/user/findOneUserByUserNo/**",
                "/**/user/findUserAndOrgByUserNo/**",
                "/**/webSocket/**",
                "/**/*.html",
                "/**/*.js",
                "/**/*.css",
                "/**/*.woff",
                "/**/*.ttf",
                "/**/menu/**",
                "/**/warningEvent/**",//报警、信息推送
                "/**/WarningRule/**",//报警规则
                "/**/WarningInfo/**",//报警信息
                "/**/role/findAllRole",//获取所有角色
                "/**/test/**",//test
                "/**/Async/**",//test
                "/WeComMessagePush/sendKbToUsers" //宽厚板厂看板推送
        );
    }
}
