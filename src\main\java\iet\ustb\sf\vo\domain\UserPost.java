package iet.ustb.sf.vo.domain;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 用户岗位中间表
 */
@Data
@Entity
@Table(name = "DU_USER_POST")
public class UserPost extends BaseEntity {

    /**
     * 用户id
     */
    @Column(length = 64, nullable = false)
    private String userId;

    /**
     * 组织编号
     */
    @Column(length = 64, nullable = false)
    private String orgCode;

    /**
     * 岗位编号
     */
    @Column(length = 64, nullable = false)
    private String postCode;

    /**
     * 是否主岗
     */
    @Column(length = 64, nullable = false)
    private String isMainPost;

    /**
     * 主岗编号
     */
    @Column(length = 64)
    private String mainPostCode;

    /**
     * 状态：0-启用,1-禁用
     */
    @Column(length = 64, nullable = false)
    private String status;

    /**
     * 操作标识：N 新增,U修改,D删除
     */
    @Column(length = 64)
    private String operStus;

    /**
     * 判重项目值域
     */
    private String repeatField;

}
