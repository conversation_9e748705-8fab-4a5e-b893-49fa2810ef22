package iet.ustb.sf.utils;

import java.util.HashMap;
import java.util.Map;

public class SsoParamUtil {
    public static final String client_id = "R9swewynMQ";
    public static final String client_secert = "7d9ce0ff-c1e4-431a-a21b-451d08955c99";
    public static final String appkey = "93b599864ef9d02dd79c1a6cd0255cd8";
    public static final Map<String, String> userKey;
    //	测试机
//	private static final String Host = "http://ssotest.nisco.cn/";
//	正式机
    private static final String Host = "https://sso.nisco.cn/";
    //	认证跳转
    public static final String SSOLOGIN = Host + "profile/oauth2/authorize";
    //	获取token
    public static final String SSOGETTOKEN = Host + "profile/oauth2/accessToken";
    //	获取个人信息
    public static final String SSOGETINFO = Host + "profile/oauth2/profile";
    //	统一登出
    public static final String SSOLOGINOUT = Host + "logout";

    static {
        userKey = new HashMap<String, String>();
    }
}
