package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.WarningInfo;
import iet.ustb.sf.service.WarningInfoService;
import iet.ustb.sf.vo.AjaxJson;
import iet.ustb.sf.vo.PageVo;
import iet.ustb.sf.vo.WarningInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author: Dr.Monster
 * @Title: WarningInfoController
 * @Date: 23/10/31 11:4254
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */
@RestController
@RequestMapping("/WarningInfo")
@Api(value = "报警记录", tags = "报警记录")
public class WarningInfoController {

    @Autowired
    WarningInfoService warningInfoService;

    @PostMapping("/findPageByMultiCondition")
    @ApiOperation(value = "按多条件查找分页", notes = "{\n" +
            "    \"pageIndex\":1,\n" +
            "    \"pageSize\":10,\n" +
            "    \"moduleCode\":\"\",\n" +
            "    \"alertContent\":\"\",\n" +
            "    \"deviceName\":\"\",\n" +
            "    \"areaName\":\"\",\n" +
            "    \"ifFalse\":\"1\",\n" +
            "    \"warningType\":\"0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测\",\n" +
            "    \"alertLevel\":\"1-一级，2-二级，3-三级，0-未评级\",\n" +
            "    \"isConfirm\":\"0-未确认，1-确认\",\n" +
            "    \"status\":\"0-未处理,1-已挂起,2-已处理,3-持续中\",\n" +
            "    \"roleIDs\": [\n" +
            "        \"384a8727-4a6f-4106-af3e-f30c0458c611\"\n" +
            "    ],\n" +
            "    \"startTime\":\"2023-12-01 08:00\",\n" +
            "    \"endTime\":\"2023-12-01 18:00\"\n" +
            "}")
    public AjaxJson findPageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<WarningInfo> warningRulePage = warningInfoService.findPageByMultiCondition(jsonObject);
            ajaxJson.setData(warningRulePage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }



    @PostMapping("/findFalsePageByMultiCondition")
    @ApiOperation(value = "按多条件查找误报分页", notes = "{\n" +
            "    \"pageIndex\":1,\n" +
            "    \"pageSize\":10,\n" +
            "    \"moduleCode\":\"\",\n" +
            "    \"alertContent\":\"\",\n" +
            "    \"deviceName\":\"\",\n" +
            "    \"areaName\":\"\",\n" +
            "    \"ifFalse\":\"1\",\n" +
            "    \"warningType\":\"0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测\",\n" +
            "    \"alertLevel\":\"1-一级，2-二级，3-三级，0-未评级\",\n" +
            "    \"isConfirm\":\"0-未确认，1-确认\",\n" +
            "    \"status\":\"0-未处理,1-已挂起,2-已处理,3-持续中\",\n" +
            "    \"roleIDs\": [\n" +
            "        \"384a8727-4a6f-4106-af3e-f30c0458c611\"\n" +
            "    ],\n" +
            "    \"startTime\":\"2023-12-01 08:00\",\n" +
            "    \"endTime\":\"2023-12-01 18:00\"\n" +
            "}")
    public AjaxJson findFalsePageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<WarningInfo> warningRulePage = warningInfoService.findFalsePageByMultiCondition(jsonObject);
            ajaxJson.setData(warningRulePage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }



    @PostMapping("/findPageByMultiConditionNew")
    @ApiOperation(value = "按多条件查找分页", notes = "{\n" +
            "    \"pageIndex\":1,\n" +
            "    \"pageSize\":10,\n" +
            "    \"moduleCode\":\"\",\n" +
            "    \"alertContent\":\"\",\n" +
            "    \"deviceName\":\"\",\n" +
            "    \"areaName\":\"\",\n" +
            "    \"warningType\":\"0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测\",\n" +
            "    \"alertLevel\":\"1-一级，2-二级，3-三级，0-未评级\",\n" +
            "    \"isConfirm\":\"0-未确认，1-确认\",\n" +
            "    \"status\":\"0-未处理,1-已挂起,2-已处理,3-持续中\",\n" +
            "    \"roleIDs\": [\n" +
            "        \"384a8727-4a6f-4106-af3e-f30c0458c611\"\n" +
            "    ],\n" +
            "    \"startTime\":\"2023-12-01 08:00\",\n" +
            "    \"endTime\":\"2023-12-01 18:00\"\n" +
            "}")
    public AjaxJson findPageByMultiConditionNew(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            PageVo<WarningInfoVo> warningRulePageVo = warningInfoService.findPageByMultiConditionNew(jsonObject);
            ajaxJson.setData(warningRulePageVo);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping(value = "/exportInfos")
    @ApiOperation(value = "数据查询导出（自定义）", notes = "{\n" +
            "    \"moduleCode\":\"\",\n" +
            "    \"alertContent\":\"\",\n" +
            "    \"deviceName\":\"\",\n" +
            "    \"areaName\":\"\",\n" +
            "    \"warningType\":\"0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测\",\n" +
            "    \"alertLevel\":\"1-一级，2-二级，3-三级，0-未评级\",\n" +
            "    \"isConfirm\":\"0-未确认，1-确认\",\n" +
            "    \"status\":\"0-未处理,1-已挂起,2-已处理,3-持续中\",\n" +
            "    \"roleIDs\": [\n" +
            "        \"384a8727-4a6f-4106-af3e-f30c0458c611\"\n" +
            "    ],\n" +
            "    \"startTime\":\"2023-12-01 08:00\",\n" +
            "    \"endTime\":\"2023-12-01 18:00\"\n" +
            "}", produces = "application/json")
    public AjaxJson exportInfos(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            warningInfoService.exportInfos(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }



    @PostMapping(value = "/exportFalseInfos")
    @ApiOperation(value = "误报信息导出）", notes = "{\n" +
            "    \"moduleCode\":\"\",\n" +
            "    \"alertContent\":\"\",\n" +
            "    \"deviceName\":\"\",\n" +
            "    \"areaName\":\"\",\n" +
            "    \"warningType\":\"0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测\",\n" +
            "    \"alertLevel\":\"1-一级，2-二级，3-三级，0-未评级\",\n" +
            "    \"isConfirm\":\"0-未确认，1-确认\",\n" +
            "    \"status\":\"0-未处理,1-已挂起,2-已处理,3-持续中\",\n" +
            "    \"roleIDs\": [\n" +
            "        \"384a8727-4a6f-4106-af3e-f30c0458c611\"\n" +
            "    ],\n" +
            "    \"startTime\":\"2023-12-01 08:00\",\n" +
            "    \"endTime\":\"2023-12-01 18:00\"\n" +
            "}", produces = "application/json")
    public AjaxJson exportFalseInfos(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            warningInfoService.exportFalseInfos(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }



    @PostMapping(value = "/saveFalseInfo")
    @ResponseBody
    @ApiOperation(value = "误报信息保存(确认误报之后调用)", notes = "{\n" +
            "  \"list\": [\n" +
            "    {\n" +
            "      \"alertAdvice\": \"误报\",\n" +
            "      \"alertContent\": \"2023-12-08 08:00:00-2023-12-08 16:00:00,1#铸机水耗,单耗：5.45m3/t,超指标：2.55m3/t\",\n" +
            "      \"alertLevel\": 3,\n" +
            "      \"alertTypeName\": \"超标报警\",\n" +
            "      \"createDateTime\": \"2023-12-08 16:02:25\",\n" +
            "      \"createUserNo\": \"009314\",\n" +
            "      \"dealUser\": \"一炼钢1#连铸岗位\",\n" +
            "      \"id\": \"1733034277441900546\",\n" +
            "      \"isConfirm\": 0,\n" +
            "      \"isFalse\": 0,\n" +
            "      \"moduleCode\": \"ems\",\n" +
            "      \"moduleName\": \"智慧能源\",\n" +
            "      \"msgID\": \"1733034277441900546\",\n" +
            "      \"remark\": \"\",\n" +
            "      \"status\": 2,\n" +
            "      \"updateDateTime\": \"2023-12-08 17:29:53\",\n" +
            "      \"updateUserNo\": \"342625199702081978\",\n" +
            "      \"alertAnalysis\": \"test\",\n" +
            "      \"falseFeedBack\": \"test\",\n" +
            "      \"isConfirmFalse\": 1\n" +
            "    }\n" +
            "  ]\n" +
            "}", produces = "application/json")
    public AjaxJson saveFalseInfo(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            warningInfoService.saveFalseInfo(jsonObject);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }





    @PostMapping(value = "/batchSaveInfos")
    @ResponseBody
    @ApiOperation(value = "报警规则保存", notes = "{\n" +
            "  \"list\": [\n" +
            "    {\n" +
            "      \"areaID\": \"1\",\n" +
            "      \"areaName\": \"1\",\n" +
            "      \"pointID\": \"1\",\n" +
            "      \"pointName\": \"1\",\n" +
            "      \"deviceID\": \"1\",\n" +
            "      \"deviceName\": \"1\",\n" +
            "      \"alertContent\": \"1\",\n" +
            "      \"alertAdvice\": \"1\",\n" +
            "      \"diagnosticMessage\": \"1\",\n" +
            "      \"moduleName\": \"1\",\n" +
            "      \"moduleCode\": \"1\",\n" +
            "      \"alertLevel\": \"1\",\n" +
            "      \"status\": \"1\",\n" +
            "      \"dealUser\": \"1\",\n" +
            "      \"humanAdvice\": \"1\",\n" +
            "      \"dealTime\": \"1\",\n" +
            "      \"isConfirm\": \"1\",\n" +
            "      \"alertAnalysis\": \"1\",\n" +
            "      \"alertTypeName\": \"1\"\n" +
            "      \"alertValue\": \"1\"\n" +
            "      \"warningType\":\"\",\n" +
            "      \"remark\": \"1\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"areaID\": \"1\",\n" +
            "      \"areaName\": \"1\",\n" +
            "      \"pointID\": \"1\",\n" +
            "      \"pointName\": \"1\",\n" +
            "      \"deviceID\": \"1\",\n" +
            "      \"deviceName\": \"1\",\n" +
            "      \"alertContent\": \"1\",\n" +
            "      \"alertAdvice\": \"1\",\n" +
            "      \"diagnosticMessage\": \"1\",\n" +
            "      \"moduleName\": \"1\",\n" +
            "      \"moduleCode\": \"1\",\n" +
            "      \"alertLevel\": \"1\",\n" +
            "      \"status\": \"1\",\n" +
            "      \"dealUser\": \"1\",\n" +
            "      \"humanAdvice\": \"1\",\n" +
            "      \"dealTime\": \"1\",\n" +
            "      \"isConfirm\": \"1\",\n" +
            "      \"alertAnalysis\": \"1\",\n" +
            "      \"alertTypeName\": \"1\"\n" +
            "      \"alertValue\": \"1\"\n" +
            "      \"warningType\":\"\",\n" +
            "      \"remark\": \"1\"\n" +
            "    },\n" +
            "  ]\n" +
            "}", produces = "application/json")
    public AjaxJson batchSaveInfos(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            warningInfoService.batchSaveInfos(jsonObject);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping("/findModuleInfoList")
    @ApiOperation(value = "获取模块列表", notes = "{}")
    public AjaxJson findModuleInfoList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningInfoService.findModuleInfoList());
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping("/findAreaNameList")
    @ApiOperation(value = "获取区域名称列表", notes = "{}")
    public AjaxJson findAreaNameList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningInfoService.findAreaNameList());
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping("/getDoneRateByRoleIDs")
    @ApiOperation(value = "按角色统计已处理未处理的报警", notes = "按角色统计已处理未处理的报警,{\n" +
            "    \"productionLineName\": [\n" +
            "        \"宽厚板厂\",\n" +
            "        \"第一炼钢厂\"\n" +
            "    ],\n" +
            "    \"roleIDs\": [\n" +
            "        \"ff4ea243-28c0-4e8c-a738-0c742530cd9f\"\n" +
            "    ],//产线选了，角色可不选，角色选了，产线可不选\n" +
            "    \"startTime\": \"2024-01-01 00:00:00\",\n" +
            "    \"endTime\": \"2024-01-06 00:00:00\",\n" +
            "    \"pageIndex\": 1,\n" +
            "    \"pageSize\": 10\n" +
            "}" , produces = "application/json")
    public AjaxJson getDoneRateByRoleIDs(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setData(warningInfoService.getDoneRateByRoleIDs(jsonObject));
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }



    @PostMapping("/getDoneRateGroupRoles")
    @ApiOperation(value = "按角色统计已处理未处理的报警", notes = "按角色统计已处理未处理的报警,{\n" +
            "    \"startTime\": \"2024-01-01 00:00:00\",\n" +
            "    \"endTime\": \"2024-01-06 00:00:00\"\n" +
            "}" , produces = "application/json")
    public AjaxJson getDoneRateGroupRoles(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setData(warningInfoService.getDoneRateGroupRoles(jsonObject));
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }



    @PostMapping(value = "/exportDoneRate")
    @ApiOperation(value = "导出按角色统计已处理未处理的报警", notes = "导出按角色统计已处理未处理的报警,{\n" +
            "    \"roleIDs\": [\n" +
            "        \"ff4ea243-28c0-4e8c-a738-0c742530cd9f\"\n" +
            "    ],\n" +
            "    \"startTime\": \"2023-11-01 08:00\",\n" +
            "    \"endTime\": \"2023-11-30 08:00\"\n" +
            "}" , produces = "application/json")
    public AjaxJson exportDoneRate(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            warningInfoService.exportDoneRate(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping("/exportAlertInfoByRoleIDs")
    @ApiOperation(value = "按角色统计已处理未处理的报警列表", notes = "{\n" +
            "    \"roleIDs\": [\n" +
            "        \"ff4ea243-28c0-4e8c-a738-0c742530cd9f\"\n" +
            "    ],\n" +
            "    \"startTime\": \"2023-11-01 08:00\",\n" +
            "    \"endTime\": \"2023-11-30 08:00\"\n" +
            "}" , produces = "application/json")
    public AjaxJson exportAlertInfoByRoleIDs(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            warningInfoService.exportAlertInfoByRoleIDs(jsonObject, response);
            ajaxJson.setData("导出成功");
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    @PostMapping(value = "/findRuleAlertCount")
    @ApiOperation(value = "报警规则分组统计", notes = "{\n" +
            "    \"pageIndex\": 1,\n" +
            "    \"pageSize\": 10,\n" +
            "    \"startTime\": \"2023-12-19 00:00:00\",\n" +
            "    \"endTime\": \"2023-12-20 23:59:59\"\n" +
            "}", produces = "application/json")
    public AjaxJson findRuleAlertCount(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningInfoService.findRuleAlertCount(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping(value = "/findHistoryByAlertID")
    @ApiOperation(value = "查询历史记录", notes = "{\n" +
            "    \"alertInfoID\": \"1739436434642702338\",\n" +
            "    \"alertAdvice\": \"合格率\"\n" +
            "}" , produces = "application/json")
    public AjaxJson findHistoryByAlertID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningInfoService.findHistoryByAlertID(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping(value = "/findHistoryByRoleAndTime")
    @ApiOperation(value = "查询已完成和未完成的历史记录", notes = "{\n" +
            "\t\"status\": 0, 0-未完成,1-已完成\n" +
            "\t\"startTime\": \"2024-03-19 08:00:00\",\n" +
            "\t\"endTime\": \"2024-03-20 08:00:00\",\n" +
            "\t\"roleID\": \"765e029b-f416-4b46-93be-8622363fd483\",\n" +
            "\t\"pageIndex\": 1,\n" +
            "\t\"pageSize\": 10\n" +
            "}" , produces = "application/json")
    public AjaxJson findHistoryByRoleAndTime(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningInfoService.findHistoryByRoleAndTime(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping(value = "/exportHistory")
    @ApiOperation(value = "历史记录导出", notes = "{\n" +
            "    \"alertInfoID\":\"0318f7e2-f148-4c06-94dd-4be3fae20df9\"\n" +
            "}" , produces = "application/json")
    public AjaxJson exportHistory(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            warningInfoService.exportHistory(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping(value = "/exportHistoryByWarningRuleID")
    @ApiOperation(value = "历史记录导出", notes = "{\n" +
            "    \"warningRuleID\":\"1699242746084069378\"\n" +
            "}" , produces = "application/json")
    public AjaxJson exportHistoryByWarningRuleID(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            warningInfoService.exportHistoryByWarningRuleID(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }



    @PostMapping(value = "/findHistoryByWaringEventID")
    @ApiOperation(value = "根据报警消息ID，查询历史处置记录", notes = "{\n" +
            "    \"warningEventID\": \"be29afd9-1e59-49f4-ba79-1671e5bf8ddc\"\n" +
            "}" , produces = "application/json")
    public AjaxJson findHistoryByWaringEventID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningInfoService.findHistoryByWaringEventID(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping(value = "/findHistoryByWarningRuleID")
    @ApiOperation(value = "根据报警消息ID，查询历史处置记录", notes = "{\n" +
            "\t\"startTime\": \"2024-02-20\",\n" +
            "\t\"endTime\": \"2024-02-29\",\n" +
            "\t\"warningRuleID\": \"b6b339fb80c445ef9656e0e7d11f2248\",\n" +
            "\t\"pageIndex\": 1,\n" +
            "\t\"pageSize\": 10\n" +
            "}" , produces = "application/json")
    public AjaxJson findHistoryByWarningRuleID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningInfoService.findHistoryByWarningRuleID(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping(value = "/findSummaryByWarningRuleID")
    @ApiOperation(value = "根据报警消息ID，查询历史处置记录", notes = "{\n" +
            "    \"warningRuleID\": \"999fb562479a4b1c83ce0c37b025e6ae\"\n" +
            "}" , produces = "application/json")
    public AjaxJson findSummaryByWarningRuleID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningInfoService.findSummaryByWarningRuleID(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping("/getDoneRateByModuleCodes")
    @ApiOperation(value = "按模块统计已处理未处理的报警", notes = "{\n" +
            "    \"moduleCodes\": [\n" +
            "        \"ems\",\n" +
            "        \"qms\"\n" +
            "    ],\n" +
            "    \"startTime\": \"2024-01-01 00:00:00\",\n" +
            "    \"endTime\": \"2024-01-06 00:00:00\"\n" +
            "}" , produces = "application/json")
    public AjaxJson getDoneRateByModuleCodes(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setData(warningInfoService.getDoneRateByModuleCodes(jsonObject));
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @PostMapping(value = "/exportDoneRateByModuleCodes")
    @ApiOperation(value = "导出按模块统计已处理未处理的报警", notes = "{\n" +
            "    \"moduleCodes\": [\n" +
            "        \"ems\",\n" +
            "        \"qms\"\n" +
            "    ],\n" +
            "    \"startTime\": \"2024-01-01 00:00:00\",\n" +
            "    \"endTime\": \"2024-01-06 00:00:00\"\n" +
            "}" , produces = "application/json")
    public AjaxJson exportDoneRateByModuleCodes(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            warningInfoService.exportDoneRateByModuleCodes(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportAlertInfoByModuleCodes")
    @ApiOperation(value = "按模块统计已处理未处理的报警列表", notes = "{\n" +
            "    \"moduleCodes\": [\n" +
            "        \"ems\",\n" +
            "        \"qms\"\n" +
            "    ],\n" +
            "    \"startTime\": \"2024-01-01 00:00:00\",\n" +
            "    \"endTime\": \"2024-01-06 00:00:00\"\n" +
            "}" , produces = "application/json")
    public AjaxJson exportAlertInfoByModuleCodes(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            warningInfoService.exportDoneRateByModuleCodes(jsonObject, response);
            ajaxJson.setData("导出成功");
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }



    @PostMapping("/findRateByRoleIDsWithTime")
    @ApiOperation(value = "角色统计曲线", notes = "{\n" +
            "    \"productionLineName\": [\n" +
            "        \"第一炼钢厂\"\n" +
            "    ],\n" +
            "    \"roleIDs\": [\n" +
            "        \"0f3ef77b-3ec7-4b95-8488-32d41d2e1994\",\n" +
            "        \"0f3ef77b-3ec7-4b95-8488-32d41d2e1995\",\n" +
            "        \"0f3ef77b-3ec7-4b95-8488-32d41d2e1996\"\n" +
            "    ],\n" +
            "    \"startTime\": \"2024-01-01 00:00:00\",\n" +
            "    \"endTime\": \"2024-01-20 00:00:00\",\n" +
            "    \"pageIndex\": 1,\n" +
            "    \"pageSize\": 10\n" +
            "}" , produces = "application/json")
    public AjaxJson findRateByRoleIDsWithTime(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setData(warningInfoService.findRateByRoleIDsWithTime(jsonObject));
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @PostMapping("/findRateByModuleCodesWithTime")
    @ApiOperation(value = "模块统计曲线", notes = "{\n" +
            "    \"moduleCodes\": [\n" +
            "        \"ems\",\n" +
            "        \"qms\"\n" +
            "    ],\n" +
            "    \"startTime\": \"2024-01-01 00:00:00\",\n" +
            "    \"endTime\": \"2024-01-20 00:00:00\",\n" +
            "    \"pageIndex\": 1,\n" +
            "    \"pageSize\": 10\n" +
            "}" , produces = "application/json")
    public AjaxJson findRateByModuleCodesWithTime(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setData(warningInfoService.findRateByModuleCodesWithTime(jsonObject));
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @PostMapping("/findIsFalseByModuleCodesWithTime")
    @ApiOperation(value = "模块误报曲线", notes = "{\n" +
            "    \"moduleCodes\": [\n" +
            "        \"ems\",\n" +
            "        \"qms\"\n" +
            "    ],\n" +
            "    \"startTime\": \"2024-01-01 00:00:00\",\n" +
            "    \"endTime\": \"2024-01-06 00:00:00\"\n" +
            "}" , produces = "application/json")
    public AjaxJson findIsFalseByModuleCodesWithTime(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setData(warningInfoService.findIsFalseByModuleCodesWithTime(jsonObject));
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }





    @PostMapping("/getUnDoneRateByRoleIDs")
    @ApiOperation(value = "按角色统计未处理的报警", notes = "按角色统计未处理的报警,{\n" +
            "    \"productionLineName\": [\n" +
            "        \"宽厚板厂\",\n" +
            "        \"第一炼钢厂\"\n" +
            "    ],\n" +
            "    \"roleIDs\": [\n" +
            "        \"ff4ea243-28c0-4e8c-a738-0c742530cd9f\"\n" +
            "    ],//产线选了，角色可不选，角色选了，产线可不选\n" +
            "    \"startTime\": \"2024-01-01 00:00:00\",\n" +
            "    \"endTime\": \"2024-01-06 00:00:00\",\n" +
            "    \"pageIndex\": 1,\n" +
            "    \"pageSize\": 10\n" +
            "}" , produces = "application/json")
    public AjaxJson getUnDoneRateByRoleIDs(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setData(warningInfoService.getUnDoneRateByRoleIDs(jsonObject));
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }



    @PostMapping("/getUnDoneRateGroupRoles")
    @ApiOperation(value = "按角色统计未处理的报警", notes = "按角色统计未处理的报警,{\n" +
            "    \"startTime\": \"2024-01-01 00:00:00\",\n" +
            "    \"endTime\": \"2024-01-06 00:00:00\"\n" +
            "}" , produces = "application/json")
    public AjaxJson getUnDoneRateGroupRoles(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setData(warningInfoService.getUnDoneRateGroupRoles(jsonObject));
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @PostMapping("/findFinishSummary")
    @ApiOperation(value = "完成情况百分比统计", notes = "按角色统计未处理的报警,{\n" +
            "    \"productionLineName\": [\n" +
            "        \"宽厚板厂\",\n" +
            "        \"第一炼钢厂\"\n" +
            "    ],\n" +
            "    \"roleIDs\": [\n" +
            "        \"ff4ea243-28c0-4e8c-a738-0c742530cd9f\"\n" +
            "    ],//产线选了，角色可不选，角色选了，产线可不选\n" +
            "    \"startTime\": \"2024-01-01 00:00:00\",\n" +
            "    \"endTime\": \"2024-01-06 00:00:00\"\n" +
            "}" , produces = "application/json")
    public AjaxJson findFinishSummary(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setData(warningInfoService.findFinishSummary(jsonObject));
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

}
