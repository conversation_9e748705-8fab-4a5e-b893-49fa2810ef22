package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.Dictionary;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 数据字典Service
 *
 * <AUTHOR>
 * @create 2023-02-09
 */
public interface DictionaryService {

    /**
     * 查找所有内容
     *
     * @return {@link List }<{@link Dictionary }>
     * <AUTHOR>
     * @create 2023-02-09
     */
    List<Dictionary> findAll();

    /**
     * 按多条件查找分页
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link Dictionary }>
     * <AUTHOR>
     * @create 2023-02-09
     */
    Page<Dictionary> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param dictionary 词典
     * @return {@link Dictionary }
     * <AUTHOR>
     * @create 2023-02-09
     */
    Dictionary save(Dictionary dictionary);

    /**
     * 删除
     *
     * @param dictionary 词典
     * <AUTHOR>
     * @create 2023-02-09
     */
    void delete(Dictionary dictionary) throws Exception;
}
