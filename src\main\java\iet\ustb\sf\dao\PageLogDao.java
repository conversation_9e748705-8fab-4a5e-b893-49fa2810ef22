package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.PageLog;
import iet.ustb.sf.vo.eo.AccessNumEO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface PageLogDao extends JpaSpecificationExecutor<PageLog>, JpaRepository<PageLog, String> {

    /**
     * 查找组织使用人数和访问次数
     *
     * @param loginTimeStart 登录时间开始
     * @param loginTimeEnd   登录时间结束
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-08
     */
    @Query(value = "select new iet.ustb.sf.vo.eo.AccessNumEO"
            + "(a.orgCode, b.orgAllName, a.level, count(distinct a.userNo) , count(a))"
            + " from PageLog a"
            + "          left join Org b on a.orgCode = b.orgCode"
            + " where a.loginTime >= ?1"
            + "   and a.loginTime <= ?2"
            + "   and a.oneOrgCode <> 'X'"
            + "   and b.status = '0' "
            + "   and b.operStus <> 'D'"
            + " group by a.orgCode"
            + " order by count(a) desc")
    List<AccessNumEO> findGroupOrgByLoginTime(Date loginTimeStart, Date loginTimeEnd);

    /**
     * 查询用户访问次数
     *
     * @param loginTime    登录时间
     * @param loginTimeEnd
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-03
     */
    @Query(value = "select new iet.ustb.sf.vo.eo.AccessNumEO(a.userNo, b.userName, e.postName, count(a), c.orgAllName)"
            + " from PageLog a"
            + "          left join User b on a.userNo = b.userNo"
            + "          left join Org c on a.oneOrgCode = c.orgCode"
            + "          left join UserPost d on b.id = d.userId"
            + "          left join Post e on d.postCode = e.postCode"
            + " where a.loginTime >= ?1 "
            + "   and a.loginTime <= ?2 "
            + "   and b.status = '0' "
            + "   and c.status = '0' "
            + "   and d.status = '0' "
            + "   and e.status = '0' "
            + "   and b.operStus <> 'D' "
            + "   and c.operStus <> 'D' "
            + "   and d.operStus <> 'D' "
            + "   and e.operStus <> 'D' "
            + " group by a.userNo"
            + " order by count(a) desc")
    List<AccessNumEO> findGroupUserByLoginTime(Date loginTime, Date loginTimeEnd);

    /**
     * 查询应用访问次数
     *
     * @param loginTime    登录时间
     * @param loginTimeEnd
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-03
     */
    @Query(value = "select new iet.ustb.sf.vo.eo.AccessNumEO(a.resourceId, count(a))"
            + " from PageLog a"
            + " where a.loginTime >= ?1 "
            + "   and a.loginTime <= ?2 "
            + " group by a.resourceId"
            + " order by count(a) desc")
    List<AccessNumEO> findGroupAppByLoginTime(Date loginTime, Date loginTimeEnd);


    /**
     * 汇总所有使用人数和访问次数
     *
     * @param loginTimeStart 登录时间开始
     * @param loginTimeEnd   登录时间结束
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-08
     */
    @Query(value = "select new iet.ustb.sf.vo.eo.AccessNumEO"
            + "(count(distinct a.userNo) , count(a))"
            + " from PageLog a"
            + "          left join Org b on a.orgCode = b.orgCode"
            + " where a.loginTime >= ?1"
            + "   and a.loginTime <= ?2"
            + "   and a.oneOrgCode <> 'X'"
            + "   and b.status = '0' "
            + "   and b.operStus <> 'D'")
    List<AccessNumEO> findSummaryNumByLoginTime(Date loginTimeStart, Date loginTimeEnd);

    /**
     * 查找板材事业部所有组织
     *
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-08
     */
//    @Query("from PageLog ")
//    List<Map<String, Object>> findOrgUsers(String loginTimeStart, String loginTimeEnd);

    /**
     * 查找板材事业部所有组织 //综合处驾驶班过滤掉
     *
     * @return {@link List }<{@link AccessNumEO}>
     * <AUTHOR>
     * @create 2022-11-08
     */
    @Query(value = "select t.orgLevel,"
            + "       t.orgCode                                                                                     code,"
            + "       t.orgAllName                                                                                  name,"
            + "       t.parentOrgCode                                                                               parentCode,"
            + "       (select count(1) from du_user where orgcode = t.orgcode and operstus <> 'D' and status = '0') orgNum,"
            + "       (select count(1) from du_user where orgcode = t.orgcode and operstus <> 'D' and status = '0' "
            + "             and t.orgallname not like '%甲班' and t.orgallname not like '%乙班' and t.orgallname not like '%丙班' "
            + "             and t.orgallname not like '%丁班' and t.parentOrgCode <> 'X66040000') shouldUseNum,"
            + "       useNum,"
            + "       ifnull(sum(t.accessNum), 0)                                                                   num"
            + " from (select a.orgLevel, a.orgcode, a.orgAllName, a.parentOrgCode, count(b.userno) useNum, sum(num) accessNum"
            + "      from du_org a"
            + "               left join (select orgcode, userno, count(1) num"
            + "                          from page_log"
            + "                          where logintime >= ?1"
            + "                            and logintime <= ?2"
            + "                          group by orgcode, userno) b on a.orgcode = b.orgcode"
            + "      where a.orgAllName like '板材事业部%'"
            + "        and a.orgCode <> 'X50010101'"
            + "        and a.operstus <> 'D'"
            + "      group by a.orgcode, a.orgAllName, a.orgLevel, a.parentOrgCode) t"
            + " group by t.orgLevel, t.orgcode, t.orgAllName, t.parentOrgCode"
            + " order by t.orgLevel, t.orgcode", nativeQuery = true)
    List<Map<String, Object>> findOrgUsers(String startTime, String endTime);

    /**
     * 查找上次登录时间
     *
     * @param userNo     用户编号
     * @param resourceId 资源id
     * @return {@link Date }
     * <AUTHOR>
     * @create 2023-03-22
     */
    @Query(value = "select loginTime from page_Log where userNo = ?1 and resourceId = ?2 order by loginTime desc limit 1", nativeQuery = true)
    Date findLastLoginTime(String userNo, String resourceId);


    @Query(value = "select new iet.ustb.sf.vo.eo.AccessNumEO(a.userNo, b.userName, e.postName, count(a), c.orgAllName)"
            + " from PageLog a"
            + "          left join User b on a.userNo = b.userNo"
            + "          left join Org c on a.orgCode = c.orgCode"
            + "          left join UserPost d on b.id = d.userId"
            + "          left join Post e on d.postCode = e.postCode"
            + " where a.loginTime >= :loginTimeStart "
            + "   and a.loginTime <= :loginTimeEnd "
            + "   and b.userNo = :userNo "
            + "   and c.orgCode = :orgCode "
            + "   and b.status = '0' "
            + "   and b.operStus <> 'D' "
            + "   and c.operStus <> 'D' "
            + "   and d.operStus <> 'D' "
            + "   and e.operStus <> 'D' "
            + " group by a.userNo"
            + " order by count(a) desc")
    List<AccessNumEO> findUserPageAccessData(Date loginTimeStart, Date loginTimeEnd, String userNo, String orgCode);

    /**
     * 按模块查找页面访问次数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-03-27
     */
    @Query(value = "select b.serviceName code, count(1) num"
            + " from page_log a"
            + "          left join ds_resource b on a.resourceId = b.id"
            + " where a.loginTime >= ?1"
            + "   and a.loginTime <= ?2"
            + "   and b.type = 'menu'"
            + "   and b.status = 1"
            + " group by b.serviceName"
            + " order by count(1) desc", nativeQuery = true)
    List<Map<String, Object>> findPageAccessNumByService(String startTime, String endTime);


    /**
     * 按模块查找每个页面访问次数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-03-27
     */
    @Query(value = "select b.serviceName code, c.name parentName, b.name, count(1) num"
            + " from page_log a"
            + "         left join ds_resource b on a.resourceId = b.id"
            + "         left join ds_resource c on b.parentId = c.id"
            + " where a.loginTime >= ?1"
            + "   and a.loginTime <= ?2"
            + "  and b.type = 'menu'"
            + "  and b.status = 1"
            + " group by b.serviceName, b.name, c.name"
            + " order by count(1) desc", nativeQuery = true)
    List<Map<String, Object>> findEachPageAccessNumByService(String startTime, String endTime);

    /**
     * 查找每个页面访问列表
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param resourceId 资源id
     * @param serviceNo  服务编号
     * @param pageName   页面名称
     * @param pageable   可分页
     * @return {@link Page }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-03-27
     */
    @Query(value = "select a.id resourceId, a.serviceName code, b.name parentName, a.name, IFNULL(c.num,0) num"
            + " from ds_resource a"
            + "         left join ds_resource b on a.parentId = b.id"
            + "         left join (select resourceId, count(1) num"
            + "                    from page_log"
            + "                    where 1 = 1"
            + "                      and if(?1 is not null && ?1 != '', loginTime >= ?1, 1 = 1)"
            + "                      and if(?2 is not null && ?2 != '', loginTime <= ?2, 1 = 1)"
            + "                    group by resourceId) c"
            + "                   on a.id = c.resourceId"
            + " where a.status = 1"
            + "  and a.isshow = 1"
            + "  and a.type = 'menu'"
            + "  and (b.isshow is null or b.isshow = 1)"
            + "  and if(?3 is not null && ?3 != '', a.id = ?3, 1 = 1)"
            + "  and if(?4 is not null && ?4 != '', a.serviceName = ?4, 1 = 1)"
            + "  and if(?5 is not null && ?5 != '', a.name like concat('%', ?5, '%'), 1 = 1)"
            + " order by c.num desc",
            countProjection = "b.name", nativeQuery = true)
    Page<Map<String, Object>> findEachPageAccessList(String startTime, String endTime,
        String resourceId, String serviceNo, String pageName, Pageable pageable);

    /**
     * 按组织查找页面负责人访问次数
     *
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @param handleUserNoSet 所有负责业务员
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-03-27
     */
    @Query(value = "select a.userNo,"
            + "        c.userName,"
            + "        count(1) accessNum"
            + " from page_log a"
            + "          left join ds_resource b on a.resourceId = b.id"
            + "          left join du_user c on a.userNo = c.userNo"
            + " where a.loginTime >= ?1"
            + "   and a.loginTime <= ?2"
            + "   and a.userNo in ?3"
            + "   and b.type = 'menu'"
            + "   and c.operStus <> 'D'"
            + " group by a.userNo, c.userName"
            + " order by count(1) desc", nativeQuery = true)
    List<Map<String, Object>> findHandleUserAccessNumByOrg(String startTime, String endTime, Set<String> handleUserNoSet);

    @Query(value = "select a.userNo,"
            + "        c.userName,"
            + "        count(1) accessNum"
            + " from page_log a"
            + "          left join ds_resource b on a.resourceId = b.id"
            + "          left join du_user c on a.userNo = c.userNo"
            + " where a.loginTime >= ?1"
            + "   and a.loginTime <= ?2"
            + "   and b.type = 'menu'"
            + "   and c.operStus <> 'D'"
            + " group by a.userNo, c.userName"
            + " order by count(1) desc", nativeQuery = true)
    List<Map<String, Object>> findHandleUserAccessNumByOrg(String startTime, String endTime);


    /**
     * 按组织查找用户访问次数
     *
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param orgCodeList 组织代码列表
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-03-27
     */
    @Query(value = "select b.userNo code, b.userName name, count(1) num"
            + " from page_log a"
            + "         left join du_user b on a.userNo = b.userNo"
            + "         left join du_org c on b.orgCode = c.orgCode"
            + "         left join ds_resource d on a.resourceId = d.id"
            + " where a.loginTime >= ?1"
            + "   and a.loginTime <= ?2"
            + "  and c.orgCode in ?3"
            + "  and b.operStus <> 'D'"
            + "  and c.operStus <> 'D'"
            + "  and d.type = 'menu'"
            + " group by a.userNo"
            + " order by count(1) desc", nativeQuery = true)
    List<Map<String, Object>> findUserAccessNumByOrg(String startTime, String endTime, Set<String> orgCodeList);

    /**
     * 查找用户页面访问量列表
     *
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @param userNo          用户编号
     * @param allOrgSet       所有组织集合
     * @param leaderUserNoSet 包含的领导
     * @param noLeaderUserNoSet 不包含领导编号
     * @param pageable        可分页
     * @return {@link Page }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-04-07
     */
    @Query(value = "select b.userNo, b.userName, c.orgCode, c.orgAllName, coalesce(a.num, 0) accessNum"
            + " from (select m.userNo, count(1) num"
            + "      from page_log m"
            + "               inner join ds_resource n on m.resourceId = n.id"
            + "      where if(?1 is not null && ?1 != '', m.loginTime >= ?1, 1 = 1)"
            + "        and if(?2 is not null && ?2 != '', m.loginTime <= ?2, 1 = 1)"
            + "        and n.type = 'menu'"
            + "        and n.status = 1"
            + "      group by userNo) a"
            + "         right join du_user b on a.userNo = b.userNo"
            + "         inner join du_org c on b.orgCode = c.orgCode"
            + " where 1 = 1"
            + "   and if(?3 is not null && ?3 != '', (b.userNo = ?3 or b.userName = ?3), 1 = 1)"
            + "   and (coalesce(?4, null) is null or b.orgCode in (?4))"
            + "   and (coalesce(?5, null) is null or b.userNo in (?5))"
            + "   and (coalesce(?6, null) is null or b.userNo not in (?6))"
            + "   and b.status = '0'"
            + "   and c.status = '0'"
            + "   and b.operStus <> 'D'"
            + "   and c.operStus <> 'D'"
            + " group by b.userNo, b.userName, c.orgCode, c.orgAllName"
            + " order by field(b.userNo, ?5)",
            countProjection = "b.userNo", nativeQuery = true)
    Page<Map<String, Object>> findUserPageAccessList(String startTime, String endTime,
                                                     String userNo, Set<String> allOrgSet, Set<String> leaderUserNoSet, Set<String> noLeaderUserNoSet, Pageable pageable);

    @Query(value = "select b.userNo, b.userName, c.orgCode, c.orgAllName, coalesce(a.num, 0) accessNum"
            + " from (select m.userNo, count(1) num"
            + "      from page_log m"
            + "               inner join ds_resource n on m.resourceId = n.id"
            + "      where if(?1 is not null && ?1 != '', m.loginTime >= ?1, 1 = 1)"
            + "        and if(?2 is not null && ?2 != '', m.loginTime <= ?2, 1 = 1)"
            + "        and n.type = 'menu'"
            + "        and n.status = 1"
            + "      group by userNo) a"
            + "         right join du_user b on a.userNo = b.userNo"
            + "         inner join du_org c on b.orgCode = c.orgCode"
            + " where 1 = 1"
            + "   and if(?3 is not null && ?3 != '', (b.userNo = ?3 or b.userName = ?3), 1 = 1)"
            + "   and (coalesce(?4, null) is null or b.orgCode in (?4))"
            + "   and (coalesce(?5, null) is null or b.userNo in (?5))"
            + "   and (coalesce(?6, null) is null or b.userNo not in (?6))"
            + "   and b.status = '0'"
            + "   and c.status = '0'"
            + "   and b.operStus <> 'D'"
            + "   and c.operStus <> 'D'"
            + " group by b.userNo, b.userName, c.orgCode, c.orgAllName"
            + " order by a.num desc",
            nativeQuery = true)
    List<Map<String, Object>> findUserPageAccessList2(String startTime, String endTime,
                                                     String userNo, Set<String> allOrgSet, Set<String> leaderUserNoSet, Set<String> noLeaderUserNoSet);

    @Query(value = "select b.userNo, b.userName, c.orgCode, c.orgAllName, coalesce(a.num, 0) accessNum"
            + " from (select m.userNo, count(1) num"
            + "      from page_log m"
            + "               inner join ds_resource n on m.resourceId = n.id"
            + "      where if(?1 is not null && ?1 != '', m.loginTime >= ?1, 1 = 1)"
            + "        and if(?2 is not null && ?2 != '', m.loginTime <= ?2, 1 = 1)"
            + "        and n.type = 'menu'"
            + "        and n.status = 1"
            + "      group by userNo) a"
            + "         right join du_user b on a.userNo = b.userNo"
            + "         inner join du_org c on b.orgCode = c.orgCode"
            + " where 1 = 1"
            + "   and if(?3 is not null && ?3 != '', (b.userNo = ?3 or b.userName = ?3), 1 = 1)"
            + "   and (coalesce(?4, null) is null or b.orgCode in (?4))"
            + "   and (coalesce(?5, null) is null or b.userNo in (?5))"
            + "   and (coalesce(?6, null) is null or b.userNo not in (?6))"
            + "   and b.status = '0'"
            + "   and c.status = '0'"
            + "   and b.operStus <> 'D'"
            + "   and c.operStus <> 'D'"
            + " group by b.userNo, b.userName, c.orgCode, c.orgAllName",
            countProjection = "b.userNo", nativeQuery = true)
    Page<Map<String, Object>> findSortUserPageAccessList(String startTime, String endTime,
                                                     String userNo, Set<String> allOrgSet, Set<String> leaderUserNoSet, Set<String> noLeaderUserNoSet, Pageable pageable);

    /**
     * 查找用户访问次数排行
     *
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @param userNoSet       用户集合
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-12-04
     */
    @Query(value = "select b.userNo, b.userName, c.orgCode, c.orgAllName, coalesce(a.num, 0) accessNum"
            + " from (select m.userNo, count(1) num"
            + "      from page_log m"
            + "               inner join ds_resource n on m.resourceId = n.id"
            + "      where if(?1 is not null && ?1 != '', m.loginTime >= ?1, 1 = 1)"
            + "        and if(?2 is not null && ?2 != '', m.loginTime <= ?2, 1 = 1)"
            + "        and n.type = 'menu'"
            + "        and n.status = 1"
            + "      group by userNo) a"
            + "         right join du_user b on a.userNo = b.userNo"
            + "         inner join du_org c on b.orgCode = c.orgCode"
            + " where 1 = 1"
            + "   and (coalesce(?3, null) is null or b.userNo in (?3))"
            + "   and b.status = '0'"
            + "   and c.status = '0'"
            + "   and b.operStus <> 'D'"
            + "   and c.operStus <> 'D'"
            + " group by b.userNo, b.userName, c.orgCode, c.orgAllName"
            + " order by coalesce(a.num, 0) desc", nativeQuery = true)
    List<Map<String, Object>> findUserAccessRankList(String startTime, String endTime,
                                                     Set<String> userNoSet);

    /**
     * 查找包含宽厚板厂未访问过的用户页面访问数据
     * @return
     */
    @Query(value = "(select a.userNo, b.userName, c.orgCode, c.orgAllName, count(1) accessNum"
            + "  from page_log a"
            + "           right join du_user b on a.userNo = b.userNo"
            + "           inner join du_org c on b.orgCode = c.orgCode"
            + "           inner join ds_resource f on a.resourceId = f.id"
            + "  where 1 = 1"
            + "    and if(?1 is not null && ?1 != '', a.loginTime >= ?1, 1 = 1)"
            + "    and if(?2 is not null && ?2 != '', a.loginTime <= ?2, 1 = 1)"
            + "    and if(?3 is not null && ?3 != '', (a.userNo = ?3 or b.userName = ?3), 1 = 1)"
            + "    and (coalesce(?4, null) is null or b.orgCode in (?4))"
            + "    and (coalesce(?5, null) is null or a.userNo in (?5))"
            + "    and (coalesce(?6, null) is null or a.userNo not in (?6))"
            + "    and b.status = '0'"
            + "    and b.operStus <> 'D'"
            + "    and c.operStus <> 'D'"
            + "    and f.type = 'menu'"
            + "  group by a.userNo, b.userName, c.orgCode, c.orgAllName"
            + "  order by count(1) desc)"
            + " union"
            + " (select a.userNo, a.userName, b.orgCode, b.orgAllName, 0 accessNum"
            + "  from du_user a"
            + "           inner join du_org b on a.orgCode = b.orgCode"
            + "  where a.orgcode like 'X38%'"
            + "    and a.status = '0'"
            + "    and a.operStus <> 'D'"
            + "    and a.userno "
            + "         not in (select a.userNo"
            + "           from page_log a"
            + "                    right join du_user b on a.userNo = b.userNo"
            + "                    inner join du_org c on b.orgCode = c.orgCode"
            + "                    inner join ds_resource f on a.resourceId = f.id"
            + "           where 1 = 1"
            + "             and if(?1 is not null && ?1 != '', a.loginTime >= ?1, 1 = 1)"
            + "             and if(?2 is not null && ?2 != '', a.loginTime <= ?2, 1 = 1)"
            + "             and if(?3 is not null && ?3 != '', (a.userNo = ?3 or b.userName = ?3), 1 = 1)"
            + "             and (coalesce(?4, null) is null or b.orgCode in (?4))"
            + "             and (coalesce(?5, null) is null or a.userNo in (?5))"
            + "             and (coalesce(?6, null) is null or a.userNo not in (?6))"
            + "             and b.status = '0'"
            + "             and c.status = '0'"
            + "             and b.operStus <> 'D'"
            + "             and c.operStus <> 'D'"
            + "             and f.type = 'menu'"
            + "             and f.status = 1"
            + "           group by a.userNo, b.userName, c.orgCode, c.orgAllName"
            + "           order by count(1) desc)"
            + "    and a.operstus <> 'D')"
            + " order by accessNum desc,orgCode,userNo", countProjection = "userNo", nativeQuery = true)
    Page<Map<String, Object>> findAllUserPageAccessList(String startTime, String endTime,
        String userNo, Set<String> allOrgSet, Set<String> leaderUserNoSet, Set<String> noLeaderUserNoSet, Pageable pageable);

    /**
     * 按用户编号查找页面访问列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param userNo    用户编号
     * @param pageable  可分页
     * @return {@link Page }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-03-27
     */
    @Query(value = "select a.userNo, "
            + "     DATE_FORMAT(a.loginTime,'%Y-%m-%d %H:%i:%s') loginTime, "
            + "     b.serviceName serviceNo, "
            + "     c.name parentName, "
            + "     b.name pageName"
            + " from page_log a"
            + "          left join ds_resource b on a.resourceId = b.id"
            + "          left join ds_resource c on b.parentId = c.id"
            + " where 1 = 1"
            + "   and if(?1 is not null && ?1 != '', a.loginTime >= ?1, 1=1)"
            + "   and if(?2 is not null && ?2 != '', a.loginTime <= ?2, 1=1)"
            + "   and a.userNo = ?3"
            + "   and b.type = 'menu'"
//            + "   and c.type = 'menu'"
            + " order by a.loginTime desc",
            countProjection = "a.userNo", nativeQuery = true)
    Page<Map<String, Object>> findPageAccessListByUserNo(String startTime, String endTime,
        String userNo, Pageable pageable);

    @Query(value = "select d.userNo, d.userName, b.serviceName, c.name parentName, b.name, count(1) accessNum, e.totNum"
            + " from du_user d"
            + "         left join page_log a on a.userNo = d.userNo"
            + "         left join ds_resource b on a.resourceId = b.id"
            + "         left join ds_resource c on b.parentId = c.id"
            + "         left join (select d.userNo, count(1) totNum"
            + "              from du_user d"
            + "                       left join page_log a on a.userNo = d.userNo"
            + "                       left join ds_resource b on a.resourceId = b.id"
            + "              where 1 = 1"
            + "                and if(?1 is not null && ?1 != '', a.loginTime >= ?1, 1=1)"
            + "                and if(?2 is not null && ?2 != '', a.loginTime <= ?2, 1=1)"
            + "                and if(?3 is not null && ?3 != '', (d.userNo = ?3 or d.userName = ?3), 1=1)"
            + "                and (coalesce(?4,null) is null or d.orgCode in (?4))"
            + "                and (coalesce(?5,null) is null or d.userNo in (?5))"
            + "                and b.status = 1"
            + "                and b.type = 'menu'"
            + "                and d.status = '0'"
            + "                and d.operStus <> 'D'"
            + "              group by d.userNo) e on a.userNo = e.userNo"
            + " where 1 = 1"
            + "   and if(?1 is not null && ?1 != '', a.loginTime >= ?1, 1=1)"
            + "   and if(?2 is not null && ?2 != '', a.loginTime <= ?2, 1=1)"
            + "   and if(?3 is not null && ?3 != '', (d.userNo = ?3 or d.userName = ?3), 1=1)"
            + "   and (coalesce(?4,null) is null or d.orgCode in (?4))"
            + "   and (coalesce(?5,null) is null or d.userNo in (?5))"
            + "   and (coalesce(?6,null) is null or d.userNo not in (?6))"
            + "   and b.status = 1"
            + "   and c.status = 1"
            + "   and b.type = 'menu'"
            + "   and c.type = 'menu'"
            + "   and d.status = '0'"
            + "   and d.operStus <> 'D'"
            + " group by d.userNo, d.userName, b.serviceName, b.name, c.name"
            + " order by e.totNum desc, d.userNo", nativeQuery = true)
    List<Map<String, Object>> findUserDetPageAccessList(String startTime, String endTime,
        String userNo, Set<String> allOrgSet, Set<String> leaderUserNoSet, Set<String> noLeaderUserNoSet);

    /**
     * 按资源id查找用户页面访问列表
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param resourceId 资源id
     * @param allOrgSet
     * @param pageable   可分页
     * @return {@link Page }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-03-27
     */
    @Query(value = "select a.userNo, b.userName, c.orgCode, c.orgAllName, DATE_FORMAT(a.loginTime,'%Y-%m-%d %H:%i:%s') loginTime"
            + " from page_log a"
            + "          inner join du_user b on a.userNo = b.userNo"
            + "          inner join du_org c on b.orgCode = c.orgCode"
            + "          inner join ds_resource f on a.resourceId = f.id"
            + " where 1 = 1"
            + "   and if(?1 is not null && ?1 != '', a.loginTime >= ?1, 1=1)"
            + "   and if(?2 is not null && ?2 != '', a.loginTime <= ?2, 1=1)"
            + "   and a.resourceId = ?3"
            + "   and (coalesce(?4, null) is null or c.orgCode in (?4))"
            + "   and b.status = '0'"
            + "   and b.operStus <> 'D'"
            + "   and c.operStus <> 'D'"
            + "   and f.type = 'menu'"
            + " order by loginTime desc",
            countProjection = "a.userNo", nativeQuery = true)
    Page<Map<String, Object>> findUserPageAccessListByResourceId(String startTime, String endTime, String resourceId, Set<String> allOrgSet, Pageable pageable);

    @Query(value = "select a.id,a.serviceName,b.name parentName, a.name, IFNULL(c.num, 0) num"
            + " from ds_resource a"
            + "         left join ds_resource b on a.parentId = b.id"
            + "         left join ds_resource c on b.parentId = c.id"
            + "         left join ds_resource d on c.parentId = d.id"
            + "         left join ds_resource e on d.parentId = e.id"
            + "         left join ds_resource f on e.parentId = f.id"
            + "         left join (select distinct parentid, type from ds_resource) g on g.parentId = a.id"
            + "         left join (select resourceId, count(1) num"
            + "                    from page_log"
            + "                    where userNo = ?1"
            + "                      and if(?2 is not null && ?2 != '', loginTime >= ?2, 1 = 1)"
            + "                      and if(?3 is not null && ?3 != '', loginTime <= ?3, 1 = 1)"
            + "                    group by resourceId) c on a.id = c.resourceId"
            + " where a.type = 'menu'"
            + "  and (g.parentid is null or g.type = 'button')"
            + "  and a.isshow = 1"
            + "  and (b.isshow is null or b.isshow = 1)"
            + "  and (c.isshow is null or c.isshow = 1)"
            + "  and (d.isshow is null or d.isshow = 1)"
            + "  and (e.isshow is null or e.isshow = 1)"
            + "  and (f.isshow is null or f.isshow = 1)"
            + "  and a.status = 1"
            + "  and (b.status is null or b.status = 1)"
            + "  and (c.status is null or c.status = 1)"
            + "  and (d.status is null or d.status = 1)"
            + "  and (e.isshow is null or e.status = 1)"
            + "  and (f.status is null or f.status = 1)"
            + "  and a.handleUser like concat('%',?1,'%')"
            + " order by num desc", countProjection = "a.id", nativeQuery = true)
    Page<Map<String, Object>> findOwnerResources(String userNo, String startTime, String endTime, Pageable pageable);


    /**
     * 查找领导者访问次数
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param userNoList 用户无列表
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-08-22
     */
    @Query(value = "SELECT"
            + "    userNo,"
            + "    userName,"
            + "    MAX(CASE WHEN week = 1 THEN accessNum else 0 END) AS firstWeek,"
            + "    MAX(CASE WHEN week = 2 THEN accessNum else 0 END) AS secondWeek,"
            + "    MAX(CASE WHEN week = 3 THEN accessNum else 0 END) AS thirdWeek,"
            + "    MAX(CASE WHEN week = 4 THEN accessNum else 0 END) AS fourthWeek,"
            + "    MAX(CASE WHEN week = 5 THEN accessNum else 0 END) AS fifthWeek,"
            + "    SUM(accessNum) totalNum"
            + " FROM"
            + "    ("
            + "        SELECT"
            + "            p.userNo,"
            + "            u.userName,"
            + "            FLOOR((DAYOFMONTH(p.loginTime) - 1) / 7) + 1 AS week,"
            + "            COUNT(*) AS accessNum"
            + "        FROM"
            + "            page_log p inner join du_user u on p.userNo = u.userNo"
            + "                       inner join ds_resource r on p.resourceId = r.id"
            + "        WHERE 1 = 1"
            + "            and if(?1 is not null && ?1 != '', p.loginTime >= ?1, 1=1)"
            + "            and if(?2 is not null && ?2 != '', p.loginTime <= ?2, 1=1)"
            + "            and p.userNo in ?3"
            + "            and u.status = 0"
            + "            and u.operstus <> 'D'"
            + "            and r.status = 1"
            + "            and r.type = 'menu'"
            + "        GROUP BY"
            + "            p.userNo, u.userName, week"
            + "    ) AS 统计结果"
            + " GROUP BY"
            + "    userNo, userName"
            + " order by FIELD(userNo, ?3)", nativeQuery = true)
    List<Map<String, Object>> findLeaderAccessNum(String startTime, String endTime, List<String> userNoList);


    /**
     * 查找C/E层人均访问次数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param levelName 级别名称
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-08-22
     */
    @Query(value = "select case"
            + "           when substr(a.orgcode, 1, 4) = 'X500' then substr(a.orgcode, 1, 5)"
            + "           else substr(a.orgcode, 1, 3) end                  as subOrgCode,"
            + "       ifnull(round(avg(e.accessNum)),0) as accessNum"
            + " from du_user a"
            + "         left join du_user_post b on a.id = b.userid"
            + "         left join du_post c on b.postcode = c.postcode"
            + "         left join du_org d on a.orgcode = d.orgcode"
            + "         left join (select p.userno, count(1) accessNum"
            + "                    from page_log p inner join ds_resource r on p.resourceid  = r.id"
            + "                    where if(?1 is not null && ?1 != '', p.loginTime >= ?1, 1 = 1)"
            + "                      and if(?2 is not null && ?2 != '', p.loginTime <= ?2, 1 = 1)"
            + "                      and r.type = 'menu'"
            + "                    group by p.userNo) e on a.userno = e.userno"
            + " where 1 = 1"
            + "  and b.ismainpost = 'Y'"
            + "  and c.postname like %?3%"
            + "  and d.orgallname like '%板材事业部%'"
            + "  and a.status = 0"
            + "  and b.status = 0"
            + "  and c.status = 0"
            + "  and d.status = 0"
            + "  and a.operstus <> 'D'"
            + "  and b.operstus <> 'D'"
            + "  and c.operstus <> 'D'"
            + "  and d.operstus <> 'D'"
            + " group by subOrgCode"
            + " order by subOrgCode", nativeQuery = true)
    List<Map<String, Object>> findLevelAccessNum(String startTime, String endTime, String levelName);

    /**
     * 查找C/E层用户编号
     *
     * @param levelName 级别名称
     * @return {@link Set }<{@link String }>
     * <AUTHOR>
     * @create 2023-09-07
     */
    @Query(value = "select a.userNo"
            + " from du_user a"
            + "         left join du_user_post b on a.id = b.userId"
            + "         left join du_post c on b.postCode = c.postCode"
            + "         left join du_org d on a.orgCode = d.orgCode"
            + " where 1 = 1"
            + "  and b.isMainPost = 'Y'"
            + "  and c.postName like %?1%"
            + "  and d.orgAllName like '%板材事业部%'"
            + "  and a.status = 0"
            + "  and b.status = 0"
            + "  and c.status = 0"
            + "  and d.status = 0"
            + "  and a.operstus <> 'D'"
            + "  and b.operstus <> 'D'"
            + "  and c.operstus <> 'D'"
            + "  and d.operstus <> 'D'"
            + " order by a.userNo", nativeQuery = true)
    Set<String> findLevelUserNo(String levelName);

    /**
     * 查找C/E层人员访问次数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param levelName 级别名称
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-09-07
     */
    @Query(value = "select a.userNo,"
            + "       a.userName,"
            + "       d.orgCode,"
            + "       d.orgAllName,"
            + "       ifnull(e.accessNum, 0) as accessNum"
            + " from du_user a"
            + "         left join du_user_post b on a.id = b.userId"
            + "         left join du_post c on b.postCode = c.postCode"
            + "         left join du_org d on a.orgCode = d.orgCode"
            + "         left join (select p.userNo, count(1) accessNum"
            + "                    from page_log p"
            + "                             inner join ds_resource r on p.resourceId = r.id"
            + "                    where if(?1 is not null && ?1 != '', p.loginTime >= ?1, 1 = 1)"
            + "                      and if(?2 is not null && ?2 != '', p.loginTime <= ?2, 1 = 1)"
            + "                      and r.type = 'menu'"
            + "                    group by p.userNo) e on a.userNo = e.userNo"
            + " where 1 = 1"
            + "  and b.isMainPost = 'Y'"
            + "  and c.postName like %?3%"
            + "  and d.orgAllName like '%板材事业部%'"
            + "  and a.status = 0"
            + "  and b.status = 0"
            + "  and c.status = 0"
            + "  and d.status = 0"
            + "  and a.operstus <> 'D'"
            + "  and b.operstus <> 'D'"
            + "  and c.operstus <> 'D'"
            + "  and d.operstus <> 'D'"
            + "order by ifnull(e.accessNum, 0) desc, a.userNo", nativeQuery = true)
    List<Map<String, Object>> findLevelDetailAccessNum(String startTime, String endTime, String levelName);

    /**
     * 查找问题反馈次数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-08-22
     */
    @Query(value = "select case"
            + "           when substr(a.orgcode, 1, 4) = 'X500' then substr(a.orgcode, 1, 5)"
            + "           else substr(a.orgcode, 1, 3) end                  as subOrgCode,"
            + "       ifnull(sum(f.feedBackNum),0) as feedBackNum,"
            + "       ifnull(count(f.feedBackNum),0) as feedBackCount,"
            + "       round(ifnull(sum(f.feedBackNum)/count(f.feedBackNum),0),2) as avgFeedBackNum"
            + " from du_user a"
            + "         left join du_user_post b on a.id = b.userid"
            + "         left join du_post c on b.postcode = c.postcode"
            + "         left join du_org d on a.orgcode = d.orgcode"
            + "         left join (select quesuserno, count(1) feedBackNum"
            + "                    from feed_back"
            + "                    where if(?1 is not null && ?1 != '', createdatetime >= ?1, 1=1)"
            + "                         and if(?2 is not null && ?2 != '', createdatetime <= ?2, 1=1)"
            + "                    group by quesuserno) f on a.userno = f.quesuserno"
            + " where 1 = 1"
            + "  and b.ismainpost = 'Y'"
            + "  and d.orgallname like '%板材事业部%'"
            + "  and a.status = 0"
            + "  and b.status = 0"
            + "  and c.status = 0"
            + "  and d.status = 0"
            + "  and a.operstus <> 'D'"
            + "  and b.operstus <> 'D'"
            + "  and c.operstus <> 'D'"
            + "  and d.operstus <> 'D'"
            + " group by subOrgCode"
            + " order by subOrgCode", nativeQuery = true)
    List<Map<String, Object>> findFeedBackNum(String startTime, String endTime);

    /**
     * 查找部门应该访问人数
     *
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-09-19
     */
    @Query(value = "select c.subOrgCode, c.num - ifnull(d.value,0) num"
            + " from (select substr(a.orgCode, 1, 3) subOrgCode, count(1) num"
            + "      from du_user a"
            + "               left join du_org b on a.orgcode = b.orgcode"
            + "      where a.operstus <> 'D'"
            + "        and a.status = '0'"
            + "        and (a.orgcode like 'X32%'"
            + "          or a.orgcode like 'X38%'"
            + "          or a.orgcode like 'X66%'"
            + "          or a.orgcode like 'X73%'"
            + "          or a.orgcode like 'X84%'"
            + "          )"
            + "        and b.orgallname not like '%甲班'"
            + "        and b.orgallname not like '%乙班'"
            + "        and b.orgallname not like '%丙班'"
            + "        and b.orgallname not like '%丁班'"
            + "        and b.parentOrgCode <> 'X66040000'"
            + "      group by subOrgCode) c"
            + "         left join (select n.code, n.value"
            + "                    from dictionary m"
            + "                             left join dictionary_dtl n on m.id = n.dict_id"
            + "                    where m.code = 'exceptUserNum') d"
            + "                   on substr(c.subOrgCode, 1, 3) = d.code", nativeQuery = true)
    List<Map<String, Object>> findShouldUseNum();

    /**
     * 汇总所有使用人数和访问次数
     *
     * @param loginTimeStart 登录时间开始
     * @param loginTimeEnd   登录时间结束
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-08
     */
    @Query(value = "select new iet.ustb.sf.vo.eo.AccessNumEO"
            + "(count(distinct a.userNo) , count(a))"
            + " from PageLog a"
            + "          left join Org b on a.orgCode = b.orgCode"
            + " where a.loginTime >= ?1"
            + "   and a.loginTime <= ?2"
//            + "   and a.oneOrgCode <> 'X'"
            + "   and b.status = '0' "
            + "   and b.operStus <> 'D'")
    List<AccessNumEO> findSummaryNumByLoginTime2(Date loginTimeStart, Date loginTimeEnd);

}
