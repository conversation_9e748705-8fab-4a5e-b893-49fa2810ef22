package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.WarningRule;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @Author: Dr.Monster
 * @Title: WarningRuleService
 * @Date: 23/09/21 13:4201
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */
public interface WarningRuleService {

    Page<WarningRule> findPageByMultiCondition(JSONObject jsonObject);

    void exportRules(JSONObject jsonObject, HttpServletResponse response);

    void batchSaveRules(JSONObject jsonObject);

    void batchDeleteRules(JSONObject jsonObject);

    void batchDeleteRulesByModuleCodes(JSONObject jsonObject);

//    List<Map<String , String>> findAreaInfoList();
//
//    List<Map<String , String>> findProductionLineInfoList();
//
//    List<Map<String , String>> findDeviceInfoList();
//
//    List<Map<String , String>> findPointInfoList();
//
    List<Map<String , String>> findModuleInfoList();
//
//    List<Map<String , String>> findRoleInfoList();

    List<String> findAreaInfoList();

    List<String> findProductionLineInfoList();

    List<String> findDeviceInfoList();

    List<String> findPointInfoList();

//    List<String> findModuleInfoList();

    List<String> findRoleInfoList();

    String changePushMode(JSONObject jsonObject);


    Integer checkPushMode(String id);

    int checkRuleExist(String ruleID);

}
