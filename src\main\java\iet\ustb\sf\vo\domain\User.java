package iet.ustb.sf.vo.domain;

import lombok.Data;
import org.hibernate.annotations.Proxy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 用户表
 */
@Data
@Entity
@Table(name = "DU_USER")
@Proxy(lazy = false)
public class User extends BaseEntity {

    //是否被选中,1-是,0-否
    @Column
    String isSelected;
    /**
     * 所属组织
     */
    @Column(length = 64, nullable = false)
    private String orgCode;
    /**
     * 组织名称
     */
    @Transient
    private String orgName;

    /**
     * 员工编号
     */
    @Column(length = 64, unique = true, nullable = false)
    private String userNo;
    /**
     * 员工姓名
     */
    @Column(length = 64, nullable = false)
    private String userName;
    /**
     * 性别
     */
    @Column(nullable = false)
    private String sex;
    /**
     * 手机号
     */
    @Column(length = 64, nullable = false)
    private String mobPhone;
    /**
     * 证件类型：身份证/台胞证/护照/其他证件
     */
    @Column(length = 64, nullable = false)
    private String cerType;
    /**
     * 证件号码
     */
    @Column(length = 64, nullable = false)
    private String idNum;
    /**
     * 账号
     */
    @Column(length = 64)
    private String loginId;
    /**
     * 出生日期
     */
    private String birthDay;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 邮箱
     */
    @Column(length = 64)
    private String email;
    /**
     * 民族
     */
    private String folk;
    /**
     * 政治面貌
     */
    private String politicsName;
    /**
     * 文化程度
     */
    private String academicDeg;
    /**
     * 微信号
     */
    @Column(length = 64)
    private String wechatNo;
    /**
     * 固定电话
     */
    private String tel;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 是否停用：0-可用,1-不可用
     */
    @Column(length = 64)
    private String status;
    /**
     * 操作标识：N 新增,U修改,D删除
     */
    @Column(length = 64)
    private String operStus;
    /**
     * 工作状态：1-在职,2-休假
     */
    @Column(length = 64)
    private String workStatus;
    /**
     * 工号
     */
    @Column(length = 64)
    private String jobNo;
    /**
     * 职工类别：1-正式工,2-顶岗实习生,3-劳务派遣工,4-返聘工,5-钟点工
     */
    @Column(length = 64)
    private String empCategory;
    /**
     * 发薪公司别
     */
    @Column(length = 64)
    private String payCompId;
    /**
     * 判重项目值域
     */
    private String repeatField;
    /**
     * 密码
     */
    @Column(length = 32)
    private String password;
    /**
     * 是否修改过密码
     */
    @Column(length = 1)
    private String isModifiedPwd;
    /**
     * 备用字段
     */
    private String spareField;

    @Column(name = "userID" , length = 256)
    private String userID;

}
