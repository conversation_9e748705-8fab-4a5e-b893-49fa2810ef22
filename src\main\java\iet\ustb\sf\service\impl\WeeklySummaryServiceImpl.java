package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.WeeklySummaryDao;
import iet.ustb.sf.vo.domain.WeeklySummary;
import iet.ustb.sf.service.WeeklySummaryService;
import iet.ustb.sf.utils.ToolsUtil;
import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

/**
 * 周/月/日总结
 *
 * <AUTHOR>
 * @create 2023-01-04
 * @see WeeklySummaryService
 */
@Service
public class WeeklySummaryServiceImpl implements WeeklySummaryService {
    @Autowired
    private WeeklySummaryDao weeklySummaryDao;

    @Override
    public List<WeeklySummary> findAll() {
        return weeklySummaryDao.findAll();
    }

    @Override
    public Page<WeeklySummary> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<WeeklySummary> weeklySummaryPage = weeklySummaryDao.findAll(createSpecs(jsonObject), pageable);
        return weeklySummaryPage;
    }

    @Override
    public List<WeeklySummary> findByMultiCondition(JSONObject jsonObject) {

        List<WeeklySummary> weeklySummaryList = weeklySummaryDao.findAll(createSpecs(jsonObject));
        return weeklySummaryList;
    }

    @Override
    public void saveList(JSONObject jsonObject) {
        JSONArray list = jsonObject.getJSONArray("data");
        List<WeeklySummary> listWeekly = JSONArray.parseArray(list.toString(), WeeklySummary.class);
        weeklySummaryDao.saveAll(listWeekly);
    }

    private Specification<WeeklySummary> createSpecs(JSONObject json) {
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            String startDate = json.getString("startDate");
            String endDate = json.getString("endDate");
            Integer module = json.getInteger("module");
            Integer type = json.getInteger("type");
            String completed = json.getString("completed");
            String projectTeamLeader = json.getString("projectTeamLeader");
            String sheetMaterialLeader = json.getString("sheetMaterialLeader");
            String responsibleUnit = json.getString("responsibleUnit");
            String workArrangeStartDate = json.getString("workArrangeStartDate");
            String workArrangeEndDate = json.getString("workArrangeEndDate");
            String plannCompleteStartDate = json.getString("plannCompleteStartDate");
            String plannCompleteEndDate = json.getString("plannCompleteEndDate");

            if (StringUtils.isNotBlank(startDate)) {
                list.add(cb.greaterThanOrEqualTo(root.get("weekDate"), startDate));
            }
            if (StringUtils.isNotBlank(endDate)) {
                list.add(cb.lessThanOrEqualTo(root.get("weekDate"), endDate));
            }
            if (StringUtils.isNotBlank(completed)) {
                list.add(cb.equal(root.get("completed"), completed));
            }
            if (StringUtils.isNotBlank(projectTeamLeader)) {
                list.add(cb.like(root.get("projectTeamLeader"), "%" + projectTeamLeader + "%"));
            }
            if (StringUtils.isNotBlank(sheetMaterialLeader)) {
                list.add(cb.like(root.get("sheetMaterialLeader"), "%" + sheetMaterialLeader + "%"));
            }
            if (StringUtils.isNotBlank(responsibleUnit)) {
                list.add(cb.like(root.get("responsibleUnit"), "%" + responsibleUnit + "%"));
            }
            if (module != null) {
                list.add(cb.equal(root.get("module"), module));
            }
            if (type != null) {
                list.add(cb.equal(root.get("type"), type));
            }

            if (StringUtils.isNotBlank(workArrangeStartDate)) {
                list.add(cb.greaterThanOrEqualTo(root.get("workArrangeDate"), workArrangeStartDate));
            }
            if (StringUtils.isNotBlank(workArrangeEndDate)) {
                list.add(cb.lessThanOrEqualTo(root.get("workArrangeDate"), workArrangeEndDate));
            }

            if (StringUtils.isNotBlank(plannCompleteStartDate)) {
                list.add(cb.greaterThanOrEqualTo(root.get("plannCompleteDate"), plannCompleteStartDate));
            }
            if (StringUtils.isNotBlank(plannCompleteEndDate)) {
                list.add(cb.lessThanOrEqualTo(root.get("plannCompleteDate"), plannCompleteEndDate));
            }
            query.where(list.toArray(new Predicate[list.size()]));

            List<Order> orderList = new ArrayList<>();
            orderList.add(cb.asc(root.get("priority")));
            orderList.add(cb.desc(root.get("createDateTime")));
            query.orderBy(orderList);

            return query.getRestriction();
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeeklySummary save(WeeklySummary weeklySummary) {
        return weeklySummaryDao.save(weeklySummary);
    }

    @Override
    public void delete(JSONObject jsonObject) throws Exception {
        String id = jsonObject.getString("id");
        weeklySummaryDao.deleteById(id);
    }

}
