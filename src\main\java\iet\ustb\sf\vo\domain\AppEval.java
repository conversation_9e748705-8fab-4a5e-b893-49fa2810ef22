package iet.ustb.sf.vo.domain;


import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 应用评价
 */
@Data
@Entity
@Table(name = "APP_EVAL")
public class AppEval extends BaseEntity {

    /**
     * 周日期
     */
    @Column(length = 64, nullable = false)
    private String weekDate;

    /**
     * 应用编号
     */
    @Column(length = 64, nullable = false)
    private String serviceNo;

    /**
     * 模型编号
     */
    @Column(length = 64, nullable = false)
    private String modelNo;

    /**
     * 价值类型
     */
    @Column(length = 64)
    private String valueType;

    /**
     * 填报单位
     */
    @Column(length = 64)
    private String inputUnit;

    /**
     * 应用情况
     */
    @Column(length = 5000)
    private String appDescription;

    /**
     * 问题反馈
     */
    @Column(length = 5000)
    private String feedBack;

}
