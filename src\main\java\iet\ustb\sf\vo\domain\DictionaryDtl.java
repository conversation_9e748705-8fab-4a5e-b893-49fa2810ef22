package iet.ustb.sf.vo.domain;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * 数据词典详细信息
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "DICTIONARY_DTL")
public class DictionaryDtl extends BaseEntity {

    /**
     * 编码
     */
    @Column(length = 64, nullable = false)
    private String code;

    /**
     * 词典值
     */
    @Column(nullable = false)
    private String value;

    /**
     * 词典值1
     */
    private String value1;

    /**
     * 词典值2
     */
    private String value2;

    /**
     * 词典值3
     */
    private String value3;

    /**
     * 描述
     */
    private String description;

    private String dictId;

    @ManyToOne
    private Dictionary dict;

    @Transient
    private String parentCode;

    public DictionaryDtl(String code, String value, String value1, String value2, String value3, String description) {
        this.code = code;
        this.value = value;
        this.value1 = value1;
        this.value2 = value2;
        this.value3 = value3;
        this.description = description;
    }
}
