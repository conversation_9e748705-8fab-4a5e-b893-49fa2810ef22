package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.AppLog;
import iet.ustb.sf.vo.eo.AccessNumEO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AppLogDao extends JpaSpecificationExecutor<AppLog>, JpaRepository<AppLog, String> {

    /**
     * 查找组织使用人数和访问次数
     *
     * @param loginTimeStart 登录时间开始
     * @param loginTimeEnd   登录时间结束
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-08
     */
    @Query(value = "select new iet.ustb.sf.vo.eo.AccessNumEO"
            + "(a.orgCode, b.orgAllName, a.level, count(distinct a.userNo) , count(a))"
            + " from AppLog a"
            + "          left join Org b on a.orgCode = b.orgCode"
            + " where a.loginTime >= ?1"
            + "   and a.loginTime <= ?2"
            + "   and a.oneOrgCode <> 'X'"
            + "   and b.status = '0' "
            + "   and b.operStus <> 'D'"
            + " group by a.orgCode"
            + " order by count(a) desc")
    List<AccessNumEO> findGroupOrgByLoginTime(Date loginTimeStart, Date loginTimeEnd);

    /**
     * 查询用户访问次数
     *
     * @param loginTime    登录时间
     * @param loginTimeEnd
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-03
     */
    @Query(value = "select new iet.ustb.sf.vo.eo.AccessNumEO(a.userNo, b.userName, count(a), c.orgAllName)"
            + " from AppLog a"
            + "          left join User b on a.userNo = b.userNo"
            + "          left join Org c on a.oneOrgCode = c.orgCode"
            + " where a.loginTime >= ?1 "
            + "   and a.loginTime <= ?2 "
            + "   and b.status = '0' "
            + "   and b.operStus <> 'D' "
            + " group by a.userNo, b.userName, c.orgAllName"
            + " order by count(a) desc")
    List<AccessNumEO> findGroupUserByLoginTime(Date loginTime, Date loginTimeEnd);

    /**
     * 查询应用访问次数
     *
     * @param loginTime    登录时间
     * @param loginTimeEnd
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-03
     */
    @Query(value = "select new iet.ustb.sf.vo.eo.AccessNumEO(a.serviceNo, count(a))"
            + " from AppLog a"
            + " where a.loginTime >= ?1 "
            + "   and a.loginTime <= ?2 "
            + " group by a.serviceNo"
            + " order by count(a) desc")
    List<AccessNumEO> findGroupAppByLoginTime(Date loginTime, Date loginTimeEnd);


    /**
     * 汇总所有使用人数和访问次数
     *
     * @param loginTimeStart 登录时间开始
     * @param loginTimeEnd   登录时间结束
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-08
     */
    @Query(value = "select new iet.ustb.sf.vo.eo.AccessNumEO"
            + "(count(distinct a.userNo) , count(a))"
            + " from PageLog a"
            + "          left join Org b on a.orgCode = b.orgCode"
            + " where a.loginTime >= ?1"
            + "   and a.loginTime <= ?2"
//            + "   and a.oneOrgCode <> 'X'"
            + "   and b.status = '0' "
            + "   and b.operStus <> 'D'")
    List<AccessNumEO> findSummaryNumByLoginTime(Date loginTimeStart, Date loginTimeEnd);

    /**
     * 查找板材事业部所有组织 //综合处驾驶班过滤掉
     *
     * @return {@link List }<{@link AccessNumEO}>
     * <AUTHOR>
     * @create 2022-11-08
     */
    @Query(value = "select t.orgLevel,"
            + "       t.orgCode                                                                                     code,"
            + "       t.orgAllName                                                                                  name,"
            + "       t.parentOrgCode                                                                               parentCode,"
            + "       (select count(1) from du_user where orgcode = t.orgcode and operstus <> 'D' and status = '0') orgNum,"
            + "       (select count(1) from du_user where orgcode = t.orgcode and operstus <> 'D' and status = '0' "
            + "             and t.orgallname not like '%甲班' and t.orgallname not like '%乙班' and t.orgallname not like '%丙班' "
            + "             and t.orgallname not like '%丁班' and t.parentOrgCode <> 'X66040000') shouldUseNum,"
            + "       useNum,"
            + "       ifnull(sum(t.accessNum), 0)                                                                   num"
            + " from (select a.orgLevel, a.orgcode, a.orgAllName, a.parentOrgCode, count(b.userno) useNum, sum(num) accessNum"
            + "      from du_org a"
            + "               left join (select orgcode, userno, count(1) num"
            + "                          from page_log"
            + "                          where logintime >= ?1"
            + "                            and logintime <= ?2"
            + "                          group by orgcode, userno) b on a.orgcode = b.orgcode"
            + "      where a.orgAllName like '板材事业部%'"
            + "        and a.orgCode <> 'X50010101'"
            + "        and a.operstus <> 'D'"
            + "      group by a.orgcode, a.orgAllName, a.orgLevel, a.parentOrgCode) t"
            + " group by t.orgLevel, t.orgcode, t.orgAllName, t.parentOrgCode"
            + " order by t.orgLevel, t.orgcode", nativeQuery = true)
    List<Map<String, Object>> findOrgUsers(String startTime, String endTime);
}
