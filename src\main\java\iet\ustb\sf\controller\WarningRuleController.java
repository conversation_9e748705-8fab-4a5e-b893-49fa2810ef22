package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.WarningRule;
import iet.ustb.sf.service.WarningRuleService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author: Dr.Monster
 * @Title: WarningRuleController
 * @Date: 23/09/21 13:5341
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */

@RestController
@RequestMapping("/WarningRule")
@Api(value = "报警规则", tags = "报警规则")
public class WarningRuleController {

    @Autowired
    WarningRuleService warningRuleService;

    @PostMapping("/findPageByMultiCondition")
    @ApiOperation(value = "按多条件查找分页", notes = "{\n" +
            "    \"pageIndex\":1,\n" +
            "    \"pageSize\":10,\n" +
            "    \"moduleCode\":\"\",\n" +
            "    \"ruleName\":\"\",\n" +
            "    \"areaID\":\"\",\n" +
            "    \"areaName\":\"\",\n" +
            "    \"productionLineID\":\"\",\n" +
            "    \"productionLineName\":\"\",\n" +
            "    \"pointName\":\"\",\n" +
            "    \"liablePersonID\":\"\",\n" +
            "    \"deviceID\":\"\",\n" +
            "    \"deviceName\":\"\",\n" +
            "    \"ruleType\":\"\",\n" +
            "    \"startTime\": \"2023-11-01 08:00\",\n" +
            "    \"endTime\": \"2023-11-30 08:00\"\n" +
            "}", produces = "application/json")
    public AjaxJson findPageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<WarningRule> warningRulePage = warningRuleService.findPageByMultiCondition(jsonObject);
            ajaxJson.setData(warningRulePage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping(value = "/exportRules")
    @ApiOperation(value = "数据查询导出（自定义）", notes = "{\n" +
            "    \"pageIndex\":1,\n" +
            "    \"pageSize\":10,\n" +
            "    \"moduleCode\":\"\",\n" +
            "    \"ruleName\":\"\",\n" +
            "    \"areaID\":\"\",\n" +
            "    \"areaName\":\"\",\n" +
            "    \"productionLineID\":\"\",\n" +
            "    \"productionLineName\":\"\",\n" +
            "    \"pointName\":\"\",\n" +
            "    \"liablePersonID\":\"\",\n" +
            "    \"deviceID\":\"\",\n" +
            "    \"deviceName\":\"\",\n" +
            "    \"ruleType\":\"\",\n" +
            "    \"startTime\": \"2023-11-01 08:00\",\n" +
            "    \"endTime\": \"2023-11-30 08:00\"\n" +
            "}", produces = "application/json")
    public AjaxJson exportRules(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            warningRuleService.exportRules(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping(value = "/batchSaveRules")
    @ResponseBody
    @ApiOperation(value = "报警规则保存", notes = "{\n" +
            "  \"list\": [\n" +
            "    {\n" +
            "      \"alertLevel\": \"1\",\n" +
            "      \"areaID\": \"1\",\n" +
            "      \"areaName\": \"1\",\n" +
            "      \"deviceID\": \"1\",\n" +
            "      \"deviceName\": \"1\",\n" +
            "      \"id\": \"1\",\n" +
            "      \"liablePersonID\": \"1\",\n" +
            "      \"liablePersonName\": \"1\",\n" +
            "      \"moduleCode\": \"1\",\n" +
            "      \"moduleName\": \"1\",\n" +
            "      \"pointID\": \"1\",\n" +
            "      \"pointName\": \"1\",\n" +
            "      \"productionLineID\": \"1\",\n" +
            "      \"productionLineName\": \"1\",\n" +
            "      \"pushRoleID\": \"1\",\n" +
            "      \"pushRoleName\": \"1\",\n" +
            "      \"ruleDesc\": \"1\",\n" +
            "      \"ruleName\": \"1\",\n" +
            "      \"ruleValue\": \"1\",\n" +
            "      \"ruleType\":\"1\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"alertLevel\": \"1\",\n" +
            "      \"areaID\": \"1\",\n" +
            "      \"areaName\": \"1\",\n" +
            "      \"deviceID\": \"1\",\n" +
            "      \"deviceName\": \"1\",\n" +
            "      \"id\": \"1\",\n" +
            "      \"liablePersonID\": \"1\",\n" +
            "      \"liablePersonName\": \"1\",\n" +
            "      \"moduleCode\": \"1\",\n" +
            "      \"moduleName\": \"1\",\n" +
            "      \"pointID\": \"1\",\n" +
            "      \"pointName\": \"1\",\n" +
            "      \"productionLineID\": \"1\",\n" +
            "      \"productionLineName\": \"1\",\n" +
            "      \"pushRoleID\": \"1\",\n" +
            "      \"pushRoleName\": \"1\",\n" +
            "      \"ruleDesc\": \"1\",\n" +
            "      \"ruleName\": \"1\",\n" +
            "      \"ruleValue\": \"1\",\n" +
            "      \"ruleType\":\"1\"\n" +
            "    }\n" +
            "  ]\n" +
            "}", produces = "application/json")
    public AjaxJson batchSaveRules(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            warningRuleService.batchSaveRules(jsonObject);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping(value = "/batchDeleteRules")
    @ResponseBody
    @ApiOperation(value = "报警规则批量删除", notes = "{\n" +
            "    \"IDs\":[\n" +
            "        \"95e35da0-444b-479f-9f5b-baa49331b106\",\n" +
            "        \"d7e6dda4-8797-4940-b984-a59a9ac3b01d\"\n" +
            "    ]\n" +
            "}", produces = "application/json")
    public AjaxJson batchDeleteRules(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            warningRuleService.batchDeleteRules(jsonObject);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping(value = "/batchDeleteRulesByModuleCodes")
    @ResponseBody
    @ApiOperation(value = "报警规则批量删除", notes = "{\n" +
            "    \"moduleCodes\":[\n" +
            "        \"ems\",\n" +
            "        \"qms\"\n" +
            "    ]\n" +
            "}", produces = "application/json")
    public AjaxJson batchDeleteRulesByModuleCodes(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            warningRuleService.batchDeleteRulesByModuleCodes(jsonObject);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findAreaInfoList")
    @ApiOperation(value = "获取区域列表", notes = "{}")
    public AjaxJson findAreaInfoList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningRuleService.findAreaInfoList());
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping("/findProductionLineInfoList")
    @ApiOperation(value = "获取产线列表", notes = "{}")
    public AjaxJson findProductionLineInfoList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningRuleService.findProductionLineInfoList());
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping("/findDeviceInfoList")
    @ApiOperation(value = "获取设备列表", notes = "{}")
    public AjaxJson findDeviceInfoList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningRuleService.findDeviceInfoList());
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping("/findPointInfoList")
    @ApiOperation(value = "获取点位列表", notes = "{}")
    public AjaxJson findPointInfoList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningRuleService.findPointInfoList());
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping("/findModuleInfoList")
    @ApiOperation(value = "获取模块列表", notes = "{}")
    public AjaxJson findModuleInfoList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningRuleService.findModuleInfoList());
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findRoleInfoList")
    @ApiOperation(value = "获取角色列表", notes = "{}")
    public AjaxJson findRoleInfoList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningRuleService.findRoleInfoList());
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


    @PostMapping("/changePushMode")
    @ApiOperation(value = "改变规则关联报警的推送状态,0-不推送,1-推送", notes = "{\n" +
            "\t\"id\":\"2aa3eff8-75a8-4508-987c-56ba64ec771c\",\n" +
            "\t\"status\":0-不推送,1-推送\n" +
            "}")
    public AjaxJson changePushMode(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(warningRuleService.changePushMode(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }
}
