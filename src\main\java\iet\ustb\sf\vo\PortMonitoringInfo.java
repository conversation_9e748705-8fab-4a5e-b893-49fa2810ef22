package iet.ustb.sf.vo;


import com.baomidou.mybatisplus.annotation.TableName;
import iet.ustb.sf.vo.domain.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 接口监控(PortMonitoringInfo)表实体类
 *
 * <AUTHOR>
 * @since 2025-03-21 17:05:44
 */
@Entity
@Table(name = "port_monitoring_info")
@Data
public class PortMonitoringInfo extends BaseEntity {
//主键
    @Column(name = "id")
    private String id;
//数据来源
    @Column(name = "data_from")
    private String dataFrom;
//接口中文名
    @Column(name = "port_name")
    private String portName;
//接口类方法
    @Column(name = "port_method")
    private String portMethod;
//ip端口
    private String ip;
//请求参数类型
    @Column(name = "request_data_type")
    private String requestDataType;
//响应参数类型
    @Column(name = "response_data_type")
    private String responseDataType;
//请求数据
    @Column(name = "request_data")
    private String requestData;
//响应数据
    @Column(name = "response_data")
    private String responseData;
//响应数据大小（byte）
    @Column(name = "response_data_size")
    private String responseDataSize;
//接口消耗时间（ms）
    @Column(name = "port_time")
    private String portTime;
    //错误日志信息
    @Column(name = "error_message")
    private String errorMessage;
    //接口地址
    @Column(name = "port_url")
    private String portUrl;
    //调用时间（ms）
    @Column(name = "call_time")
    private String callTime;
    //接口类名
    @Column(name = "port_category")
    private String portCategory;
    //接口类别
    @Column(name = "port_type")
    private Integer portType;
    //接口状态
    @Column(name = "port_status")
    private Integer portStatus;

    //查询类型：1：请求数据，2：响应数据，3：错误数据
    @Transient
    private Integer type;

}

