package iet.ustb.sf.utils;

import iet.ustb.sf.vo.domain.User;


/**
 * 用户线程本地实用程序
 *
 * <AUTHOR>
 * @create 2022-10-26
 */
public class UserThreadUtil {
    private final static ThreadLocal<User> USER_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 添加用户
     * @param user
     */
    public static void setUser(User user){
        USER_THREAD_LOCAL.set(user);
    }

    /**
     * 获取用户
     */
    public static User getUser(){
        return USER_THREAD_LOCAL.get();
    }

    /**
     * 清理用户
     */
    public static void clear(){
        USER_THREAD_LOCAL.remove();
    }
}
