package iet.ustb.sf.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import iet.ustb.sf.vo.domain.Org;
import org.apache.ibatis.annotations.Param;

/**
 * (DuOrg)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-06 15:38:05
 */
public interface DuOrgMapper extends BaseMapper<Org> {

    /**
    * 批量新增数据（MyBatis原生foreach方法）
    *
    * @param entities List<DuOrg> 实例对象列表
    * @return 影响行数
    */
    int insertBatch(@Param("entities") List<Org> entities);

    /**
    * 批量新增或按主键更新数据（MyBatis原生foreach方法）
    *
    * @param entities List<DuOrg> 实例对象列表
    * @return 影响行数
    * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
    */
    int insertOrUpdateBatch(@Param("entities") List<Org> entities);

    /**
     * 根据父级编码或编码集合查询组织信息
     * @param orgCodeList
     * @param parentOrgCodeList
     * @return
     */
    List<Org> checkOrgCodesExist(@Param("orgCodeList") List<String> orgCodeList,
                                 @Param("parentOrgCodeList") List<String> parentOrgCodeList);

    /**
     * 获取当前最大一级菜单编号
     * @return
     */
    String getMaxTopLevelCode();

    /**
     * 获取当前最大子菜单编号
     * @param parentCode
     * @return
     */
    String getMaxSubLevelCode(String parentCode);


    /**
     * 根据父级编码查询组织信息
     * @param parentOrgCode
     * @return
     */
    List<Map<String, Object>> findListByOrgCode(@Param("parentOrgCode") String parentOrgCode);

}

