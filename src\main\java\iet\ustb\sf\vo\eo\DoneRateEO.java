package iet.ustb.sf.vo.eo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * @Author: Dr.Monster
 * @Title: DoneRateVo
 * @Date: 23/11/29 17:0350
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */
@Data
public class DoneRateEO {

    @Excel(name = "角色名/模块名")
    String roleName;

    @Excel(name = "已完成")
    int done;

    @Excel(name = "未完成")
    int unDone;

    @Excel(name = "总计")
    int total;

    @Excel(name = "完成率")
    String rate;

    @Excel(name = "一级报警")
    Integer firstLevel;

    @Excel(name = "二级报警")
    Integer secondLevel;

    @Excel(name = "三级报警")
    Integer thirdLevel;

    @Excel(name = "不标准建议数（为空）")
    Integer adviceAtypia;

    @Excel(name = "不标准分析数（为空）")
    Integer analysisAtypia;

    //2小时内处理完
    @Excel(name = "2小时内处理完")
    public Integer twoHourDone = 0;

    //4小时内处理完
    @Excel(name = "4小时内处理完")
    public Integer fourHourDone = 0;

    //8小时内处理完
    @Excel(name = "8小时内处理完")
    public Integer eightHourDone = 0;

    //8小时内未处理完
    @Excel(name = "8小时内未处理完")
    public Integer gtEightHourDone = 0;
}
