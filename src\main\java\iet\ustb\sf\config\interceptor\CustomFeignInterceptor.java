package iet.ustb.sf.config.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import iet.ustb.sf.utils.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@Configuration
public class CustomFeignInterceptor implements RequestInterceptor {
    @Autowired
    private AuthUtil authUtil;

    /**
     * feign拦截器，可以带token
     * @param requestTemplate
     */
    @Override
    public void apply(RequestTemplate requestTemplate) {
//        requestTemplate.header("Authorization", "Bearer my-token");
//        requestTemplate.header("Trace-Id", UUID.randomUUID().toString());
        if (!requestTemplate.headers().containsKey("Content-Type")) {
            requestTemplate.header("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        }
        requestTemplate.header("x-token", authUtil.generateXToken());
    }
}
