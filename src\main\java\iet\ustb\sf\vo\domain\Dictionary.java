package iet.ustb.sf.vo.domain;

import lombok.Data;

import javax.persistence.*;

/**
 * 数据词典
 *
 * <AUTHOR>
 * @create 2023-02-09
 * @see BaseEntity
 */
@Data
@Entity
@Table(name = "DICTIONARY")
public class Dictionary extends BaseEntity {

    /**
     * 编码
     */
    @Column(length = 64, nullable = false)
    private String code;

    /**
     * 名称
     */
    @Column(length = 128, nullable = false)
    private String name;

    /**
     * 描述
     */
    private String description;

}
