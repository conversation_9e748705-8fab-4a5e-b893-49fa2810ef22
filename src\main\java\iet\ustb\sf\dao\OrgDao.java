package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.Org;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface OrgDao extends JpaSpecificationExecutor<Org>, JpaRepository<Org, String> {

    @Query(value = "select * from du_org b where b.orgCode=?1", nativeQuery = true)
    Org findByOrgCode(String orgCode);

    @Query(value = "SELECT id,orgCode,"
            + " orgAllName,"
            + " orgDesc,"
            + " parentOrgCode,"
            + " (select s.orgAllName from du_org s where s.orgCode = t.parentOrgCode) as parentOrgName,"
            + " createUserNo,"
            + " createDateTime,"
            + " status,"
            + " EXISTS"
            + "     (SELECT id FROM du_org WHERE parentOrgCode = t.orgCode AND status = 0) AS hasChildren"
            + " FROM du_org t"
            + " WHERE  t.parentOrgCode = ?1", nativeQuery = true)
    List<Map<String, Object>> findListByOrgCode(String parentOrgCode);

    @Override
    List<Org> findAll();

    @Override
    Optional<Org> findById(String id);

    Org findOneById(String id);


    @Query(value = "from Org where parentOrgCode = ?1")
    List<Org> findOrgsByParentOrgCode(String parentOrgCode);

    @Query(value = " select do.* from du_org do join du_user du on du.orgCode = do.orgCode " +
            " where du.userNo = ?1 ", nativeQuery = true)
    List<Org> findOrgByUserNo(String userNo);

    @Query(value = " select * from du_org where orgAllName in (?1) ", nativeQuery = true)
    List<Org> findOrgsByNames(List<String> names);

    /**
     * 查找可用列表
     *
     * @return {@link List }<{@link Org }>
     * <AUTHOR>
     * @create 2023-09-22
     */
    @Query(value = "from Org")
    List<Org> findAvailableList();

    void deleteByOrgCode(String orgCode);

    @Query(value = "from Org where parentOrgCode=?1" )
    List<Org> findOrgsByParent(String orgCode);
}
