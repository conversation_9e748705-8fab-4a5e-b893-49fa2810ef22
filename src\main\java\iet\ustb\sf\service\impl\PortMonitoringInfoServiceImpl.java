package iet.ustb.sf.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import iet.ustb.sf.config.interceptor.annotation.AutoLog;
import iet.ustb.sf.dao.PortMonitoringInfoDao;
import iet.ustb.sf.mapper.IconImgMapper;
import iet.ustb.sf.mapper.PortMonitoringInfoMapper;
import iet.ustb.sf.service.PortMonitoringInfoService;
import iet.ustb.sf.utils.constant.EnumConstant;
import iet.ustb.sf.vo.PortMonitoringInfo;
import iet.ustb.sf.vo.domain.IconImg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;

/**
 * 接口监控(PortMonitoringInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-21 17:05:46
 */
@Service("portMonitoringInfoService")
public class PortMonitoringInfoServiceImpl implements PortMonitoringInfoService {

    @Autowired
    private PortMonitoringInfoMapper portMonitoringInfoMapper;

    @Autowired
    private PortMonitoringInfoDao portMonitoringInfoDao;

    /**
     * 新增
     * @param portMonitoringInfo
     */
    public void insert(PortMonitoringInfo portMonitoringInfo) {
        if(ObjectUtil.isEmpty(portMonitoringInfo)){
            return;
        }
        portMonitoringInfoDao.save(portMonitoringInfo);
    };

    /**
     * 分页查询
     * @param portMonitoringInfo
     * @return
     */
    @Override
    public IPage<PortMonitoringInfo> getPortMonitoringInfoByPage(PortMonitoringInfo portMonitoringInfo) {
        Page<PortMonitoringInfo> page = new Page<>(portMonitoringInfo.getPageIndex(), portMonitoringInfo.getPageSize());
        return portMonitoringInfoMapper.getPortMonitoringInfoByPage(page, portMonitoringInfo);
    }

    /**
     * 查询数据详情
     * @param portMonitoringInfo
     * @return
     */
    @Override
    public PortMonitoringInfo getPortMonitoringDetail(PortMonitoringInfo portMonitoringInfo) {
        PortMonitoringInfo result = new PortMonitoringInfo();

        if(EnumConstant.PORT_MONITORING_TYPE_1.equals(portMonitoringInfo.getType())){
            result = portMonitoringInfoMapper.getRequestData(portMonitoringInfo);
        }else if(EnumConstant.PORT_MONITORING_TYPE_2.equals(portMonitoringInfo.getType())){
            result = portMonitoringInfoMapper.getResponseData(portMonitoringInfo);
        } else if (EnumConstant.PORT_MONITORING_TYPE_3.equals(portMonitoringInfo.getType())) {
            result = portMonitoringInfoMapper.getErrorData(portMonitoringInfo);
        }

        return result;
    }
}

