package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.CoordinateMatter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR>
 * @create 2022-12-16
 */
public interface CoordinateMatterDao  extends JpaSpecificationExecutor<CoordinateMatter>, JpaRepository<CoordinateMatter, String> {

    /**
     * 查找最大优先级
     *
     * @return {@link Integer }
     * <AUTHOR>
     * @create 2022-12-22
     */
    @Query(value = "select max(priority) from CoordinateMatter")
    Integer findMaxPriority();
}
