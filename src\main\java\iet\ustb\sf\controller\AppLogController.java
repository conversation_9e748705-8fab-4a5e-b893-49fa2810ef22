package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.AppLog;
import iet.ustb.sf.vo.eo.AccessNumEO;
import iet.ustb.sf.service.AppLogService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 应用日志
 *
 * <AUTHOR>
 * @create 2022-09-21
 */
@RestController
@RequestMapping("/AppLog")
@Api(value = "应用日志", tags = "应用日志")
public class AppLogController {

    @Autowired
    private AppLogService appLogService;

    @ResponseBody
    @ApiOperation(value = "查询所有", notes = "查询所有")
    @PostMapping("/findAll")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<AppLog> appLogList = appLogService.findAll();
            ajaxJson.setData(appLogList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ResponseBody
    @ApiOperation(value = "按多条件查找应用日志", notes = "{\"userNo\":\"023958\",\"orgCode\":\"1212\",\"startTime\": \"2022-09-22 09:16:00\", \"endTime\": \"2022-09-22 10:13:30\"}")
    @PostMapping("/findByMultiCondition")
    public AjaxJson findByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<AppLog> appLogPages = appLogService.findByMultiCondition(jsonObject);
            ajaxJson.setData(appLogPages);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ResponseBody
    @ApiOperation(value = "按登录时间查找不同组织/人员/应用访问次数",
            notes = "{\"loginTimeStart\":\"2022-10-31\",\"loginTimeEnd\":\"2022-11-04\",\"type\":\"org-2\"}")
    @PostMapping("/findAccessNumByLoginTime")
    public AjaxJson findAccessNumByLoginTime(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<AccessNumEO> list = appLogService.findAccessNumByLoginTime(jsonObject);
            ajaxJson.setData(list);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ResponseBody
    @ApiOperation(value = "查询应用访问次数",
            notes = "{\"loginTimeStart\":\"2023-02-01\",\"loginTimeEnd\":\"2023-02-28\"}")
    @PostMapping("/findGroupAppByLoginTime")
    public AjaxJson findGroupAppByLoginTime(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<AccessNumEO> list = appLogService.findGroupAppByLoginTime(jsonObject);
            ajaxJson.setData(list);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportExcel")
    @ApiOperation(value = "导出Excel",
            notes = "入参：{\"loginTimeStart\":\"2022-10-31\",\"loginTimeEnd\":\"2022-11-04\"}")
    public AjaxJson exportExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            appLogService.exportExcel(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportLeaderAndOrgExcel")
    @ApiOperation(value = "导出Excel",
            notes = "入参：{\"loginTimeStart\":\"2023-08-01\",\"loginTimeEnd\":\"2023-08-31\"}")
    public AjaxJson exportLeaderAndOrgExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            appLogService.exportLeaderAndOrgExcel(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportWord")
    @ApiOperation(value = "导出Word",
            notes = "入参：{\"loginTimeStart\": \"2022-10-31\",\"loginTimeEnd\": \"2022-11-04\" }")
    public AjaxJson exportWord(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            JSONObject json = appLogService.exportWord(jsonObject);
            ajaxJson.setData(json);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @Deprecated
    @ResponseBody
    @ApiOperation(value = "保存（弃用）", produces = "application/json ")
    @PostMapping("/save")
    public AjaxJson save(@RequestBody @Valid AppLog AppLog, HttpServletRequest request) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            // 保存
            appLogService.save(AppLog, request);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }


}
