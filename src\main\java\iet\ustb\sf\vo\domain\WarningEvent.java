package iet.ustb.sf.vo.domain;

import lombok.Data;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.Proxy;

import javax.persistence.*;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: Dr.Monster
 * @Slogan: The never-compromising angry youth.
 * @PackageName: iet.ustb.sf.domain
 * @Date: 2022/08/10/13:41
 * @Description:
 */
@Data
@Entity
@Table(name = "Warning_Event")
@Proxy(lazy = false)
public class WarningEvent extends BaseEntity {

    //消息标识唯一ID
    @Column
    String msgID;

    //是否为离线消息,0-否,1-是
    @Column
    String isOffline;

    //是否推送,0-否,1-是
    @Column
    String isPush;

    //要推送的人
    @Column
    String userNo;

    //报警厂区
    @Column
    String waringPlant;

    //报警模块儿
    @Column
    String serviceName;

    //报警类型,info-消息, alert-报警
    @Column
    String alertType;

    //报警等级,1-一般,2-严重,3-紧急,(当type为alert时填写)
    @Column
    String alertLevel;

    @OneToOne
    @Cascade(value = org.hibernate.annotations.CascadeType.SAVE_UPDATE)
    WarningEventBody warningEventBody;

    //状态,0-未处理，1-已处理,9-审核中
    @Column
    String status;

    //报警中文名
    @Transient
    String serviceNameCN;

    //区域ID
    @Column
    String areaID;

    //区域名称
    @Column
    String areaName;

    //点位ID
    @Column
    String pointID;

    //点位名称
    @Column
    String pointName;

    //设备ID
    @Column
    String deviceID;

    //设备名称
    @Column
    String deviceName;

    //0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测，6-趋势校验报警
    @Column
    Integer warningType;

    //报警规则ID
    @Column
    String warningRuleID;

    /**
     * 班次,0，1，2-大夜班，白班，小夜班
     */
    @Column
    String classes;

    /**
     * 班组,0，1，2，3-甲，乙，丙，丁
     */
    @Column
    String team;
}
