package iet.ustb.sf.vo.eo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 领导访问次数
 *
 * <AUTHOR>
 * @create 2022-11-03
 * @see Serializable
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LeaderAccessNumEO implements Serializable {

    /**
     * 工号
     */
    private String userNo;
    /**
     * 姓名
     */
    @Excel(name = "姓名",width = 20)
    private String userName;
    /**
     * 第一周
     */
    @Excel(name = "第一周（次）", type=10, width = 20)
    private Long firstWeek;
    /**
     * 第二周
     */
    @Excel(name = "第二周（次）", type=10, width = 20)
    private Long secondWeek;
    /**
     * 第三周
     */
    @Excel(name = "第三周（次）", type=10, width = 20)
    private Long thirdWeek;
    /**
     * 第四周
     */
    @Excel(name = "第四周（次）", type=10, width = 20)
    private Long fourthWeek;
    /**
     * 第五周
     */
    @Excel(name = "第五周（次）", type=10, width = 20)
    private Long fifthWeek;
    /**
     * 累计
     */
    @Excel(name = "累计（次）", type=10)
    private Long totalNum;
}
