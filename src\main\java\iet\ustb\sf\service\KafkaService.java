//package iet.ustb.sf.service;
//
//import lombok.extern.apachecommons.CommonsLog;
//import org.apache.kafka.clients.consumer.ConsumerRecord;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.kafka.annotation.KafkaListener;
//import org.springframework.kafka.core.KafkaTemplate;
//import org.springframework.stereotype.Service;
//
///**
// * Title:
// * Packet:cn.edu.ustb.nercar.service
// * Description:
// * Author:崔庆胜
// * Create Date: 2020/7/10 5:53 PM
// * Modify User:
// * Modify Date:
// * Modify Description:
// */
///*@Component*/
//@CommonsLog
//@Service
//public class KafkaService {
//
//    @Autowired
//    private KafkaTemplate template;
//
////    @KafkaListener(topics = {"Test_Kafka"}, groupId = "resources_01")
//    private void onMessage1(ConsumerRecord<String, String> consumerRecord) throws Exception {
//        long start = System.currentTimeMillis();
//
//        try {
////            log.info("from listener1 group1 : " + consumerRecord.value() + ", " + consumerRecord.partition());
//        } catch (Exception e) {
////            log.error("receive is " + consumerRecord.topic() + ", "
////                    + consumerRecord.value() + ",DM_01,msg:" + e.getMessage());
//        }
////        log.warn("DM01: duration **********" + (System.currentTimeMillis() - start));
//    }
//
//
//    public void sendMessage(String topic, String message) {
//        template.send(topic, message);
////        log.info("send is " + topic + ": " + message);
//    }
//
//
//}
