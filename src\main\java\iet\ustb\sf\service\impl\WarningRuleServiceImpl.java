package iet.ustb.sf.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.RoleDao;
import iet.ustb.sf.dao.WarningRuleDao;
import iet.ustb.sf.vo.domain.Role;
import iet.ustb.sf.vo.domain.WarningRule;
import iet.ustb.sf.utils.enumVo.WarningTypeEnumVo;
import iet.ustb.sf.vo.eo.WarningRuleEO;
import iet.ustb.sf.service.WarningRuleService;
import iet.ustb.sf.utils.ToolsUtil;
import lombok.Cleanup;
import org.apache.poi.ss.usermodel.Workbook;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.*;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: Dr.Monster
 * @Title: WarningRuleServiceImpl
 * @Date: 23/09/21 13:4213
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */
@Service
public class WarningRuleServiceImpl implements WarningRuleService {

    @Autowired
    WarningRuleDao warningRuleDao;

    @Autowired
    RoleDao roleDao;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<WarningRule> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);
        Page<WarningRule> warningRulePage = warningRuleDao.findAll(new Specification<WarningRule>() {
            @Override
            public Predicate toPredicate(Root<WarningRule> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<Predicate>();
                Predicate[] arr = new Predicate[list.size()];

                String moduleCode = jsonObject.getString("moduleCode");
                String ruleName = jsonObject.getString("ruleName");
                String areaID = jsonObject.getString("areaID");
                String areaName = jsonObject.getString("areaName");
                String productionLineID = jsonObject.getString("productionLineID");
                String productionLineName = jsonObject.getString("productionLineName");
                String deviceID = jsonObject.getString("deviceID");
                String deviceName = jsonObject.getString("deviceName");
                String pointName = jsonObject.getString("pointName");
                String liablePersonID = jsonObject.getString("liablePersonID");
                Integer ruleType = jsonObject.getInteger("warningType");

                String startTime = jsonObject.getString("startTime");
                String endTime = jsonObject.getString("endTime");

                if (!ToolsUtil.isEmpty(moduleCode)) {
                    list.add(cb.equal(root.get("moduleCode"), moduleCode));
                }

                if (!ToolsUtil.isEmpty(ruleName)) {
                    list.add(cb.like(root.get("ruleName"), "%" + ruleName + "%"));
                    list.add(cb.like(root.get("ruleDesc"), "%" + ruleName + "%"));
                }

                if (!ToolsUtil.isEmpty(areaID)) {
                    list.add(cb.equal(root.get("areaID"), areaID));
                }

                if (!ToolsUtil.isEmpty(areaName)) {
                    list.add(cb.equal(root.get("areaName"), areaName));
                }

                if (!ToolsUtil.isEmpty(deviceID)) {
                    list.add(cb.equal(root.get("deviceID"), deviceID));
                }

                if (!ToolsUtil.isEmpty(deviceName)) {
                    list.add(cb.equal(root.get("deviceName"), deviceName));
                }

                if (!ToolsUtil.isEmpty(productionLineID)) {
                    list.add(cb.equal(root.get("productionLineID"), productionLineID));
                }

                if (!ToolsUtil.isEmpty(productionLineName)) {
                    list.add(cb.equal(root.get("productionLineName"), productionLineName));
                }

                if (!ToolsUtil.isEmpty(pointName)) {
                    list.add(cb.like(root.get("pointName"), "%" + pointName + "%"));
                }

                if (!ToolsUtil.isEmpty(liablePersonID)) {
                    list.add(cb.equal(root.get("liablePersonID"), liablePersonID));
                }

                if (!ToolsUtil.isEmpty(ruleType)) {
                    list.add(cb.equal(root.get("ruleType"), ruleType));
                }

                list.add(cb.notEqual(root.get("status"), 0));

                if (!ToolsUtil.isEmpty(startTime)) {
                    list.add(cb.greaterThanOrEqualTo(root.get("createDateTime").as(String.class), startTime + ":00"));
                }
                if (!ToolsUtil.isEmpty(endTime)) {
                    list.add(cb.lessThanOrEqualTo(root.get("createDateTime").as(String.class), endTime + ":59"));
                }

                cq.where(list.toArray(arr));
                cq.orderBy(cb.desc(root.get("createDateTime")));
                return null;
            }
        }, pageable);

//        List<WarningRule> warningRuleList = warningRulePage.getContent();
//        warningRuleList.stream().map(x-> {
//            for(WarningTypeEnumVo vo : WarningTypeEnumVo.values()){
//                if(!ToolsUtil.isEmpty(x.getRuleType())){
//                    if(x.getRuleType() == vo.getCode()){
//                        x.setRuleTypeName(vo.getName());
//                        return x;
//                    }
//                }
//            }
//            return x;
//        }).collect(Collectors.toList());

//        warningRulePage.getContent().stream().map(x-> {
//            for(WarningTypeEnumVo vo : WarningTypeEnumVo.values()){
//                if(!ToolsUtil.isEmpty(x.getRuleType())){
//                    if(x.getRuleType() == vo.getCode()){
//                        x.setRuleTypeName(vo.getName());
//                        return x;
//                    }
//                }
//            }
//            return x;
//        });
        return warningRulePage;
    }

    @Override
    public void exportRules(JSONObject jsonObject, HttpServletResponse response) {

        //功能描述：把同一个表格多个sheet测试结果重新输出
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("报警规则清单");
        // 设置sheet表头名称
        exportParams.setSheetName("报警规则清单");
        jsonObject.put("pageIndex" , 1);
        jsonObject.put("pageSize" , Integer.MAX_VALUE);

        Page<WarningRule> warningRulePage = findPageByMultiCondition(jsonObject);
        List<WarningRule> warningRuleList = warningRulePage.get().collect(Collectors.toList());
        warningRuleList.stream().map(x-> {
            for(WarningTypeEnumVo vo : WarningTypeEnumVo.values()){
                if(!ToolsUtil.isEmpty(x.getRuleType())){
                    if(x.getRuleType() == vo.getCode()){
                        x.setRuleTypeName(vo.getName());
                        return x;
                    }
                }
            }
            return x;
        }).collect(Collectors.toList());
        List<WarningRuleEO> warningRuleEOList = JSONArray.parseArray(JSON.toJSONString(warningRuleList) , WarningRuleEO.class);
        try{
            @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(exportParams, WarningRuleEO.class , warningRuleEOList);
            ToolsUtil.downLoadExcel("报警规则清单", response, workBook);
        }catch (Exception e){

        }
    }

    @Override
    public void batchSaveRules(JSONObject jsonObject) {
        if(ToolsUtil.isEmpty(jsonObject) || ToolsUtil.isEmpty(jsonObject.get("list"))){
            return;
        }else {
//            System.out.println("JSONObject..." + jsonObject.toJSONString());
            List<WarningRule> ruleList = ToolsUtil.jsonObjectToEntityList(jsonObject.get("list") , WarningRule.class);

            for(WarningRule rule : ruleList){
                matchRole(rule);
                if (ToolsUtil.isEmpty(rule.getPushRoleName())){
                    rule.setStatus(0);
                }else{
                    rule.setStatus(1);
                }
                warningRuleDao.save(rule);
            }
//            System.out.println("ruleList..." + JSONObject.toJSONString(ruleList));
//            warningRuleDao.saveAllAndFlush(ruleList);
        }
    }

    @Override
    public void batchDeleteRules(JSONObject jsonObject) {
        if(ToolsUtil.isEmpty(jsonObject) || ToolsUtil.isEmpty(jsonObject.get("IDs"))){
            return;
        }else {
//            System.out.println("JSONObject..." + jsonObject.toJSONString());
            List<String> iDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("IDs") , String.class);

            for(String id : iDs){
                WarningRule warningRule = warningRuleDao.getById(id);
                if(ToolsUtil.isEmpty(warningRule)){
                    continue;
                }
                warningRule.setStatus(0);
                warningRuleDao.save(warningRule);
            }
        }
    }

    @Override
    public void batchDeleteRulesByModuleCodes(JSONObject jsonObject) {
        if(ToolsUtil.isEmpty(jsonObject) || ToolsUtil.isEmpty(jsonObject.get("moduleCodes"))){
            return;
        }else {
//            System.out.println("JSONObject..." + jsonObject.toJSONString());
            List<String> moduleCodes = ToolsUtil.jsonObjectToEntityList(jsonObject.get("moduleCodes") , String.class);
            List<WarningRule> warningRuleList = warningRuleDao.findWarningRulesByModuleCodes(moduleCodes);
            for(WarningRule warningRule : warningRuleList){
                warningRule.setStatus(0);
                warningRuleDao.save(warningRule);
            }
        }
    }

    public void matchRole(WarningRule warningRule){
        String roleIDStr = warningRule.getPushRoleID();
        if(ToolsUtil.isEmpty(roleIDStr)){
            return;
        }
        String[] roleIDs = roleIDStr.split(",");
        StringBuilder sb = new StringBuilder();
        Role role;
        String count;
        for(String roleID : roleIDs){
            count = roleDao.checkExist(roleID);
            if("0".equals(count)){
                continue;
            }
            role = roleDao.getById(roleID);
            sb.append(role.getRoleName());
            sb.append(",");
        }
        String roleStr = sb.toString();
        if(ToolsUtil.isEmpty(roleStr)){
            return;
        }
        warningRule.setPushRoleName(roleStr.substring(0 , sb.toString().length() - 1));
    }


//    @Override
//    public List<Map<String, String>> findAreaInfoList() {
//        String sql = "select distinct (areaID) as ID , areaName as Name from warning_rule";
//        return findIDNameMap(sql);
//    }
//
//    @Override
//    public List<Map<String, String>> findProductionLineInfoList() {
//        String sql = "select distinct (productionLineID) as ID , productionLineName as Name from warning_rule";
//        return findIDNameMap(sql);
//    }
//
//    @Override
//    public List<Map<String, String>> findDeviceInfoList() {
//        String sql = "select distinct (deviceID) as ID , deviceName as Name from warning_rule";
//        return findIDNameMap(sql);
//    }
//
//    @Override
//    public List<Map<String, String>> findPointInfoList() {
//        String sql = "select distinct (pointID) as ID , pointName as Name from warning_rule";
//        return findIDNameMap(sql);
//    }
//
    @Override
    public List<Map<String, String>> findModuleInfoList() {
        String sql = "select distinct (moduleCode) as ID , moduleName as Name from warning_rule";
        return findIDNameMap(sql);
    }
//
//    @Override
//    public List<Map<String, String>> findRoleInfoList() {
//        String sql = "select distinct (pushRoleID) as ID , pushRoleName as Name from warning_rule";
//        return findIDNameMap(sql);
//    }
//
    List<Map<String , String>> findIDNameMap(String sql){
        Session session = entityManager.unwrap(Session.class);
        SQLQuery dataSqlQuery = session.createSQLQuery(sql.toString());
        dataSqlQuery.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return dataSqlQuery.list();
    }




    @Override
    public List<String> findAreaInfoList() {
        String sql = "select distinct areaName as Name from warning_rule";
        return findNameList(sql);
    }

    @Override
    public List<String> findProductionLineInfoList() {
        String sql = "select distinct productionLineName as Name from warning_rule";
        return findNameList(sql);
    }

    @Override
    public List<String> findDeviceInfoList() {
        String sql = "select distinct deviceName as Name from warning_rule";
        return findNameList(sql);
    }

    @Override
    public List<String> findPointInfoList() {
        String sql = "select distinct pointName as Name from warning_rule";
        return findNameList(sql);
    }

//    @Override
//    public List<String> findModuleInfoList() {
//        String sql = "select distinct moduleName as Name from warning_rule";
//        return findNameList(sql);
//    }

    @Override
    public List<String> findRoleInfoList() {
        String sql = "select distinct pushRoleName as Name from warning_rule";
        return findNameList(sql);
    }

    @Override
    public String changePushMode(JSONObject jsonObject) {
        String id = jsonObject.getString("id");
        Integer status = jsonObject.getInteger("status");
        WarningRule warningRule = warningRuleDao.getById(id);
        warningRule.setPushMode(status);
        return warningRuleDao.save(warningRule).getId();
    }

    @Override
    public Integer checkPushMode(String id) {
        try{
            WarningRule warningRule = warningRuleDao.getById(id);
            return warningRule.getPushMode();
        }catch (Exception e){
            return 0;
        }
    }

    //监测规则是否存在，0-不存在，1-存在
    @Override
    public int checkRuleExist(String ruleID){
        try{
            WarningRule warningRule = warningRuleDao.getById(ruleID);
            if(ToolsUtil.isEmpty(warningRule)){
                return 0;
            }else{
                return warningRule.getStatus();
            }
        }catch (Exception e){
            return 0;
        }

    }

    List<String> findNameList(String sql){
        Session session = entityManager.unwrap(Session.class);
        SQLQuery dataSqlQuery = session.createSQLQuery(sql.toString());
        dataSqlQuery.setResultTransformer(Transformers.TO_LIST);
        return dataSqlQuery.list();
    }
}
