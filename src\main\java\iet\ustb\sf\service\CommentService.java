package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.Comment;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 问题反馈评论
 *
 * <AUTHOR>
 * @create 2022-10-26
 */
public interface CommentService {

    /**
     * 查找所有内容
     *
     * @return {@link List }<{@link Comment }>
     * <AUTHOR>
     * @create 2022-10-26
     */
    List<Comment> findAll();

    /**
     * 按多条件查找分页
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link Comment }>
     * <AUTHOR>
     * @create 2022-10-26
     */
    Page<Comment> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 通过问题反馈id查找树
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link Comment }>
     * <AUTHOR>
     * @create 2022-11-03
     */
    List<Comment> findTreeByFeedBackId(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param comment 评论
     * @return {@link Comment }
     * <AUTHOR>
     * @create 2022-10-26
     */
    Comment save(Comment comment);
}
