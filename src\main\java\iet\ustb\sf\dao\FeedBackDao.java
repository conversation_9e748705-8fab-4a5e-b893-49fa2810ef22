package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.FeedBack;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface FeedBackDao extends JpaSpecificationExecutor<FeedBack>, JpaRepository<FeedBack, String> {

    @Query(value = "select quesType, count(*) num from feed_back "
            + " where handleStatus in ?1 "
            + "  and createDateTime >= ?2 and createDateTime <= ?3"
            + " group by quesType", nativeQuery = true)
    List<Map<String, Integer>> findCountByStatus(List<Integer> statusList, Date startDate, Date endDate);


    /**
     * 查找用户问题反馈数
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-09-06
     */
    @Query(value = "select a.quesUserNo userNo,"
            + "       b.userName,"
            + "       c.orgCode,"
            + "       c.orgAllName,"
            + "       count(1) totalNum,"
            + "       ifNull((select count(1)"
            + "               from feed_back"
            + "               where quesUserNo = a.quesUserNo and handleStatus = 2"
            + "                 and date(createDateTime) >= ?1 and date(createDateTime) <= ?2"
            + "                 and quesType = 2"
            + "               group by quesUserNo),0) finishedNum,"
            + "       ifNull((select count(1)"
            + "        from feed_back"
            + "        where quesUserNo = a.quesUserNo and handleStatus <> 2"
            + "            and date(createDateTime) >= ?1 and date(createDateTime) <= ?2"
            + "            and quesType = 2"
            + "        group by quesUserNo),0) unfinishedNum"
            + " from feed_back a"
            + "          left join du_user b on a.quesuserno = b.userno"
            + "          left join du_org c on b.orgcode = c.orgcode"
            + " where date(a.createDateTime) >= ?1 and date(a.createDateTime) <= ?2"
            + "   and a.quesType = 2"
            + "   and b.status = '0'"
            + "   and c.status = '0'"
            + "   and b.operstus <> 'D'"
            + "   and c.operstus <> 'D'"
            + " group by a.quesuserno, b.userName, c.orgCode, c.orgAllName"
            + " order by c.orgCode, a.quesUserNo", nativeQuery = true)
    List<Map<String, Object>> findUserFeedBackNum(String startDate, String endDate);

    /**
     * 查找厂处问题反馈数量
     *
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param handleStatus 处理状态
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2024-01-30
     */
    @Query(value = "select case"
            + "           when substr(quesOrgCode, 1, 4) = 'X500' then substr(quesOrgCode, 1, 5)"
            + "           else substr(quesOrgCode, 1, 3) end as oneOrgCode,"
            + "       count(1)                                  num"
            + " from feed_back"
            + " where quesType = 2"
            + "   and createDateTime between date_format(?1, '%Y-%m-%d') and date_format(?2, '%Y-%m-%d')"
            + "   and if(?3 is not null && ?3 != '', handleStatus = ?3, 1 = 1)"
            + "   and (substr(quesOrgCode, 1, 3) in ('X32', 'X38', 'X66', 'X73')"
            + "     or substr(quesOrgCode, 1, 5) in ('X5001', 'X5002', 'X5003', 'X5004', 'X5007'))"
            + " group by oneOrgCode"
            + " order by num desc", nativeQuery = true)
    List<Map<String, Object>> findOneOrgFeedBackNum(String startDate, String endDate, Integer handleStatus);
}
