package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.dao
 * @title: RoleDao
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/1010:47
 */
public interface RoleDao extends JpaSpecificationExecutor<Role>, JpaRepository<Role, String> {

    @Query(value = "SELECT role.* from ds_role as role join\n" +
            "ds_role_resourcelist as rrlist on\n" +
            "role.id = rrlist.Role_id\n" +
            "where rrlist.resourceList_id = ?1 ", nativeQuery = true)
    List<Role> findRoleByRscID(String RscID);


//    @Query(value = "SELECT role.* from ds_role as role join\n" +
//            "ds_role_userlist as rulist on\n" +
//            "role.id = rulist.Role_id\n" +
//            "where rulist.userList_id = ?1 and role.status = 1", nativeQuery = true)
//    List<Role> findRoleByUserID(String userID);



    @Query(value = "SELECT role.id,description, isDef, roleCode, roleName, status, type, createDateTime, createUserNo, updateDateTime, updateUserNo\n" +
            "from ds_role as role\n" +
            "         join\n" +
            "     ds_role_userlist as rulist on\n" +
            "         role.id = rulist.Role_id\n" +
            "where rulist.userList_id = ?1\n" +
            "  and role.status = 1", nativeQuery = true)
    List<Role> findRoleByUserID(String userID);


    @Query(value = "select * from ds_role where status = 1", nativeQuery = true)
    List<Role> findAllAvailable();


    @Modifying
    @Query(value = "delete from ds_role_resourcelist where resourceList_id = " +
            "?1 and Role_id in (?2)", nativeQuery = true)
    void deleteRelateByRcsIDandRoleIDs(String rcsID , List<String> roleIDs);


    @Modifying
    @Query(value = "delete from ds_role_resourcelist where resourceList_id " +
            " in(?1)", nativeQuery = true)
    void deleteRelateByRcsIDs(List<String> rcsIDs);


    @Query(value = "select cast(count(*) as char) as count from ds_role where id = ?1" , nativeQuery = true)
    String checkExist(String id);

    @Query(value = "select * from ds_role where rolename like %?1% and roleType = 2" , nativeQuery = true)
    List<Role> findRolesByProductionLineName(String productionLineName);
}
