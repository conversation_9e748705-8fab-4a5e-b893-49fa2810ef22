package iet.ustb.sf.vo.eo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;

/**
 * @Author: Dr.Monster
 * @Title: WarningInfoEO
 * @Date: 23/10/31 14:5518
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */
@Data
public class WarningInfoEO {

    //模块名称
    @Excel(name = "所属模块")
    String moduleName;

    //报警描述
    @Excel(name = "报警描述")
    String alertContent;

    @Excel(name = "报警时间" , exportFormat = "yyyy-MM-dd HH:mm:ss")
    Date createDateTime;

    @Excel(name = "处理时间" , exportFormat = "yyyy-MM-dd HH:mm:ss")
    Date dealTime;

    //区域名称
    @Excel(name = "区域名")
    String areaName;

    //设备名称
    @Excel(name = "设备名")
    String deviceName;

    //点位名称
    @Excel(name = "点位名")
    String pointName;

    //报警类型名称
    @Excel(name = "报警类型")
    String alertTypeName;

    //报警级别，1-一级，2-二级，3-三级，0-未评级
    @Excel(name = "报警等级")
    int alertLevel;

    //处置情况,0-未处理,1-已挂起,2-已处理,3-持续中
    int status;

    @Excel(name = "是否处置")
    String statusName;

    @Excel(name = "报警分析")
    String alertAnalysis;

    //处理建议
    @Excel(name = "处理意见")
    String alertAdvice;

    //处理人
    @Excel(name = "处理人")
    String dealUser;

    //是否误报，0-否，1-是
    Integer isFalse;

    @Excel(name = "是否误报")
    String isFalseName;

    @Excel(name = "误报处理反馈")
    String falseFeedBack;

    @Excel(name = "处理时长")
    String diffTime;
}
