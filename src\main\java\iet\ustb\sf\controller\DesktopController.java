package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.service.DesktopService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.controller
 * @title: DesktopController
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/2516:35
 */

@RestController
@RequestMapping("/desktop")
@Api(value = "桌面，包含桌面，图标，文件夹，小部件", tags = "桌面，包含桌面，图标，文件夹，小部件")
public class DesktopController {

    @Autowired
    DesktopService desktopService;


    @ResponseBody
    @PostMapping("/getDefaultDesktop")
    @ApiOperation(value = "获取默认桌面", notes = "获取默认桌面,{}", produces = "application/json")
    public AjaxJson getDefaultDesktop(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(desktopService.getDefaultDesktop(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/getDesktopByUserNo")
    @ApiOperation(value = "根据userNo获取桌面", notes = "根据userNo获取桌面,{\n" +
            "    \"userNo\":\"073694\",\n" +
            "}", produces = "application/json")
    public AjaxJson getDesktopByUserNo(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(desktopService.getDesktopByUserNo(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/getDesktopByIDAndUserNo")
    @ApiOperation(value = "根据ID和userNo获取桌面", notes = "根据ID和userNo获取桌面,{\n" +
            "    \"userNo\":\"073694\",\n" +
            "    \"desktopID\":\"\"\n" +
            "}", produces = "application/json")
    public AjaxJson getDesktopByIDAndUserNo(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(desktopService.getDesktopByIDAndUserNo(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/createDesktop")
    @ApiOperation(value = "新增桌面", notes = "新增桌面,{\"name\":\"AAA\",\"type\":\"1\",\"userNo\":\"073694\"}", produces = "application/json")
    public AjaxJson createDesktop(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(desktopService.createDesktop(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/deleteDesktop")
    @ApiOperation(value = "删除桌面", notes = "删除桌面,{\"deskID\":\"05dbd260-9cba-4263-ba2a-c95389a3cdee\"}", produces = "application/json")
    public AjaxJson deleteDesktop(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(desktopService.deleteDesktop(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/saveDesktopConfig")
    @ApiOperation(value = "保存桌面配置", notes = "保存桌面配置,{\n" +
            "    \"config\":\"\",\n" +
            "    \"userNo\":\"\",\n" +
            "    \"desktopID\":\"\"\n" +
            "}", produces = "application/json")
    public AjaxJson saveDesktopConfig(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(desktopService.saveDesktopConfig(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/getDesktopConfig")
    @ApiOperation(value = "获取桌面配置", notes = "获取桌面配置,{\n" +
            "    \"userNo\":\"\",\n" +
            "    \"desktopID\":\"\"\n" +
            "}", produces = "application/json")
    public AjaxJson getDesktopConfig(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(desktopService.getDesktopConfig(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/getDesktopConfigsByUserNo")
    @ApiOperation(value = "根据userNo获取桌面配置", notes = "根据userNo获取桌面配置,{\n" +
            "    \"userNo\":\"\",\n" +
            "}", produces = "application/json")
    public AjaxJson getDesktopConfigsByUserNO(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(desktopService.getDesktopConfigsByUserNo(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/saveDashConfig")
    @ApiOperation(value = "保存任务栏配置", notes = "保存任务栏配置,{\n" +
            "    \"config\":\"\",\n" +
            "    \"userNo\":\"\",\n" +
            "}", produces = "application/json")
    public AjaxJson saveDashConfig(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(desktopService.saveDashConfig(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/getDashConfig")
    @ApiOperation(value = "获取任务栏配置", notes = "获取任务栏配置,{\n" +
            "    \"id\":\"\",\n" +
            "    \"userNo\":\"\"\n" +
            "}", produces = "application/json")
    public AjaxJson getDashConfig(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(desktopService.getDashConfig(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }
}
