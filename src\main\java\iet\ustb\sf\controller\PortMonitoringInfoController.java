package iet.ustb.sf.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import iet.ustb.sf.service.PortMonitoringInfoService;
import iet.ustb.sf.vo.AjaxJson;
import iet.ustb.sf.vo.PortMonitoringInfo;
import iet.ustb.sf.vo.domain.IconImg;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 接口监控(PortMonitoringInfo)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-21 17:05:44
 */
@RestController
@RequestMapping("portMonitoringInfo")
public class PortMonitoringInfoController {
    /**
     * 服务对象
     */
    @Resource
    private PortMonitoringInfoService portMonitoringInfoService;


    /**
     * 分页查询接口监控信息
     * @param portMonitoringInfo
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/getPortMonitoringInfoByPage")
    public AjaxJson getPortMonitoringInfoByPage(@RequestBody PortMonitoringInfo portMonitoringInfo) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            IPage<PortMonitoringInfo> portMonitoringInfoList = portMonitoringInfoService.getPortMonitoringInfoByPage(portMonitoringInfo);
            ajaxJson.setData(portMonitoringInfoList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping(value = "/getPortMonitoringDetail")
    public AjaxJson getPortMonitoringDetail(@RequestBody PortMonitoringInfo portMonitoringInfo) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(portMonitoringInfoService.getPortMonitoringDetail(portMonitoringInfo));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


}

