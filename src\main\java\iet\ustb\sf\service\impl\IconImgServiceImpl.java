package iet.ustb.sf.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import iet.ustb.sf.config.MinioConfig;
import iet.ustb.sf.dao.IconImgDao;
import iet.ustb.sf.mapper.IconImgMapper;
import iet.ustb.sf.vo.CustomExceptionVo;
import iet.ustb.sf.vo.domain.IconImg;
import iet.ustb.sf.service.IconImgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * icon图标
 *
 * <AUTHOR>
 * @create 2022-08-15
 */
@Service
public class IconImgServiceImpl implements IconImgService {

    @Autowired
    private IconImgDao iconImgDao;

    @Autowired
    private IconImgMapper iconImgMapper;

    @Autowired
    private MinioConfig minioConfig;

    @Override
    public void uploadImg(MultipartFile[] files, Integer iconType) throws Exception {

        ArrayList<IconImg> iconImgList = new ArrayList<>();
        for (MultipartFile multipartFile : files) {
            IconImg iconImg = getIconImg(iconType, multipartFile);
            iconImgList.add(iconImg);

        }
        iconImgDao.saveAll(iconImgList);

    }

    /**
     * 上传图标
     * @param iconType
     * @param multipartFile
     * @return
     * @throws Exception
     */
    private IconImg getIconImg(Integer iconType, MultipartFile multipartFile) throws Exception {
        String originalFilename = multipartFile.getOriginalFilename();
        String contentType = multipartFile.getContentType();
        if (!contentType.contains("image")) {
            throw new CustomExceptionVo("400","请上传图片");
        }
        //上传图片到文件服务器
        String uploadUrl = minioConfig.putObject(multipartFile);

        // 先截取最后的文件名部分
        String encodedFileName = uploadUrl.substring(uploadUrl.lastIndexOf("/") + 1);
        // 解码
        String fileName = UriUtils.decode(encodedFileName, StandardCharsets.UTF_8);

        IconImg iconImg = new IconImg();
        iconImg.setName(fileName);
        iconImg.setIconType(iconType);
        iconImg.setResource(uploadUrl);
        iconImg.setFileName(originalFilename);
        return iconImg;
    }

    @Override
    public List<IconImg> findAllImg() {
        return iconImgDao.findAll().stream()
                // 按图标类型排序
                .sorted(Comparator.comparing(IconImg::getIconType))
                .collect(Collectors.toList());
    }

    @Override
    public IconImg findById(String id) throws Exception {
        return iconImgDao.findById(id).orElseThrow(() -> new Exception("id不存在"));
    }

    @Override
    public void deleteById(String id) throws Exception {
        iconImgDao.deleteById(id);
    }

    /**
     * 分页查询图标信息
     * @param iconImg
     * @return
     */
    @Override
    public IPage<IconImg> getIconImgByPage(IconImg iconImg) {

        Page<IconImg> page = new Page<>(iconImg.getPageIndex(), iconImg.getPageSize());
        return iconImgMapper.getIconImgInfo(page, iconImg);
    }

    /**
     * 更新图标信息
     * @param iconImg
     * @return
     */
    @Override
    public void updateIconImg(IconImg iconImg) {
        if(ObjectUtil.isNotEmpty(iconImg.getFile())){
            try {
                minioConfig.deleteObject(iconImg.getName());
                IconImg newIconImg = getIconImg(iconImg.getIconType(), iconImg.getFile());
                iconImg.setName(newIconImg.getName());
                iconImg.setResource(newIconImg.getResource());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        iconImgDao.save(iconImg);
    }
}
