package iet.ustb.sf.utils.constant;

/**
 * 枚举值常量存储
 * <AUTHOR>
 */
public class EnumConstant {

    /**
     * 数字常量：1
     */
    public static final Integer NUMBER_ONE = 1;

    /**
     * 用户编码前缀
     */
    public static final String USER_CODE = "USER";
    /**
     * 角色编码前缀
     */
    public static final String ROLE_CODE = "ROLE";
    /**
     * 菜单编码前缀
     */
    public static final String RES_CODE = "RES";
    /**
     * 组织编码前缀
     */
    public static final String ORG_CODE = "ORG";

    /**
     * 英文下划线
     */
    public static final String UNDERLINE = "_";

    /**
     * 连字符
     */
    public static final String HYPHEN = "-";


    /**
     * 本系统是否枚举值 0：否，1：是
     */
    public static final String IS_NO_FLAG_0 = "0";
    public static final String IS_NO_FLAG_1 = "1";

    /**
     * 本系统是否枚举值 0：否，1：是 数字版本
     */
    public static final Integer IS_NO_FLAG_NUM_0 = 0;
    public static final Integer IS_NO_FLAG_NUM_1 = 1;

    /**
     * 大数据平台，是否停用：1-可用,2-不可用
     */
    public static final String BD_IS_NO_FLAG_1 = "1";
    public static final String BD_IS_NO_FLAG_2 = "2";

    /**
     * 大数据平台接口返回编码，200：成功
     */
    public static final String BD_CODE_200 = "200";


    /**
     * 默认过期时间-分
     */
    public static final int EXPIRY_TIME_MINUTE = 60;

    /**
     * 数字-0
     */
    public static final Integer ZERO = 0;
    /**
     * 数字-1
     */
    public static final Integer ONE = 1;
    /**
     * 数字-2
     */
    public static final Integer TWO = 2;

    /**
     * 接口响应字段
     */
    public static final String UNKNOWN = "unkown";

    /**
     * 接口监控详情查询类型：1：请求数据，2：响应数据，3：错误数据
     */
    public static final Integer PORT_MONITORING_TYPE_1 = 1;

    public static final Integer PORT_MONITORING_TYPE_2 = 2;

    public static final Integer PORT_MONITORING_TYPE_3 = 3;
}
