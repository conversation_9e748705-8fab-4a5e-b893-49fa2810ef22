spring:
  cloud:
    consul:
      enabled: false
  redis:
    host: localhost
    port: 6379
    password:
    database: 1
  data:
    redis:
      repositories:
        enabled: true
  datasource:
    hikari:
      maximum-pool-size: 30
      max-lifetime: 300000
    url: *******************************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.jdbc.Driver
    test-on-borrow: false
    test-while-idel: true
    time-between-eviction-runs-millis: 3600000
    tomcat:
      max-wait: 10000
      max-active: 50

 #配置minio
minio:
  host: http://************:9000
  url: ${minio.host}/${minio.bucket}/
#  access-key: 3nhV3qxxKQJSkajPP9lV
#  secret-key: Q10B2uQbbnQyyrTWXuZqM8Ad1rRLWLULDv1REIsp
  access-key: root
  secret-key: 12345678
  bucket: public
server:
  port: 8081

#权限系统配置 - 开发环境
permission:
  system:
    base:
      url: http://*************:8285  # 开发环境权限系统地址
    timeout: 5000  # 开发环境超时时间
    cache:
      expire-minutes: 30  # 开发环境缓存过期时间
