spring:
  cloud:
    consul:
      enabled: false
  redis:
    host: **********
    port: 6379
    password:
    database: 3
  data:
    redis:
      repositories:
        enabled: true
  datasource:
    hikari:
      maximum-pool-size: 30
      max-lifetime: 300000
    url: *****************************************************************************************************************************************************************
    username: root
    password: Ngpds@123456
    driver-class-name: com.mysql.jdbc.Driver
    test-on-borrow: false
    test-while-idel: true
    time-between-eviction-runs-millis: 3600000
    tomcat:
      max-wait: 10000
      max-active: 50

 #配置minio
minio:
  host: http://**********:9050
  url: ${minio.host}/${minio.bucket}/
  access-key: KW5RLy7rJcb9GIpzIY9n
  secret-key: QpmV6u0xvyrog2BSvRioFyuqzIdFCJb3HNDb1oaS
  bucket: public
server:
  port: 8081

#权限系统配置 - 生产环境
permission:
  system:
    base:
      url: http://localhost:8285  # 生产环境权限系统地址，需要根据实际部署调整
    timeout: 3000  # 生产环境超时时间，更严格
    cache:
      expire-minutes: 60  # 生产环境缓存过期时间，更长以减少调用频率