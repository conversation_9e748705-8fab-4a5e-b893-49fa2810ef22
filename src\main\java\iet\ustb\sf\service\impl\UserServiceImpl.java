package iet.ustb.sf.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.microsoft.schemas.vml.STExt;
import iet.ustb.sf.client.DataPlatformClient;
import iet.ustb.sf.config.NacosConfig;
import iet.ustb.sf.dao.OrgDao;
import iet.ustb.sf.dao.RoleDao;
import iet.ustb.sf.dao.UserDao;
import iet.ustb.sf.mapper.DsRoleMapper;
import iet.ustb.sf.mapper.DuUserMapper;
import iet.ustb.sf.service.OrgService;
import iet.ustb.sf.utils.CodeGenerator;
import iet.ustb.sf.utils.constant.EnumConstant;
import iet.ustb.sf.vo.AjaxJson;
import iet.ustb.sf.vo.CustomExceptionVo;
import iet.ustb.sf.vo.domain.Org;
import iet.ustb.sf.vo.domain.Role;
import iet.ustb.sf.vo.domain.User;
import iet.ustb.sf.service.UserService;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.vo.PageVo;
import iet.ustb.sf.vo.UserVo;
import iet.ustb.sf.vo.feign.BDUserAndRoleVo;
import iet.ustb.sf.vo.feign.BDUserVo;
import io.micrometer.core.instrument.util.StringUtils;
import org.aspectj.weaver.loadtime.Aj;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.service.impl
 * @title: UserServiceImpl
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/1011:05
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    UserDao userDao;

    @Autowired
    RoleDao roleDao;

    @Autowired
    private OrgDao orgDao;

    @Autowired
    private DuUserMapper duUserMapper;

    @Autowired
    private DsRoleMapper dsRoleMapper;

    @Autowired
    private OrgServiceImpl orgServiceImpl;

    @Autowired
    private DataPlatformClient dataPlatformClient;

    @Autowired
    private NacosConfig nacosConfig;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public String doCreateUser(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return "";
        }

        User user = ToolsUtil.jsonObjectToEntity(jsonObject, User.class);
        // 默认密码123456
        user.setPassword("e10adc3949ba59abbe56e057f20f883e");
        // 未修改密码
        user.setIsModifiedPwd("N");
        // 非停用状态
        user.setStatus(EnumConstant.IS_NO_FLAG_0);
        // 非删除操作状态
        user.setOperStus("U");

        //新增用户编码
        user.setLoginId(CodeGenerator.generateUserCode());

        //判断账号是否存在，如果存在则告警
        checkUserNoExist(user);

        //调用数据中台接口将用户信息同步给数据中台
        if(EnumConstant.IS_NO_FLAG_1.equals(nacosConfig.getApiBd())){
            saveBDUser(user);
        }

        return doSaveUser(user);
    }

    /**
     * 调用数据中台接口将用户信息同步给数据中台
     * @param user
     */
    private void saveBDUser(User user) {
        BDUserVo bdUserVo = changeBDUser(user);
        AjaxJson result = dataPlatformClient.saveOrUpdateUser(bdUserVo);
        if(!EnumConstant.BD_CODE_200.equals(result.code)){
            throw new CustomExceptionVo(result.getCode(),result.getMessage());
        }
    }

    /**
     * 判断账号是否存在，如果存在则告警
     * @param user
     */
    private void checkUserNoExist(User user) {
        User checkUser = new User();
        checkUser.setUserNo(user.getUserNo());
        List<User> checkUserList = duUserMapper.getNoDelUserInfo(checkUser);
        if(CollUtil.isNotEmpty(checkUserList)){
            throw new CustomExceptionVo("400","该账号已存在，请重新编写新的账号！");
        }
    }

    @Override
    public String doSaveUser(User User) {
        return userDao.save(User).getId();
    }

    @Override
    public List<Map<String, String>> findUserOrgByUserNos(Set<String> userNos) {
        return userDao.findUserOrgByUserNos(userNos);
    }

    @Override
    public Map<String , List<UserVo>> findUserListByRoleIDs(JSONObject jsonObject) {
        if(ToolsUtil.isEmpty(jsonObject) || ToolsUtil.isEmpty(jsonObject.get("roleIDs"))){
            return null;
        }else {
            Map<String , List<UserVo>> result = new HashMap<>();
            List<String> roleIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("roleIDs") , String.class);
            if(ToolsUtil.isEmpty(roleIDs)){
                return null;
            }
            List<UserVo> userVoList;
            for (String roleID : roleIDs){
                userVoList = new ArrayList<>();
                Role role = roleDao.findById(roleID).get();
                userVoList = JSONArray.parseArray(JSON.toJSONString(role.getUserList()) , UserVo.class);
                result.put(roleID , userVoList);
            }
            return result;
        }
    }

    @Override
    public User findUserByUserNo(String userNo) {
        User user = userDao.findOneUserByUserNo(userNo);
        return user;
    }

    /**
     * 更新用户信息
     * @param jsonObject
     * @return
     */
    @Override
    public String doUpdaterUser(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return "";
        }
        User user = ToolsUtil.jsonObjectToEntity(jsonObject, User.class);

        //调用接口更新数据中台用户信息
        if(EnumConstant.IS_NO_FLAG_1.equals(nacosConfig.getApiBd())){
            saveBDUser(user);
        }

        return doSaveUser(user);
    }

    /**
     * 删除用户信息
     * @param jsonObject
     */
    @Override
    public void doDeleteUser(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject.get("id"))) {
            return;
        }
        String id = jsonObject.getString("id");
        User user = userDao.getById(id);
        user.setStatus("1");
        user.setOperStus("D");

        //调用接口删除数据中台用户信息
        if(EnumConstant.IS_NO_FLAG_1.equals(nacosConfig.getApiBd())){
            deleteBDUser(user);
        }

        doSaveUser(user);
    }

    /**
     * 调用数据中台接口删除用户信
     * @param user
     */
    private void deleteBDUser(User user) {
        BDUserVo bdUserVo = new BDUserVo();
        List<String> bdUserList = new ArrayList<>();
        bdUserList.add(user.getUserNo());

        bdUserVo.setUsers(bdUserList);
        AjaxJson result = dataPlatformClient.deleteUser(bdUserVo);
        if(!EnumConstant.BD_CODE_200.equals(result.code)){
            throw new CustomExceptionVo(result.getCode(),result.getMessage());
        }
    }

    /**
     * 大数据中台的用户字段数据转换
     * @param user
     * @return
     */
    private BDUserVo changeBDUser(User user) {
        BDUserVo bdUserVo = new BDUserVo();
        bdUserVo.setAccount(user.getUserNo());
        bdUserVo.setEmail(user.getEmail());
        bdUserVo.setTelephone(user.getMobPhone());
        if(EnumConstant.IS_NO_FLAG_0.equals(user.getStatus())){
            bdUserVo.setStatus(EnumConstant.BD_IS_NO_FLAG_1);
        } else if (EnumConstant.IS_NO_FLAG_1.equals(user.getStatus())) {
            bdUserVo.setStatus(EnumConstant.BD_IS_NO_FLAG_2);
        }
        bdUserVo.setDisplayName(user.getUserName());

        return bdUserVo;
    }

    /**
     * 分页查询用户信息
     * @param jsonObject
     * @return
     */
    @Override
    public Page<User> findAllUser(JSONObject jsonObject) {

        Pageable pageable = ToolsUtil.initPage(jsonObject);
        Map<String, Object> params = new HashMap<>();

        params.put("userNo", jsonObject.getString("userNo"));
        params.put("userName", jsonObject.getString("userName"));
        params.put("sex", jsonObject.getString("sex"));
        params.put("empCategory", jsonObject.getString("empCategory"));
        if(ObjectUtil.isNotEmpty(jsonObject.getString("orgCode"))){
            Object orgCodeObj = jsonObject.get("orgCode");
            List<String> orgCodes;
            if (orgCodeObj instanceof List<?>) {
                orgCodes = ((List<?>) orgCodeObj).stream()
                        .filter(Objects::nonNull)
                        .map(Object::toString)
                        .collect(Collectors.toList());
            } else {
                // 如果不是 List，返回空列表，避免 NPE
                orgCodes = new ArrayList<>();
            }
            List<Org> orgList = orgServiceImpl.getAllOrgs(orgCodes);
            List<String> orgCodeList = orgList.stream()
                    .map(Org::getOrgCode)
                    .collect(Collectors.toList());
            params.put("orgCode", orgCodeList);
        }

        List<User> userList = duUserMapper.findAllUser(params);
        int total = userList.size();

        // 分页
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), total);
        List<User> subList = userList.subList(start, end);

        return new PageImpl<>(subList, pageable, total);
    }

    @Override
    public User findOneUserByID(JSONObject jsonObject) {
        if (!ToolsUtil.isEmpty(jsonObject) && !ToolsUtil.isEmpty(jsonObject.getString("id"))) {
            return userDao.getById(jsonObject.getString("id"));
        } else {
            return null;
        }
    }

    @Override
    public User findOneUserByUserNo(JSONObject jsonObject) {
        if (!ToolsUtil.isEmpty(jsonObject) && !ToolsUtil.isEmpty(jsonObject.getString("userNo"))) {
            return userDao.findOneUserByUserNo(jsonObject.getString("userNo"));
        } else {
            return null;
        }
    }

    @Override
    public PageVo<User> filterUserSelected(JSONObject jsonObject) {

        return null;
    }

    /**
     * 用户分配角色
     * @param jsonObject
     * @return
     */
    @Override
    public String relateRole(JSONObject jsonObject) {

        String userID = jsonObject.getString("id");
        if (ToolsUtil.isEmpty(userID)) {
            return "";
        }

        User user = userDao.getById(userID);
        if (ToolsUtil.isEmpty(user)) {
            return "";
        }
        //新增角色集合
        List<String> addIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("addIDs"), String.class);
        List<String> addCodes = new ArrayList<>();
        //删除角色集合
        List<String> removeIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("removeIDs"), String.class);
        List<String> removeCodes = new ArrayList<>();

        List<Role> batchRoleList = new ArrayList<>();
        List<User> tmp = null;

        //新增的增加
        if (!ToolsUtil.isEmpty(addIDs)) {
            List<Role> addRoles = roleDao.findAllById(addIDs);
            for (Role item : addRoles) {
                if (ToolsUtil.isEmpty(item.getUserList())) {
                    tmp = new ArrayList<>();
                    tmp.add(user);
                    item.setUserList(tmp);
                } else {
                    List<User> newUserList = item.getUserList();
                    newUserList.add(user);
                    item.setUserList(newUserList);
                }
            }
            addCodes = addRoles.stream().map(Role::getRoleCode).collect(Collectors.toList());
            batchRoleList.addAll(addRoles);
        }

        //删除的从原来列表里移除
        if (!ToolsUtil.isEmpty(removeIDs)) {
            List<Role> removeRoles = roleDao.findAllById(removeIDs);
            for (Role item : removeRoles) {
                List<User> newUserList = item.getUserList();
                ToolsUtil.removeItemFormList(newUserList, user);
                item.setUserList(newUserList);
            }
            removeCodes = removeRoles.stream().map(Role::getRoleCode).collect(Collectors.toList());
            batchRoleList.addAll(removeRoles);
        }

        //调用中台接口，保存用户和角色的关联关系
        if(EnumConstant.IS_NO_FLAG_1.equals(nacosConfig.getApiBd())){
            saveUserRoles(user.getUserNo(), addCodes, removeCodes);
        }

        if (!ToolsUtil.isEmpty(batchRoleList)) {
            roleDao.saveAll(batchRoleList);
        }

        return user.getId();
    }

    /**
     * 调用中台接口，保存用户和角色的关联关系
     * @param userID
     * @param addCodes
     * @param removeCodes
     */
    private void saveUserRoles(String userID, List<String> addCodes, List<String> removeCodes) {
        //查询该用户现在所有的角色信息
        Role role = new Role();
        role.setUserId(userID);
        List<Role> roleList =  dsRoleMapper.getRoleInfo(role);
        List<String> roleIdList = roleList.stream().map(Role::getRoleCode).collect(Collectors.toList());
        roleIdList.addAll(addCodes);
        roleIdList.removeAll(removeCodes);
        BDUserAndRoleVo bdUserAndRoleVo = new BDUserAndRoleVo();
        bdUserAndRoleVo.setUser(ToolsUtil.isEmpty(userID) ? "" : userID);
        bdUserAndRoleVo.setRoles(roleIdList);
        AjaxJson result = dataPlatformClient.updateUserRoles(bdUserAndRoleVo);
        if(!EnumConstant.BD_CODE_200.equals(result.code)){
            throw new CustomExceptionVo(result.getCode(),result.getMessage());
        }
    }

    @Override
    public JSONObject findUserAndOrgByUserNo(JSONObject jsonObject) {

        JSONObject json = new JSONObject();
        String userNo = jsonObject.getString("userNo");

        if (StringUtils.isBlank(userNo)) return json;
        // 根据用户编号查询用户
        User user = userDao.findOneUserByUserNo(userNo);
        // 出参存取用户信息
        json.put("user", user);

        if (user == null) return json;
        // 初始化组织编号
        String orgCode = user.getOrgCode();
        JSONArray orgJsonArr = new JSONArray();
        // 遍历当前用户所在组织及以上所有组织
        // 组织层级不超过10级
        for (int i = 0; i < 10; i++) {
            JSONObject orgJsonObj = new JSONObject();
            // 根据组织编号查询组织信息
            Org org = orgDao.findByOrgCode(orgCode);

            // 出参存取当前组织信息
            if (i == 0) json.put("org", org);
            if (org == null) break;

            orgJsonObj.put("orgCode", org.getOrgCode());
            orgJsonObj.put("orgAllName", org.getOrgAllName());
            orgJsonObj.put("grade", i);
            orgJsonArr.add(orgJsonObj);

            String parentOrgCode = org.getParentOrgCode();
            orgCode = parentOrgCode;
            // 判断根目录下所有组织，当parentOrgCode=X，说明已经到根目录，故结束循环
            if ("X".equals(parentOrgCode)) {
                break;
            }
        }
        // json存取用户当前及以上所有组织
        json.put("aboveOrgGrades", orgJsonArr);

        return json;
    }

    @Override
    public JSONArray findUsersByOrgCode(JSONObject jsonObject) {
        String orgCode = jsonObject.getString("orgCode");
        List<User> userList = userDao.findUsersByOrgCode(orgCode);
        String jsonStr = JSON.toJSONString(userList, true);
        return JSONArray.parseArray(jsonStr);
    }


    @Override
    public List<User> findUserInSameOrgAndRole(JSONObject jsonObject) {
        String userNo = jsonObject.getString("userNo");
        return userDao.findUserInSameOrgAndRole(userNo);
    }

}
