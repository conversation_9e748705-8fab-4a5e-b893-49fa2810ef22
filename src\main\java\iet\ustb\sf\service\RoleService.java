package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.Role;
import iet.ustb.sf.vo.PageVo;
import iet.ustb.sf.vo.RoleVo;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.service
 * @title: RoleService
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/1011:04
 */
public interface RoleService {

    //新增
    String doCreateRole(JSONObject jsonObject);

    //修改
    String doUpdateRole(JSONObject jsonObject);

    //删除
    void doChangeStatus(JSONObject jsonObject);

    PageVo<RoleVo> findAllRole(JSONObject jsonObject);


    List<RoleVo> findAllRoleNoPage(JSONObject jsonObject);

    List<RoleVo> findRoleByUserID(JSONObject jsonObject);

    List<String> findRoleIDsByUserID(String userID);

    List<RoleVo> findRoleByRscID(JSONObject jsonObject);

    Role findOneRoleByID(JSONObject jsonObject);

    String relateResource(JSONObject jsonObject);

    String relateUser(JSONObject jsonObject);

    void exchangeRole(JSONObject jsonObject);

    List<Role> findRolesByProductionLineName(String productionLineName);

    void importRoles(int start, File file);

    Integer doDeleteRole (Role role);

}
