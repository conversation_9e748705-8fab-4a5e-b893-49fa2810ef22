package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.PageLogSecret;
import iet.ustb.sf.vo.eo.AccessNumEO;
import iet.ustb.sf.vo.eo.PageAccessNumEO;
import iet.ustb.sf.vo.eo.UserAccessNumEO;
import iet.ustb.sf.service.PageLogSecretService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 页面日志10
 *
 * <AUTHOR>
 * @create 2022-09-21
 */
@RestController
@RequestMapping("/pageLogSecret")
@Api(value = "页面日志10", tags = "页面日志10")
public class PageLogSecretController {

    @Autowired
    private PageLogSecretService pageLogSecretService;

    @ResponseBody
    @ApiOperation(value = "查询所有", notes = "查询所有")
    @PostMapping("/findAll")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<PageLogSecret> pageLogList = pageLogSecretService.findAll();
            ajaxJson.setData(pageLogList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ResponseBody
    @ApiOperation(value = "按多条件查找应用日志", notes = "{\"userNo\":\"023958\",\"orgCode\":\"1212\",\"loginTimeStart\": \"2022-09-22 09:16:00\", \"loginTimeEnd\": \"2022-09-22 10:13:30\"}")
    @PostMapping("/findByMultiCondition")
    public AjaxJson findByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<PageLogSecret> pageLogPages = pageLogSecretService.findByMultiCondition(jsonObject);
            ajaxJson.setData(pageLogPages);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ResponseBody
    @ApiOperation(value = "按登录时间查找不同组织/人员/应用访问次数",
            notes = "{\"loginTimeStart\":\"2022-10-31\",\"loginTimeEnd\":\"2022-11-04\",\"type\":\"org-2\"}")
    @PostMapping("/findAccessNumByLoginTime")
    public AjaxJson findAccessNumByLoginTime(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<AccessNumEO> list = pageLogSecretService.findAccessNumByLoginTime(jsonObject);
            ajaxJson.setData(list);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ApiOperation(value = "查找用户页面访问量列表",
            notes = "{\"loginTimeStart\":\"2023-03-20\",\"loginTimeEnd\":\"2023-03-23\",\"userNo\":\"011225\",\"orgCode\":\"*********\",\"handleUserFlag\":false}")
    @PostMapping("/findUserPageAccessList")
    public AjaxJson findUserPageAccessList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<UserAccessNumEO> list = pageLogSecretService.findUserPageAccessList(jsonObject);
            ajaxJson.setData(list);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ApiOperation(value = "按用户编号查找页面访问列表",
            notes = "{\"loginTimeStart\":\"2023-03-20\",\"loginTimeEnd\":\"2023-03-23\",\"userNo\":\"016971\"}")
    @PostMapping("/findPageAccessListByUserNo")
    public AjaxJson findPageAccessListByUserNo(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<Map<String, Object>> list = pageLogSecretService.findPageAccessListByUserNo(jsonObject);
            ajaxJson.setData(list);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ApiOperation(value = "查找每个页面访问列表",
            notes = "{\"loginTimeStart\":\"2023-03-20\",\"loginTimeEnd\":\"2023-03-23\",\"resourceId\":\"011225\",\"serviceNo\":\"iom\",\"pageName\":\"1#RH\"}")
    @PostMapping("/findEachPageAccessList")
    public AjaxJson findEachPageAccessList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<PageAccessNumEO> list = pageLogSecretService.findEachPageAccessList(jsonObject);
            ajaxJson.setData(list);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ApiOperation(value = "按资源id查找用户页面访问列表",
            notes = "{\"loginTimeStart\":\"2023-03-20\",\"loginTimeEnd\":\"2023-03-27\",\"resourceId\":\"022288fe-66f1-4935-87ee-e317b5789cbe\"}")
    @PostMapping("/findUserPageAccessListByResourceId")
    public AjaxJson findUserPageAccessListByResourceId(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<Map<String, Object>> list = pageLogSecretService.findUserPageAccessListByResourceId(jsonObject);
            ajaxJson.setData(list);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ApiOperation(value = "获取页面访问量看板",
            notes = "{\"loginTimeStart\":\"2023-03-20\",\"loginTimeEnd\":\"2023-03-23\"}")
    @PostMapping("/getPageLogLookBoard")
    public AjaxJson getPageLogLookBoard(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            JSONObject json = pageLogSecretService.getPageLogLookBoard(jsonObject);
            ajaxJson.setData(json);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ApiOperation(value = "按组织获取页面访问量看板", notes = "{\"loginTimeStart\":\"2023-03-20\",\"loginTimeEnd\":\"2023-03-23\",\"orgCode\":\"X50000000\"}")
    @PostMapping("/getPageLogLookBoardByOrg")
    public AjaxJson getPageLogLookBoardByOrg(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            JSONObject json = pageLogSecretService.getPageLogLookBoardByOrg(jsonObject);
            ajaxJson.setData(json);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ApiOperation(value = "获取领导访问记录排行", notes = "{\"loginTimeStart\":\"2023-03-20\",\"loginTimeEnd\":\"2023-03-23\"}")
    @PostMapping("/getLeaderAccess")
    public AjaxJson getLeaderAccess(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<Map<String, Object>> leaderAccess = pageLogSecretService.getLeaderAccess(jsonObject);
            ajaxJson.setData(leaderAccess);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportUserExcel")
    @ApiOperation(value = "导出用户访问Excel",
            notes = "入参：{\"loginTimeStart\":\"2023-03-20\",\"loginTimeEnd\":\"2023-03-23\",\"userNo\":\"011225\",\"orgCode\":\"*********\",\"handleUserFlag\":false}")
    public AjaxJson exportUserExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            pageLogSecretService.exportUserExcel(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportUserDetExcel")
    @ApiOperation(value = "导出用户详细访问Excel",
            notes = "入参：{\"loginTimeStart\":\"2023-03-20\",\"loginTimeEnd\":\"2023-03-23\",\"userNo\":\"011225\",\"orgCode\":\"*********\",\"handleUserFlag\":false}")
    public AjaxJson exportUserDetExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            pageLogSecretService.exportUserDetExcel(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportPageExcel")
    @ApiOperation(value = "导出页面访问Excel",
            notes = "入参：{\"loginTimeStart\":\"2023-03-20\",\"loginTimeEnd\":\"2023-03-23\",\"resourceId\":\"011225\",\"serviceNo\":\"iom\",\"pageName\":\"1#RH\"}")
    public AjaxJson exportPageExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            pageLogSecretService.exportPageExcel(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportWord")
    @ApiOperation(value = "导出Word",
            notes = "入参：{\"loginTimeStart\": \"2022-10-31\",\"loginTimeEnd\": \"2022-11-04\"}")
    public AjaxJson exportWord(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            JSONObject json = pageLogSecretService.exportWord(jsonObject);
            ajaxJson.setData(json);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ResponseBody
    @ApiOperation(value = "保存", produces = "application/json")
    @PostMapping("/save")
    public AjaxJson save(@RequestBody @Valid PageLogSecret PageLogSecret, HttpServletRequest request) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            // 保存
            pageLogSecretService.save(PageLogSecret, request);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ApiOperation(value = "获取owner主档", notes = "{\"userNo\":\"022426\",{\"loginTimeStart\": \"2023-04-01\",\"loginTimeEnd\": \"2023-04-25\"}}")
    @PostMapping("/getMainHandleUser")
    public AjaxJson getMainHandleUser(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<Map<String, Object>> list = pageLogSecretService.getMainHandleUser(jsonObject);
            ajaxJson.setData(list);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ApiOperation(value = "获取owner明细档 ", notes = "{\"userNo\":\"022426\",\"loginTimeStart\":\"2023-03-20\",\"loginTimeEnd\":\"2023-04-08\"}")
    @PostMapping("/getDetailHandleUser")
    public AjaxJson getDetailHandleUser(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<Map<String, Object>> list = pageLogSecretService.getDetailHandleUser(jsonObject);
            ajaxJson.setData(list);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ResponseBody
    @ApiOperation(value = "按登录时间查找不同组织/人员/应用访问次数",
            notes = "{\"loginTimeStart\":\"2022-10-31\",\"loginTimeEnd\":\"2022-11-04\",\"type\":\"org-2\"}")
    @PostMapping("/findAccessNumByLoginTime2")
    public AjaxJson findAccessNumByLoginTime2(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<AccessNumEO> list = pageLogSecretService.findAccessNumByLoginTime2(jsonObject);
            ajaxJson.setData(list);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @ResponseBody
    @ApiOperation(value = "查询应用访问次数",
            notes = "{\"loginTimeStart\":\"2023-02-01\",\"loginTimeEnd\":\"2023-02-28\"}")
    @PostMapping("/findGroupAppByLoginTime")
    public AjaxJson findGroupAppByLoginTime(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<AccessNumEO> list = pageLogSecretService.findGroupAppByLoginTime(jsonObject);
            ajaxJson.setData(list);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportExcel")
    @ApiOperation(value = "导出Excel",
            notes = "入参：{\"loginTimeStart\":\"2022-10-31\",\"loginTimeEnd\":\"2022-11-04\"}")
    public AjaxJson exportExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            pageLogSecretService.exportExcel(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportLeaderAndOrgExcel")
    @ApiOperation(value = "导出Excel",
            notes = "入参：{\"loginTimeStart\":\"2023-08-01\",\"loginTimeEnd\":\"2023-08-31\"}")
    public AjaxJson exportLeaderAndOrgExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            pageLogSecretService.exportLeaderAndOrgExcel(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportWord2")
    @ApiOperation(value = "导出Word",
            notes = "入参：{\"loginTimeStart\": \"2022-10-31\",\"loginTimeEnd\": \"2022-11-04\" }")
    public AjaxJson exportWord2(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            JSONObject json = pageLogSecretService.exportWord2(jsonObject);
            ajaxJson.setData(json);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportUserPageDetExcel")
    @ApiOperation(value = "导出用户页面详细excel",
            notes = "入参：{\"loginTimeStart\":\"2022-10-31\",\"loginTimeEnd\":\"2022-11-04\"}")
    public AjaxJson exportUserPageDetExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            pageLogSecretService.exportUserPageDetExcel(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

}
