package iet.ustb.sf.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import iet.ustb.sf.vo.domain.BaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: Dr.Monster
 * @Title: WaringInfo
 * @Date: 23/10/31 10:5917
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */
@Data
public class WarningInfoVo extends BaseEntity {
    //区域ID
    
    String areaID;

    //区域名称
    
    String areaName;

    //点位ID
    
    String pointID;

    //点位名称
    
    String pointName;

    //设备ID
    
    String deviceID;

    //设备名称
    
    String deviceName;

    //报警内容
    String alertContent;

    //处理建议
    String alertAdvice;

    //诊断信息
    String diagnosticMessage;

    //模块名称
    
    String moduleName;

    //模块代码
    
    String moduleCode;

    //报警级别，1-一级，2-二级，3-三级，0-未评级
    
    int alertLevel;

    //处置情况,0-未处理,1-已挂起,2-已处理,3-持续中
    
    int status = 0;

    //处理人
    
    String dealUser;

    //人工建议
    String humanAdvice;

    //处理时间
    
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date dealTime;

    //是否确认,0-未确认，1-确认
    
    int isConfirm;

    //报警分析
    String alertAnalysis;

    //报警类型名称
    String alertTypeName;

    //报警值
    
    Float alertValue;

    //备注
    String remark = "";

    //0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测
    
    Integer warningType;

    //消息ID
    
    String msgID;

    //是否是误报警
    
    Integer isFalse;

    //报警规则ID
    
    String warningRuleID;

    //角色ID，多个角色用英文逗号隔开
    
    String roleID;
}



