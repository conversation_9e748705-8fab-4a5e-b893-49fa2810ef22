package iet.ustb.sf.vo;

import lombok.Data;

/**
 * 自定义前端返回数据实体
 * <AUTHOR>
 */
@Data
public class ErrorResponseVo {

    //错误编码
    private String code;
    //是否成功
    private boolean success;
    //错误信息
    private String message;

    public ErrorResponseVo(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public ErrorResponseVo(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
