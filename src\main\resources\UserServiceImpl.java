package steel.service.impl;

import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import common.domain.R;
import common.exception.BadRequestException;
import common.exception.ForbiddenException;
import common.exception.UnauthorizedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import steel.config.JwtProperties;
import steel.domain.dto.LoginFormDTO;
import steel.domain.po.User;
import steel.domain.vo.UserLoginVO;
import steel.enums.UserStatus;
import steel.mapper.UserMapper;
import steel.service.IUserService;
import steel.utils.HttpUtil;
import steel.utils.JwtTool;

import javax.servlet.http.HttpSession;
import java.util.*;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    private final PasswordEncoder passwordEncoder;

    private final JwtTool jwtTool;

    private final JwtProperties jwtProperties;

    @Value("${ziteng.token}")
    private String tokenUrl;

    @Value("${client_id}")
    private String clientId;
    @Value("${client_secret}")
    private String clientSecret;

    @Value("${ziteng.getUserInfo}")
    private String getUserInfoUrl;

    @Autowired
    RestTemplate restTemplate;

    private static final String CLIENT_ID = "NSG67oEBdi";
    private static final String CLIENT_SECRET = "aa4d6ff8-7839-4c5f-86af-2867fca82f79";
    private static final String REDIRECT_URI = "http://172.25.21.155/platform";
    private static final String AUTHORIZE_URL = "https://sso.nisco.cn/profile/oauth2/authorize";
    private static final String TOKEN_URL = "https://sso.nisco.cn/profile/oauth2/accessToken";
    private static final String PROFILE_URL = "https://sso.nisco.cn/profile/oauth2/profile";
    private static final String APP_KEY = "93b599864ef9d02dd79c1a6cd0255cd8";

    @Override
    public UserLoginVO login(LoginFormDTO loginDTO) {
        // 1.数据校验
        String username = loginDTO.getUsername();
        String password = loginDTO.getPassword();
        // 2.根据用户名或手机号查询
        User user = lambdaQuery().eq(User::getUsername, username).one();
        Assert.notNull(user, "用户名错误");
        // 3.校验是否禁用
        if (user.getStatus() == UserStatus.FROZEN) {
            throw new ForbiddenException("用户被冻结");
        }
        // 4.校验密码
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new BadRequestException("用户名或密码错误");
        }
        // 5.生成TOKEN
        String token = jwtTool.createToken(user.getId(), jwtProperties.getTokenTTL());
        // 6.封装VO返回
        UserLoginVO vo = new UserLoginVO();
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        vo.setToken(token);
        vo.setRole(user.getRole());
        vo.setTeam(user.getTeam());
        return vo;
    }

    @Override
    public UserLoginVO logout() {
        return null;
    }

    @Override
    public UserLoginVO getUserInfo(String code) {
        //注意要使用MultiValueMap
        MultiValueMap<String, String> paramsMap = new LinkedMultiValueMap<>();
        paramsMap.add("client_id", clientId);
        paramsMap.add("client_secret", clientSecret);
        paramsMap.add("grant_type", "authorization_code");
        paramsMap.add("code", code);
        log.info("getUserInfo --> paramsMap:{}", paramsMap);

        String resultStr = HttpUtil.SSOPOST(paramsMap, tokenUrl, restTemplate);
        JSONObject resultData = JSONObject.parseObject(resultStr);

        log.info("getUserInfo --> resultData:{}", resultData);

        String id_token = resultData.getString("id_token");

        MultiValueMap<String, String> paramsMap2 = new LinkedMultiValueMap<>();
        paramsMap2.add("access_token", id_token);

        log.info("getUserInfo --> paramsMap2:{}", paramsMap2);
        String resultStr2 = HttpUtil.SSOPOST(paramsMap2, getUserInfoUrl, restTemplate);

        JSONObject resultData2 = JSONObject.parseObject(resultStr2);
        log.info("getUserInfo --> resultData2:{}", resultData2);

        String account = resultData2.getString("account");
        log.info("getUserInfo --> account:{}", account);
        User user = lambdaQuery().eq(User::getAccount, account).one();
        if (user == null) {
            throw new UnauthorizedException("无效的token");
        }

        // 5.生成TOKEN
        String token = jwtTool.createToken(user.getId(), jwtProperties.getTokenTTL());
        // 6.封装VO返回
        UserLoginVO vo = new UserLoginVO();
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        vo.setToken(token);
        vo.setRole(user.getRole());
        vo.setTeam(user.getTeam());
        return vo;
    }

    @Override
    public R callback(String code) {
        log.info("code:{}", code);
        if (code==null){
            return R.error("code不能为空");
        }
        // 获取 Access Token
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonceStr = UUID.randomUUID().toString().replace("-", "");

        params.add("client_id", CLIENT_ID);
        params.add("client_secret", CLIENT_SECRET);
        params.add("redirect_uri", REDIRECT_URI);
        params.add("code", code);
        params.add("grant_type", "authorization_code");
        params.add("oauth_timestamp", timestamp);
        params.add("nonce_str", nonceStr);

        String sign = getSign(params.toSingleValueMap(), APP_KEY+CLIENT_SECRET);

        params.add("sign", sign);
        log.info("sign:{}",sign);

        // 设置 Content-Type 为 application/x-www-form-urlencoded
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        log.info("params:{}",params);

        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(params, headers);

        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = null;
        try {
            response = restTemplate.postForEntity(TOKEN_URL, request, String.class);
        } catch (RestClientException e) {
            throw new RuntimeException("请求token信息失败：" + e.getMessage(), e);
        }

        // 输出响应
        log.info("response:{}", response);
        JSONObject jsonObject= JSON.parseObject(response.getBody());
        Map<String, Object> tokenMap = jsonObject;
        log.info("tokenMap:{}", tokenMap);
        if (!"SUCCESS".equals(tokenMap.get("msg"))) {
            return R.error("请求token信息msg状态码不是SUCCESS,请求失败");
        }

        String accessToken = (String) tokenMap.get("access_token");
        log.info("accessToken:{}", accessToken);
        // 获取用户信息
        MultiValueMap<String, Object> userParams = new LinkedMultiValueMap<>();
        String userTimestamp = String.valueOf(System.currentTimeMillis());
        String userNonce = UUID.randomUUID().toString().replace("-", "");
        userParams.add("client_secret", CLIENT_SECRET);
        userParams.add("client_id", CLIENT_ID);
        userParams.add("nonce_str", userNonce);
        userParams.add("access_token", accessToken);
        userParams.add("oauth_timestamp", userTimestamp);
        String userSign = getSign(userParams.toSingleValueMap(), APP_KEY+CLIENT_SECRET);
        userParams.add("sign", userSign);
        log.info("userParams:{}",userParams);
        HttpEntity<MultiValueMap<String, Object>> request1 = new HttpEntity<>(userParams, headers);
        ResponseEntity<String> userResponse = null;
        try {
            userResponse = restTemplate.postForEntity(PROFILE_URL, request1, String.class);
        } catch (RestClientException e) {
            throw new RuntimeException("请求用户信息失败：" + e.getMessage(), e);
        }
        System.out.println(response.getBody());
        log.info("userResponse:{}", userResponse);
        JSONObject jsonObjectuser= JSON.parseObject(userResponse.getBody());
        Map<String, Object> map = jsonObjectuser;
        log.info("usermap:{}", map);
        if (!"SUCCESS".equals(map.get("msg"))) {
            return R.error("请求用户信息msg状态码不是SUCCESS,请求失败");
        }
        // 获取用户信息和数据库对比
        User user = this.lambdaQuery().eq(User::getUsername, map.get("id")).one();
        if (user == null) {
            return R.error("工号不存在");
        }
        log.info("map:{}", map);

        //生成TOKEN
        String token = jwtTool.createToken(user.getId(), jwtProperties.getTokenTTL());
        //封装VO返回
        UserLoginVO vo = new UserLoginVO();
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        vo.setToken(token);
        vo.setRole(user.getRole());
        vo.setTeam(user.getTeam());
        return R.ok(vo);
    }

    private static String getSign(Map<String, Object> params, String secret) {
        String sign = "";
        StringBuilder sb = new StringBuilder();
        // 排序
        Set<String> keyset = params.keySet();
        TreeSet<String> sortSet = new TreeSet<String>();
        sortSet.addAll(keyset);
        Iterator<String> it = sortSet.iterator();
        // 加密字符串
        while (it.hasNext()) {
            String key = it.next();
            String value = params.get(key).toString();
            sb.append(key).append(value);
        }
        sb.append("appkey").append(secret);
        try {
            sign = md5(sb.toString()).toUpperCase();
        } catch (Exception e) {
        }
        return sign;
    }

    /**
     * MD5加密
     */
    public static String md5(String s) {
        return new MD5().digestHex(s);
    }

}
