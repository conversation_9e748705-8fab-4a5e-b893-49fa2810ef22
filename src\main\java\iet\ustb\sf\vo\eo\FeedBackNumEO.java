package iet.ustb.sf.vo.eo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 问题反馈数量
 *
 * <AUTHOR>
 * @create 2022-11-03
 * @see Serializable
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FeedBackNumEO implements Serializable {

    /**
     * 工号
     */
    @Excel(name = "工号",width = 20)
    private String userNo;
    /**
     * 姓名
     */
    @Excel(name = "姓名",width = 20)
    private String userName;
    /**
     * 组织编号
     */
    @Excel(name = "组织编号", width = 20)
    private String orgCode;
    /**
     * 组织名称
     */
    @Excel(name = "组织名称", width = 30)
    private String orgAllName;
    /**
     * 总数
     */
    @Excel(name = "总数", type = 10)
    private Long totalNum;
    /**
     * 已完成数
     */
    @Excel(name = "已完成数", type = 10)
    private Long finishedNum;
    /**
     * 未完成数
     */
    @Excel(name = "未完成数", type = 10)
    private Long unfinishedNum;
}
