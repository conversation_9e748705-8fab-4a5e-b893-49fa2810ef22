package iet.ustb.sf.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.internal.StringUtil;
import iet.ustb.sf.client.DataPlatformClient;
import iet.ustb.sf.config.NacosConfig;
import iet.ustb.sf.dao.ResourceDao;
import iet.ustb.sf.dao.RoleDao;
import iet.ustb.sf.dao.UserDao;
import iet.ustb.sf.mapper.DsRoleMapper;
import iet.ustb.sf.utils.CodeGenerator;
import iet.ustb.sf.utils.constant.EnumConstant;
import iet.ustb.sf.vo.AjaxJson;
import iet.ustb.sf.vo.CustomExceptionVo;
import iet.ustb.sf.vo.domain.Resource;
import iet.ustb.sf.vo.domain.Role;
import iet.ustb.sf.vo.domain.User;
import iet.ustb.sf.utils.enumVo.ModuleInfoEnumVo;
import iet.ustb.sf.service.RoleService;
import iet.ustb.sf.service.UserService;
import iet.ustb.sf.utils.PoiUtil;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.vo.PageVo;
import iet.ustb.sf.vo.RoleVo;
import iet.ustb.sf.vo.feign.BDRoleAndMenuVo;
import iet.ustb.sf.vo.feign.BDRoleVo;
import iet.ustb.sf.vo.feign.BDUserAndRoleVo;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.File;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.service.impl
 * @title: RoleServiceImpl
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/1011:04
 */
@Service
@RefreshScope
public class RoleServiceImpl implements RoleService {

    @Autowired
    private RoleDao roleDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private ResourceDao resourceDao;

    @Autowired
    private UserService userService;

    @Autowired
    private DsRoleMapper dsRoleMapper;

    @Autowired
    private NacosConfig nacosConfig;

    @Autowired
    DataPlatformClient dataPlatformClient;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * 新增角色
     * @param jsonObject
     * @return
     */
    @Override
    public String doCreateRole(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return "";
        }
        Role role = ToolsUtil.jsonObjectToEntity(jsonObject, Role.class);
        role.setIsNewRole(EnumConstant.IS_NO_FLAG_NUM_1);

        //新增角色编码
        role.setRoleCode(CodeGenerator.generateRoleCode());

        //调用数据中台接口保存角色信息
        if(EnumConstant.IS_NO_FLAG_1.equals(nacosConfig.getApiBd())){
            saveBDRole(role);
        }

        return doSaveRole(role);
    }

    /**
     * 调用数据中台接口保存角色信息
     * @param role
     */
    private void saveBDRole(Role role) {
        BDRoleVo bdRoleVo = new BDRoleVo();
        bdRoleVo.setCode(role.getRoleCode());
        bdRoleVo.setName(role.getRoleName());
        bdRoleVo.setDesc(role.getDescription());

        AjaxJson result = dataPlatformClient.saveOrUpdateRole(bdRoleVo);
        if(!EnumConstant.BD_CODE_200.equals(result.getCode())){
            throw new CustomExceptionVo(result.getCode(),result.getMessage());
        }
    }

    public String doSaveRole(Role role) {
        return roleDao.save(role).getId();
    }

    /**
     * 修改角色
     * @param jsonObject
     * @return
     */
    @Override
    public String doUpdateRole(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject)) {
            return "";
        }
        Role role = ToolsUtil.jsonObjectToEntity(jsonObject, Role.class);

        //调用数据中台接口保存角色信息
        if (EnumConstant.IS_NO_FLAG_1.equals(nacosConfig.getApiBd())) {
            saveBDRole(role);
        }

        Role oldRole = roleDao.getById(role.getId());
        role.setUserList(oldRole.getUserList());
        role.setResourceList(oldRole.getResourceList());
        return doSaveRole(role);
    }

    @Override
    public void doChangeStatus(JSONObject jsonObject) {
        if (ToolsUtil.isEmpty(jsonObject.get("id"))) {
            return;
        }
        String id = jsonObject.getString("id");
        Role role = roleDao.getById(id);
        int status = jsonObject.getIntValue("status");
        role.setStatus(status);
        doSaveRole(role);
    }


    @Override
    public PageVo<RoleVo> findAllRole(JSONObject jsonObject) {


        Session session = entityManager.unwrap(Session.class);

        PageVo<RoleVo> pageVo = new PageVo();

        int pageSize = ToolsUtil.isEmpty(jsonObject.get("pageSize")) ? 10 : (int) jsonObject.get("pageSize");
        int pageIndex = ToolsUtil.isEmpty(jsonObject.get("pageIndex")) ? 1 : (int) jsonObject.get("pageIndex");


        StringBuilder selectSql = new StringBuilder("select id, description, isDef, roleCode, roleName, status, type, createDateTime, createUserNo, updateDateTime, updateUserNo, roleType, orgID, moduleName, moduleCode from DS_ROLE where 1=1 ");
        StringBuilder countSql = new StringBuilder("select count(*) from DS_ROLE where 1=1 ");

        for (Map.Entry<String, Object> item : jsonObject.entrySet()) {
            if ("pageIndex".equals(item.getKey())) {
                continue;
            }
            if ("pageSize".equals(item.getKey())) {
                continue;
            }


            if (!ToolsUtil.isEmpty(jsonObject)) {
                if (!ToolsUtil.isEmpty(jsonObject.get("roleCode"))) {
                    selectSql.append(" and roleCode like " + "'%" + jsonObject.get("roleCode") + "%'");
                    countSql.append(" and roleCode like " + "'%" + jsonObject.get("roleCode") + "%'");
                }

                if (!ToolsUtil.isEmpty(jsonObject.get("roleName"))) {
                    selectSql.append(" and roleName like " + "'%" + jsonObject.get("roleName") + "%'");
                    countSql.append(" and roleName like " + "'%" + jsonObject.get("roleName") + "%'");
                }

                if (!ToolsUtil.isEmpty(jsonObject.get("roleType"))) {
                    selectSql.append(" and roleType = " + jsonObject.get("roleType"));
                    countSql.append(" and roleType = " + jsonObject.get("roleType"));
                }

                if (!ToolsUtil.isEmpty(jsonObject.get("orgID"))) {
                    selectSql.append(" and orgID like " + "'%" + jsonObject.get("orgID") + "%'");
                    countSql.append(" and orgID like " + "'%" + jsonObject.get("orgID") + "%'");
                }

                if (!ToolsUtil.isEmpty(jsonObject.get("moduleName"))) {
                    selectSql.append(" and moduleName like " + "'%" + jsonObject.get("moduleName") + "%'");
                    countSql.append(" and moduleName like " + "'%" + jsonObject.get("moduleName") + "%'");
                }

                if (!ToolsUtil.isEmpty(jsonObject.get("moduleCode"))) {
                    selectSql.append(" and moduleCode = " + "'" + jsonObject.get("moduleCode") + "'");
                    countSql.append(" and moduleCode = " + "'" + jsonObject.get("moduleCode") + "'");
                }

                if (!ToolsUtil.isEmpty(jsonObject.get("productionLineName"))) {
                    selectSql.append(" and roleName like " + "'%" + jsonObject.get("productionLineName") + "%'");
                    countSql.append(" and roleName like " + "'%" + jsonObject.get("productionLineName") + "%'");
                }
            }
        }



        SQLQuery dataSqlQuery = session.createSQLQuery(ToolsUtil.buildPagerMySql(selectSql.toString() , pageIndex , pageSize));
        dataSqlQuery.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        SQLQuery countSqlQuery = session.createSQLQuery(countSql.toString());

        List<RoleVo> roleList = JSONArray.parseArray(JSON.toJSONString(dataSqlQuery.list()) , RoleVo.class);
        int count = ((BigInteger) countSqlQuery.getSingleResult()).intValue();


        Map<String , Integer> map = new HashMap<>();
        map.put("pageIndex" , pageIndex);
        map.put("pageIndex" , pageIndex - 1);
        map.put("pageSize" , pageSize);
        pageVo.setPageable(map);
        pageVo.setContent(roleList);
        pageVo.setTotalElements(count);

        return pageVo;
    }



    public List<RoleVo> findAllRoleNoPage(JSONObject jsonObject){
        Session session = entityManager.unwrap(Session.class);
        int pageSize = ToolsUtil.isEmpty(jsonObject.get("pageSize")) ? 10 : (int) jsonObject.get("pageSize");
        int pageIndex = ToolsUtil.isEmpty(jsonObject.get("pageIndex")) ? 1 : (int) jsonObject.get("pageIndex");


        StringBuilder selectSql = new StringBuilder("select id, description, isDef, roleCode, roleName, status, type, createDateTime, createUserNo, updateDateTime, updateUserNo, roleType, orgID, moduleName from DS_ROLE where 1=1 ");

        ToolsUtil.buildPagerMySql(selectSql.toString() , pageIndex , pageSize);


        SQLQuery dataSqlQuery = session.createSQLQuery(selectSql.toString());
        dataSqlQuery.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);

        List<RoleVo> roleList = JSONArray.parseArray(JSON.toJSONString(dataSqlQuery.list()) , RoleVo.class);
        return roleList;
    }


    @Override
    public List<RoleVo> findRoleByUserID(JSONObject jsonObject) {
        String userID = jsonObject.getString("userID");
        if (ToolsUtil.isEmpty(userID)) {
            return new ArrayList<>();
        }

        Session session = entityManager.unwrap(Session.class);

        StringBuilder selectSql = new StringBuilder("SELECT role.id, description, isDef, roleCode, roleName, status, type, createDateTime, createUserNo, updateDateTime, updateUserNo, roleType, orgID, moduleName \n" +
                "from ds_role as role\n" +
                "         join\n" +
                "     ds_role_userlist as rulist on\n" +
                "         role.id = rulist.Role_id\n" +
                "where rulist.userList_id = '" + userID + "'\n" +
                "  and role.status = 1");

        SQLQuery dataSqlQuery = session.createSQLQuery(selectSql.toString());
        dataSqlQuery.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);

        List<RoleVo> roleList = JSONArray.parseArray(JSON.toJSONString(dataSqlQuery.list()) , RoleVo.class);
        return roleList;
    }


    @Override
    public List<String> findRoleIDsByUserID(String userID ) {
        if (ToolsUtil.isEmpty(userID)) {
            return new ArrayList<>();
        }

        Session session = entityManager.unwrap(Session.class);

        StringBuilder selectSql = new StringBuilder("SELECT role.id \n" +
                "from ds_role as role\n" +
                "         join\n" +
                "     ds_role_userlist as rulist on\n" +
                "         role.id = rulist.Role_id\n" +
                "where rulist.userList_id = '" + userID + "'\n" +
                "  and role.status = 1");

        SQLQuery dataSqlQuery = session.createSQLQuery(selectSql.toString());
        dataSqlQuery.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);

        List<Map<String , String>> tempList = dataSqlQuery.list();

        List<String> roleIDList = tempList.stream().map(x -> x.get("id")).collect(Collectors.toList());

        return roleIDList;
    }


    @Override
    public List<RoleVo> findRoleByRscID(JSONObject jsonObject) {


        String rscID = jsonObject.getString("rscID");
        if (ToolsUtil.isEmpty(rscID)) {
            return new ArrayList<>();
        }

        Session session = entityManager.unwrap(Session.class);
        int pageSize = ToolsUtil.isEmpty(jsonObject.get("pageSize")) ? 10 : (int) jsonObject.get("pageSize");
        int pageIndex = ToolsUtil.isEmpty(jsonObject.get("pageIndex")) ? 1 : (int) jsonObject.get("pageIndex");

        StringBuilder selectSql = new StringBuilder("SELECT role.id, description , isDef, roleCode, roleName, status, type, createDateTime, createUserNo, updateDateTime, updateUserNo, roleType, orgID, moduleName \n" +
                "from ds_role as role\n" +
                "         join\n" +
                "     ds_role_resourcelist as rrlist on\n" +
                "         role.id = rrlist.Role_id\n" +
                "where rrlist.resourceList_id ='" + rscID + "'");
        ToolsUtil.buildPagerMySql(selectSql.toString() , pageIndex , pageSize);




        SQLQuery dataSqlQuery = session.createSQLQuery(selectSql.toString());
        dataSqlQuery.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);

        List<RoleVo> roleList = JSONArray.parseArray(JSON.toJSONString(dataSqlQuery.list()) , RoleVo.class);
        return roleList;
    }


    @Override
    public Role findOneRoleByID(JSONObject jsonObject) {
        return roleDao.getById(jsonObject.getString("id"));
    }


    //检查角色是否可用
    public boolean checkRoleAvailable(Role role) {

        return role.getStatus() == 1;

    }

    /**
     * 角色关联菜单
     * @param jsonObject
     * @return
     */
    @Override
    public String relateResource(JSONObject jsonObject) {

        String id = jsonObject.getString("id");

        if (ToolsUtil.isEmpty(id)) {
            return "";
        }

        Role role = roleDao.findById(id).get();
        if (ToolsUtil.isEmpty(role)) {
            return "";
        }

        if (!checkRoleAvailable(role)) {
            return "角色不可用";
        }

        Collection<String> resourceIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("resourceIDs"), String.class);


        List<Resource> resourceList = new ArrayList<>();
        if(!ToolsUtil.isEmpty(resourceIDs)){
            resourceList = resourceDao.findAllById(resourceIDs);
        }

        //调用中台接口保存角色和菜单的关联关系
        if(EnumConstant.IS_NO_FLAG_1.equals(nacosConfig.getApiBd())){
            saveBDRoleMenus(role, resourceList);
        }

        role.setResourceList(resourceList);
        return doSaveRole(role);
    }

    /**
     * 调用中台接口，保存角色和菜单的关联关系
     * @param role
     * @param resourceList
     */
    private void saveBDRoleMenus(Role role, List<Resource> resourceList) {
        List<Resource> roleResourceList = new ArrayList<>();
        resourceList.forEach(resource -> {
           if(nacosConfig.getApiBdName().equals(resource.getServiceName())){
               if(!nacosConfig.getApiBdMenu().equals(resource.getCode())){
                   roleResourceList.add(resource);
               }
           }
        });
        BDRoleAndMenuVo bdRoleAndMenuVo = new BDRoleAndMenuVo();
        bdRoleAndMenuVo.setRole(role.getRoleCode());
        List<String> menuCodes = roleResourceList.stream().map(Resource::getCode).collect(Collectors.toList());
        bdRoleAndMenuVo.setMenus(menuCodes);
        AjaxJson result = dataPlatformClient.updateRoleMenus(bdRoleAndMenuVo);
        if(!EnumConstant.BD_CODE_200.equals(result.getCode())){
            throw new CustomExceptionVo(result.getCode(),result.getMessage());
        }
    }

    @Override
    public String relateUser(JSONObject jsonObject) {

        String id = jsonObject.getString("id");

        if (ToolsUtil.isEmpty(id)) {
            return "";
        }

        Role role = roleDao.findById(id).get();
        if (ToolsUtil.isEmpty(role)) {
            return "";
        }

        if (!checkRoleAvailable(role)) {
            return "角色不可用";
        }

        List<String> userIDs = ToolsUtil.jsonObjectToEntityList(jsonObject.get("userIDs"), String.class);
        List<String> userNos = new ArrayList<>();

        List<User> userList = new ArrayList<>();
        if (!ToolsUtil.isEmpty(userIDs)) {
            userList = userDao.findAllById(userIDs);
        }

        //调用数据中台接口保存角色和用户的关联关系
        if(EnumConstant.IS_NO_FLAG_1.equals(nacosConfig.getApiBd())){
            userNos = userList.stream().map(User::getUserNo).collect(Collectors.toList());
            saveUsersByRole(role.getRoleCode(), userNos);
        }

        role.setUserList(userList);
        return doSaveRole(role);
    }

    /**
     * 调用数据中台接口保存角色和用户的关联关系
     * @param id
     * @param userIDs
     */
    private void saveUsersByRole(String id, List<String> userIDs) {
        BDUserAndRoleVo bdUserAndRoleVo = new BDUserAndRoleVo();
        bdUserAndRoleVo.setRole(id);
        bdUserAndRoleVo.setUsers(userIDs);
        AjaxJson result = dataPlatformClient.updateRoleUsers(bdUserAndRoleVo);
        if(!EnumConstant.BD_CODE_200.equals(result.code)){
            throw new CustomExceptionVo(result.getCode(),result.getMessage());
        }
    }

    @Override
    public void exchangeRole(JSONObject jsonObject) {

        //原用户ID
        String originUserID = jsonObject.getString("originUserID");

        List<String> originRoleIDs = findRoleIDsByUserID(originUserID);

        //互换的用户ID
        String exchangeUserID = jsonObject.getString("exchangeUserID");

        List<String> exchangeRoleIDs = findRoleIDsByUserID(exchangeUserID);

        JSONObject param1 = new JSONObject();
        param1.put("id" , originRoleIDs);
        param1.put("addIds" , exchangeRoleIDs);
        param1.put("removeIDs" , originRoleIDs);


        JSONObject param2 = new JSONObject();
        param2.put("id" , exchangeRoleIDs);
        param2.put("addIds" , originRoleIDs);
        param2.put("removeIDs" , exchangeRoleIDs);

        userService.relateRole(param1);
        userService.relateRole(param2);
    }

    @Override
    public List<Role> findRolesByProductionLineName(String productionLineName) {
        if(!ToolsUtil.isEmpty(productionLineName)){
            return roleDao.findRolesByProductionLineName(productionLineName);
        }
        return null;
    }

    @Override
    public void importRoles(int start, File file) {
        Map<String, String[][]> stringMap = PoiUtil.readExcelFile_1(file);
        Map<String , Role> roleMap = new HashMap<>();
        List<Role> roleList = new ArrayList<>();
        String[][] roleArray = stringMap.get("角色梳理表");
        Role role;
        for (int i = start; i < roleArray.length; i++) {
            role = new Role();
            if(ToolsUtil.isEmpty(roleArray[i][2]) || ToolsUtil.isEmpty(roleArray[i][3])){
                continue;
            }

            role.setRoleCode(roleArray[i][2]);
            role.setRoleName(roleArray[i][3]);
            switch (roleArray[i][4]){
                case "页面访问角色":{
                    role.setRoleType(1);
                    break;
                }
                case "页面操作角色":{
                    role.setRoleType(3);
                    break;
                }
                case "报警推送角色":{
                    role.setRoleType(2);
                    break;
                }
                default:{
                    role.setRoleType(1);
                    break;
                }
            }

            role.setDescription(roleArray[i][5]);

            role.setModuleName(roleArray[i][6]);

            role.setModuleCode(String.valueOf(ModuleInfoEnumVo.getCodeByName(roleArray[i][6])));

            roleMap.put(role.getRoleCode() , role);
        }

        String[][] userArray = stringMap.get("人员角色关系梳理表");

        List<User> userList;
        for (int i = start; i < userArray.length; i++) {
            if(ToolsUtil.isEmpty(userArray[i][1]) || ToolsUtil.isEmpty(userArray[i][2])
                    || ToolsUtil.isEmpty(userArray[i][3]) || ToolsUtil.isEmpty(userArray[i][4])){
                continue;
            }
            role = roleMap.get(userArray[i][1]);
            userList = role.getUserList();
            User user = userDao.findOneUserByUserNo(userArray[i][3]);
            if(ToolsUtil.isEmpty(userList)){
                userList = new ArrayList<>();
            }
            userList.add(user);
            role.setUserList(userList);
        }

        System.out.println(JSON.toJSONString(roleMap));
    }

    /**
     * 根据角色ID删除角色并级联删除角色和菜单的关系
     * @param role
     * @return
     */
    @Override
    public Integer doDeleteRole(Role role) {
        if(ObjectUtil.isEmpty(role) || StringUtil.isNullOrEmpty(role.getId())){
            throw new CustomExceptionVo("500","角色信息为空，不允许删除！");
        }
        Integer result = 0;

        //调用数据中台接口删除角色信息
        if(EnumConstant.IS_NO_FLAG_1.equals(nacosConfig.getApiBd())){
            BDRoleVo bdRoleVo = new BDRoleVo();
            List<String> roles = new ArrayList<>();
            roles.add(role.getRoleCode());
            bdRoleVo.setRoles(roles);
            AjaxJson bdResult = dataPlatformClient.deleteRole(bdRoleVo);
            if(!EnumConstant.BD_CODE_200.equals(bdResult.getCode()
            )){
                throw new CustomExceptionVo(bdResult.getCode(),bdResult.getMessage());
            }
        }

        //删除角色
        result = dsRoleMapper.deleteByID(role);
        //删除角色和菜单的关联关系
        dsRoleMapper.deleteResByRoleID(role);
        //删除角色和用户的关联关系
        dsRoleMapper.deleteUserByRoleID(role);

        return result;
    }
}
