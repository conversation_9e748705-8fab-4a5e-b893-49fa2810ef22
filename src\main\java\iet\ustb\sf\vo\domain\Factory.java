//package iet.ustb.sf.domain;
//
///**
// * 中板所有厂
// *
// * <AUTHOR>
// * @create 2022-07-16 16:28
// */
//public enum Factory {
//
//    PlateDivision("智慧桌面", "X50000000"),
//    DevelopTeam("开发项目组", "X11110000");
//
//    private final String name;
//    private final String orgCode;
//
//    Factory(String name, String orgCode) {
//        this.name = name;
//        this.orgCode = orgCode;
//    }
//
//    public String getName() {
//        return name;
//    }
//
//    public String getOrgCode() {
//        return orgCode;
//    }
//}
