package iet.ustb.sf.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 倒班规则表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-10-19 11:27:05
 */
@Data
public class ClassRulesVo {
	private Long id;
	/**
	 * 班次,0，1，2-大夜班，白班，小夜班
	 */
	private String classes;
	/**
	 * 开始
	 */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
	/**
	 * 结束
	 */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
	/**
	 * 班组,0，1，2，3-甲，乙，丙，丁
	 */
	private String team;
	/**
	 * 冗余1
	 */
	private String extra1;
	/**
	 * 冗余2
	 */
	private String extra2;

}
