package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.DesktopConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.dao
 * @title: DesktopConfigDao
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/2710:57
 */
public interface DesktopConfigDao extends JpaSpecificationExecutor<DesktopConfig>, JpaRepository<DesktopConfig, String> {

    @Query(value = "select distinct dc.* from desktopconfig dc join desktop d on dc.desktopID = d.id where dc.userNo = ?1 and dc.desktopID = ?2 and d.deleteTag = 0", nativeQuery = true)
    DesktopConfig findOneByUserNoAndDesktopID(String userNo, String desktopID);

    @Query(value = "select distinct dc.* from desktopconfig dc join desktop d on dc.desktopID = d.id where dc.userNo = ?1 and d.deleteTag = 0", nativeQuery = true)
    List<DesktopConfig> findConfigsByUserNo(String userNo);
}
