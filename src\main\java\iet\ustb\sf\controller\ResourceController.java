package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.service.ResourceService;
import iet.ustb.sf.vo.AjaxJson;
import iet.ustb.sf.vo.domain.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.controller
 * @title: DsResourceController
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/1010:49
 */

@RestController
@RequestMapping("/resource")
@Api(value = "资源(菜单)管理", tags = "资源(菜单)管理")
public class ResourceController {

    @Autowired
    ResourceService resourceService;

    //新增
    @ResponseBody
    @PostMapping("/doAddResource")
    @ApiOperation(value = "新增", notes = "新增,{\n" +
            "            \"code\": \"ESAC\",\n" +
            "            \"createdBy\": null,\n" +
            "            \"createdDate\": null,\n" +
            "            \"icon\": \"XCAEFCWA\",\n" +
            "            \"id\": \"000068d3-6417-4db9-80aa-e21333a52462\",\n" +
            "            \"isShow\": 1,\n" +
            "            \"lastModifiedBy\": null,\n" +
            "            \"lastModifiedDate\": null,\n" +
            "            \"name\": \"资源管理\",\n" +
            "            \"new\": false,\n" +
            "            \"parentId\": \"\",\n" +
            "            \"serviceName\": \"iet-iom-service\",\n" +
            "            \"sort\": 1,\n" +
            "            \"status\": 1,\n" +
            "            \"type\": \"1\",\n" +
            "            \"url\": \"/url\"\n" +
            "        }", produces = "application/json")
    public AjaxJson doAddResource(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.doCreateResource(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    //修改
    @ResponseBody
    @PostMapping("/doUpdateResource")
    @ApiOperation(value = "修改", notes = "修改,{\n" +
            "            \"code\": \"ESAC\",\n" +
            "            \"createdBy\": null,\n" +
            "            \"createdDate\": null,\n" +
            "            \"icon\": \"XCAEFCWA\",\n" +
            "            \"id\": \"000068d3-6417-4db9-80aa-e21333a52462\",\n" +
            "            \"isShow\": 1,\n" +
            "            \"lastModifiedBy\": null,\n" +
            "            \"lastModifiedDate\": null,\n" +
            "            \"name\": \"资源管理\",\n" +
            "            \"new\": false,\n" +
            "            \"parentId\": \"\",\n" +
            "            \"serviceName\": \"iet-iom-service\",\n" +
            "            \"sort\": 1,\n" +
            "            \"status\": 1,\n" +
            "            \"type\": \"1\",\n" +
            "            \"url\": \"/url\"\n" +
            "        }", produces = "application/json")
    public AjaxJson doUpdateResource(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.doUpdateResource(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    //删除
    @ResponseBody
    @PostMapping("/doDeleteResource")
    @ApiOperation(value = "删除(逻辑删除)", notes = "删除(逻辑删除),{\"id\":\"000068d3-6417-4db9-80aa-e21333a52462\"}", produces = "application/json")
    public AjaxJson doDeleteResource(@RequestBody Resource resource) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            resourceService.doDeleteResource(resource);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    //查询
    @ResponseBody
    @PostMapping("/findAllResource")
    @ApiOperation(value = "获取所有资源(菜单),不加type字段查全量,加type查对应类型,menu-菜单,button-按钮,plugin-控件", notes = "获取所有资源(菜单),不加type字段查全量,加type查对应类型,menu-菜单,button-按钮,plugin-控件,{\n" +
            "    \"pageIndex\":1,\n" +
            "    \"pageSize\":10\n" +
            "}", produces = "application/json")
    public AjaxJson findAllResource(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.findAllResource(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }



    @ResponseBody
    @PostMapping("/findAllOfResource")
    @ApiOperation(value = "获取所有资源(菜单),不限状态", notes = "{}", produces = "application/json")
    public AjaxJson findAllOfResource(@RequestBody Resource resource) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.findAllOfResource(resource));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/findAllResourceNoPage")
    @ApiOperation(value = "获取所有资源(菜单)无分页,userNo(用户编号,可不填),type(类型,可不填),menu-菜单,button-按钮,plugin-控件,serviceName(资源名,可不填)", notes = "获取所有资源(菜单)无分页,不加type字段查全量,加type查对应类型,menu-菜单,button-按钮,plugin-控件,{\n" +
            "    \"userNo\": \"021179\",\n" +
            "    \"type\": \"menu\",\n" +
            "    \"serviceName\": \"service\"\n" +
            "}", produces = "application/json")
    public AjaxJson findAllResourceNoPage(@RequestBody Resource resource) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.findAllResourceNoPage(resource));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }
//    按条件查询列表及对应图标
    @ResponseBody
    @PostMapping("/findAllResourceNoPageWithIcon")
    @ApiOperation(value = "获取所有资源(菜单)及图标无分页,userNo(用户编号,可不填),type(类型,可不填),menu-菜单,button-按钮,plugin-控件,serviceName(资源名,可不填)", notes = "获取所有资源(菜单)及图标无分页,不加type字段查全量,加type查对应类型,menu-菜单,button-按钮,plugin-控件,{\n" +
            "    \"userNo\": \"021179\",\n" +
            "    \"type\": \"menu\",\n" +
            "    \"serviceName\": \"service\"\n" +
            "}", produces = "application/json")
    public AjaxJson findAllResourceNoPageWithIcon(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.findAllResourceNoPageWithIcon(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/findResourceByParentID")
    @ApiOperation(value = "根据父级ID获取菜单树", notes = "根据父级ID获取菜单树,{\"parentID\":\"000068d3-6417-4db9-80aa-e21333a52462\"}", produces = "application/json")
    public AjaxJson findResourceByParentID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.findResourceByParentID(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/findOneResourceByID")
    @ApiOperation(value = "根据ID获取菜单", notes = "根据ID获取菜单,{\"id\":\"000068d3-6417-4db9-80aa-e21333a52462\"}", produces = "application/json")
    public AjaxJson findOneResourceByID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.findOneResourceByID(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/filterResourceSelected")
    @ApiOperation(value = "根据角色过滤已选择的菜单", notes = "根据角色过滤已选择的菜单,{\"roleID\":\"000068d3-6417-4db9-80aa-e21333a52462\"}", produces = "application/json")
    public AjaxJson filterResourceSelected(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.filterResourceSelected(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/relateRole")
    @ApiOperation(value = "关联角色", notes = "关联角色,{\n" +
            "    \"id\": \"60139d1e-7692-4719-bd7f-ec14fee831dx\",\n" +
            "    \"addRoleIDs\": [\n" +
            "        \"111\",\n" +
            "        \"222\",\n" +
            "        \"333\"\n" +
            "    ],\n" +
            "    \"deleteRoleIDs\": [\n" +
            "        \"444\",\n" +
            "        \"555\",\n" +
            "        \"666\"\n" +
            "    ]\n" +
            "}", produces = "application/json")
    public AjaxJson relateRole(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.relateRole(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/findResourceByUserNoAndServiceNameAndType")
    @ApiOperation(value = "根据userNo,type,serviceName获取菜单", notes = "根据userNo,type,serviceName获取菜单,{\n" +
            "    \"userNo\": \"021179\",\n" +
            "    \"type\": \"menu\",\n" +
            "    \"serviceName\": \"service\"\n" +
            "}", produces = "application/json")
    public AjaxJson findResourceByUserNoAndServiceNameAndType(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.findResourceByUserNoAndServiceNameAndType(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/findResourceByUserNo")
    @ApiOperation(value = "根据userNo获取菜单", notes = "根据userNo获取菜单,{\n" +
            "    \"userNo\": \"021179\"\n" +
            "}", produces = "application/json")
    public AjaxJson findResourceByUserNo(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.findResourceByUserNoAndServiceNameAndType(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/findRootResource")
    @ApiOperation(value = "根据userNo,type,serviceName获取根菜单", notes = "根据userNo,type,serviceName获取根菜单,{\n" +
            "    \"userNo\": \"021179\",\n" +
            "    \"type\": \"menu\",\n" +
            "    \"serviceName\": \"service\"\n" +
            "}", produces = "application/json")
    public AjaxJson findRootResource(@RequestBody Resource resource) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.findRootResource(resource));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/batchUpdateResource")
    @ApiOperation(value = "批量更新菜单", notes = "批量更新菜单,{\n" +
            "    \"rscList\": [\n" +
            "        {\n" +
            "            \"code\": \"1\",\n" +
            "            \"createDateTime\": \"2022-06-23 17:33:12\",\n" +
            "            \"createUserNo\": \"023958\",\n" +
            "            \"createdBy\": \"023958\",\n" +
            "            \"createdDate\": null,\n" +
            "            \"icon\": \"icon-gaoxiaozhuxuejin\",\n" +
            "            \"id\": \"0b668052-3127-4f68-872d-30a76b7d2db2\",\n" +
            "            \"isShow\": 1,\n" +
            "            \"isSelected\": \"0\",\n" +
            "            \"lastModifiedBy\": \"023958\",\n" +
            "            \"lastModifiedDate\": null,\n" +
            "            \"name\": \"测试页面1-4\",\n" +
            "            \"new\": false,\n" +
            "            \"parentId\": \"ede612ef-061e-44c4-bf22-80635380a2d7\",\n" +
            "            \"serviceName\": \"idm\",\n" +
            "            \"sort\": 1,\n" +
            "            \"status\": 1,\n" +
            "            \"type\": \"menu\",\n" +
            "            \"updateDateTime\": \"2022-06-28 09:20:56\",\n" +
            "            \"updateUserNo\": \"023958\",\n" +
            "            \"url\": \"/\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"code\": \"1\",\n" +
            "            \"createDateTime\": \"2022-06-23 17:16:17\",\n" +
            "            \"createUserNo\": \"023958\",\n" +
            "            \"createdBy\": \"023958\",\n" +
            "            \"createdDate\": null,\n" +
            "            \"icon\": \"icon-zizhuzigerending\",\n" +
            "            \"id\": \"0e952353-7b37-44a7-b117-78b0f1daacfa\",\n" +
            "            \"isShow\": 1,\n" +
            "            \"isSelected\": \"0\",\n" +
            "            \"lastModifiedBy\": null,\n" +
            "            \"lastModifiedDate\": null,\n" +
            "            \"name\": \"测试页面1-1\",\n" +
            "            \"new\": false,\n" +
            "            \"parentId\": \"ede612ef-061e-44c4-bf22-80635380a2d7\",\n" +
            "            \"serviceName\": \"1\",\n" +
            "            \"sort\": 1,\n" +
            "            \"status\": 1,\n" +
            "            \"type\": \"menu\",\n" +
            "            \"url\": \"1\"\n" +
            "        }\n" +
            "    ]\n" +
            "}", produces = "application/json")
    public AjaxJson batchUpdateResource(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            resourceService.batchUpdateResource(jsonObject);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    /**
     * 根据菜单信息查询相关所有菜单数据
     * @param resource
     * @return
     */
    @ResponseBody
    @PostMapping("/getResourceInfo")
    public AjaxJson getResourceInfo(@RequestBody Resource resource) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.getResourceInfo(resource));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }

    /**
     * 根据菜单信息查询相关所有菜单数据，并以树结构将数据返回
     * @param resource
     * @return
     */
    @ResponseBody
    @PostMapping("/getResourceTree")
    public AjaxJson getResourceTree(@RequestBody Resource resource) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(resourceService.getResourceTree(resource));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }
}
