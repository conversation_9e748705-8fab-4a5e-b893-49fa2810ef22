package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.WarningEventBody;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: <PERSON>.<PERSON>
 * @Slogan: The never-compromising angry youth.
 * @PackageName: iet.ustb.sf.dao
 * @Date: 2022/08/15/18:40
 * @Description:
 */
public interface WarningEventBodyDao extends JpaSpecificationExecutor<WarningEventBody>, JpaRepository<WarningEventBody, Serializable> {
}
