package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.AppEval;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;

public interface AppEvalDao extends JpaSpecificationExecutor<AppEval>, JpaRepository<AppEval, String> {

    /**
     * 按周日期和服务分组查找
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param serviceNo 服务编号
     * @param inputUnit 填报单位
     * @param modelNo   模型编号
     * @param pageable  可分页
     * @return {@link Page }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2024-01-23
     */
    @Query(value = "select *"
            + " from (select a.weekDate,"
            + "             a.serviceNo,"
            + "             a.inputUnit,"
            + "             GROUP_CONCAT(a.modelNo SEPARATOR ',')  modelNos,"
            + "             GROUP_CONCAT(b.userName SEPARATOR ',') userNames"
            + "      from app_eval a"
            + "               left join du_user b on a.createUserNo = b.userNo"
            + "      where b.status = 0"
            + "        and b.operStus <> 'D'"
            + "        and if(?1 is not null && ?1 != '', a.weekDate >= ?1, 1 = 1)"
            + "        and if(?2 is not null && ?2 != '', a.weekDate <= ?2, 1 = 1)"
            + "        and if(?3 is not null && ?3 != '', a.serviceNo = ?3, 1 = 1)"
            + "        and if(?4 is not null && ?4 != '', a.inputUnit = ?4, 1 = 1)"
            + "      group by a.weekDate, a.serviceNo, a.inputUnit"
            + "      order by a.weekDate desc, a.serviceNo) t"
            + " where if(?5 is not null && ?5 != '', FIND_IN_SET(?5, t.modelNos), 1 = 1)",
            countProjection = "a.weekDate", nativeQuery = true)
    Page<Map<String, Object>> findWeekAppEval(String startTime, String endTime, String serviceNo,
                                              String inputUnit, String modelNo, Pageable pageable);

    /**
     * 按服务和模型分组查找
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param serviceNo 服务编号
     * @param inputUnit 填报单位
     * @param modelNo   模型编号
     * @return {@link Page }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2024-01-23
     */
    @Query(value = "select b.value1 serviceNo, b.code modelNo, ifnull(a.num, 0) num"
            + " from (select serviceNo, modelNo, count(1) num"
            + "      from app_eval"
            + "      where 1 = 1"
            + "        and if(?1 is not null && ?1 != '', weekDate >= ?1, 1 = 1)"
            + "        and if(?2 is not null && ?2 != '', weekDate <= ?2, 1 = 1)"
            + "        and if(?3 is not null && ?3 != '', serviceNo = ?3, 1 = 1)"
            + "        and if(?4 is not null && ?4 != '', inputUnit = ?4, 1 = 1)"
            + "        and if(?5 is not null && ?5 != '', modelNo = ?5, 1 = 1)"
            + "      group by serviceNo, modelNo) a"
            + "         right join (select n.code, n.value, n.value1"
            + "                     from dictionary m"
            + "                              left join dictionary_dtl n on m.id = n.dict_id"
            + "                     where m.code = 'modelNo') b on a.modelNo = b.code"
            + " order by num desc,serviceNo,modelNo",
            countProjection = "modelNo", nativeQuery = true)
    List<Map<String, Object>> findGroupByServerAndModel(String startTime, String endTime, String serviceNo,
                                                        String inputUnit, String modelNo);

    /**
     * 按多个键删除
     *
     * @param weekDate  星期日期
     * @param serviceNo 服务编号
     * <AUTHOR>
     * @create 2024-01-23
     */
    @Transactional
    @Modifying
    @Query("delete AppEval where weekDate = ?1 and serviceNo = ?2 ")
    void deleteByMultiKey(String weekDate, String serviceNo);


}
