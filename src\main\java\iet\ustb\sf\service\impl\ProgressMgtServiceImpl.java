package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.ProgressMgtDao;
import iet.ustb.sf.vo.domain.DictionaryDtl;
import iet.ustb.sf.vo.domain.ProgressMgt;
import iet.ustb.sf.service.DictionaryDtlService;
import iet.ustb.sf.service.ProgressMgtService;
import iet.ustb.sf.utils.ToolsUtil;
import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 进度管理
 *
 * <AUTHOR>
 * @create 2023-01-03
 * @see ProgressMgtService
 */
@Service
public class ProgressMgtServiceImpl implements ProgressMgtService {
    @Autowired
    private ProgressMgtDao progressMgtDao;
    @Autowired
    private DictionaryDtlService dictionaryDtlService;

    @Override
    public List<ProgressMgt> findAll() {
        return progressMgtDao.findAll();
    }

    @Override
    public Page<ProgressMgt> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<ProgressMgt> progressMgtPage = progressMgtDao.findAll(createSpecs(jsonObject), pageable);

        for (ProgressMgt progressMgt : progressMgtPage) {
            if (!"0".equals(progressMgt.getParentId())) {// 非一级菜单综合评分
                Integer comprehensiveScore = this.getComprehensiveScore(progressMgt);
                progressMgt.setComprehensiveScore(comprehensiveScore);
            }
        }
        for (ProgressMgt progressMgt : progressMgtPage) {
            if ("0".equals(progressMgt.getParentId())) {// 一级菜单综合评分：子集菜单综合评分的平均值
                Double collect = progressMgtDao.findByParentId(progressMgt.getId())
                        .stream()
                        .map(ProgressMgt::getComprehensiveScore)
                        .collect(Collectors.averagingInt(Integer::intValue));
                Integer comprehensiveScore = new BigDecimal(collect).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
                progressMgt.setComprehensiveScore(comprehensiveScore);
            }
        }
        return progressMgtPage;
    }

    private Specification<ProgressMgt> createSpecs(JSONObject json) {
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            String name = json.getString("name");// 名称
            Integer module = json.getInteger("module");// 模块
            String parentId = json.getString("parentId");// 父id
            if (StringUtils.isNotBlank(name)) {
                list.add(cb.like(root.get("name"), "%" + name + "%"));
            }
            if (module != null) {
                list.add(cb.equal(root.get("module"), module));
            }
            if (StringUtils.isNotBlank(parentId)) {
                list.add(cb.equal(root.get("parentId"), parentId));
            }
            query.where(list.toArray(new Predicate[list.size()]));
            query.orderBy(cb.asc(root.get("createDateTime")));
            return query.getRestriction();
        };
    }

    /**
     * 获取二级菜单的综合得分
     *
     * @param progressMgt 进度管理
     * @return {@link Integer }
     * <AUTHOR>
     * @create 2023-02-09
     */
    private Integer getComprehensiveScore(ProgressMgt progressMgt) {
        Integer pageDev = progressMgt.getPageDev() != null ? progressMgt.getPageDev() : 0;// 页面开发
        Integer backendDev = progressMgt.getBackendDev() != null ? progressMgt.getBackendDev() : 0;// 后端开发
        Integer dataAccess = progressMgt.getDataAccess() != null ? progressMgt.getDataAccess(): 0;// 数据接入
        Integer functionOnline = progressMgt.getFunctionOnline() != null ? progressMgt.getFunctionOnline() : 0;// 功能上线
        Integer putIntoOperation = progressMgt.getPutIntoOperation() != null ? progressMgt.getPutIntoOperation() : 0;// 投入运行
        Integer deliveryAcceptance = progressMgt.getDeliveryAcceptance() != null ? progressMgt.getDeliveryAcceptance() : 0;// 交付验收

        List<DictionaryDtl> dictDtlList = dictionaryDtlService.findByDictCode("scoreRate");
        double comprehensiveScore = 0;
        for (DictionaryDtl dictDtl : dictDtlList) {
            String code = dictDtl.getCode();
            String value = dictDtl.getValue();

            boolean numeric = ToolsUtil.isNumeric(value);
            if (!numeric) continue;

            // 公式计算综合评分
            /*double comprehensiveScore = pageDev * 0.1 + backendDev * 0.1 + dataAccess * 0.3
                    + functionOnline * 0.3 + putIntoOperation * 0.2 + deliveryAcceptance * 0;*/
            if ("pageDev".equals(code)) {
                comprehensiveScore += pageDev * Double.parseDouble(value);
            }
            if ("backendDev".equals(code)) {
                comprehensiveScore += backendDev * Double.parseDouble(value);
            }
            if ("dataAccess".equals(code)) {
                comprehensiveScore += dataAccess * Double.parseDouble(value);
            }
            if ("functionOnline".equals(code)) {
                comprehensiveScore += functionOnline * Double.parseDouble(value);
            }
            if ("putIntoOperation".equals(code)) {
                comprehensiveScore += putIntoOperation * Double.parseDouble(value);
            }
            if ("deliveryAcceptance".equals(code)) {
                comprehensiveScore += deliveryAcceptance * Double.parseDouble(value);
            }
        }
        // double转BigDecimal 四舍五入
        Integer cs = new BigDecimal(comprehensiveScore).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
        return cs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProgressMgt save(ProgressMgt progressMgt) {
        return progressMgtDao.save(progressMgt);
    }

    @Override
    public void delete(JSONObject jsonObject) throws Exception {
        String id = jsonObject.getString("id");
        // 删除该问题反馈单
        progressMgtDao.deleteById(id);
    }

}
