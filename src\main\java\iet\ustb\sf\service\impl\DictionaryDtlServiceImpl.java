package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.DictionaryDtlDao;
import iet.ustb.sf.utils.RedisUtil;
import iet.ustb.sf.utils.constant.Constant;
import iet.ustb.sf.vo.domain.BasicDataConfig;
import iet.ustb.sf.vo.domain.Dictionary;
import iet.ustb.sf.vo.domain.DictionaryDtl;
import iet.ustb.sf.service.DictionaryDtlService;
import iet.ustb.sf.utils.ToolsUtil;
import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据字典详细信息impl
 *
 * <AUTHOR>
 * @create 2023-02-09
 * @see DictionaryDtlService
 */
@Service
@Transactional
public class DictionaryDtlServiceImpl implements DictionaryDtlService {

    @Autowired
    private DictionaryDtlDao dictionaryDtlDao;


    @Autowired
    private BasicDataConfigServiceImpl basicDataConfigService;

    @Autowired
    RedisUtil redisUtil;

    @Override
    public List<DictionaryDtl> findAll() {
        return dictionaryDtlDao.findAll();
    }

    @Override
    public Page<DictionaryDtl> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<DictionaryDtl> dictionaryDtlPage = dictionaryDtlDao.findAll(createSpecs(jsonObject), pageable);
        return dictionaryDtlPage;
    }

    /**
     * 创建等级库
     *
     * @param json json文件
     * @return {@link Specification }<{@link DictionaryDtl }>
     * <AUTHOR>
     * @create 2023-02-09
     */
    private Specification<DictionaryDtl> createSpecs(JSONObject json) {
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            String dictId = json.getString("dictId");// 字典id
            String code = json.getString("code");// 编码
            String value = json.getString("value");// 字典值

            Assert.hasText(dictId, "dictId must not be empty");
            list.add(cb.equal(root.get("dict").get("id"), dictId));

            if (StringUtils.isNotBlank(code)) {
                list.add(cb.like(root.get("code"), "%" + code + "%"));
            }
            if (StringUtils.isNotBlank(value)) {
                list.add(cb.like(root.get("value"), "%" + value + "%"));
            }
            query.where(list.toArray(new Predicate[list.size()]));
            query.orderBy(cb.desc(root.get("createDateTime")));
            return query.getRestriction();
        };
    }

    @Override
    public DictionaryDtl save(DictionaryDtl dictionaryDtl) {
        Dictionary dictionary = dictionaryDtl.getDict();

        Assert.notNull(dictionary, "dictionary must not be null");
        Assert.hasText(dictionary.getId(), "dict.id must not be null");

        dictionaryDtlDao.save(dictionaryDtl);

        //更新redis中的数据字典的缓存值
        List<DictionaryDtl> dictionaryDtls = basicDataConfigService.getBasicDataInfo(new BasicDataConfig());
        Map<String, List<Map<String, String>>> groupedByParentCode = dictionaryDtls.stream()
                .collect(Collectors.groupingBy(
                        DictionaryDtl::getParentCode,
                        Collectors.mapping(dtl -> {
                            Map<String, String> map = new HashMap<>();
                            map.put("code", dtl.getCode());
                            map.put("text", dtl.getValue());
                            return map;
                        }, Collectors.toList())
                ));
        groupedByParentCode.forEach((parentCode, dictList) -> {
            redisUtil.setHashList(Constant.SYS_CACHE_DICT_INFO, parentCode, dictList);
        });

        return null;
    }

    @Override
    public void delete(DictionaryDtl dictionaryDtl) throws Exception {
        dictionaryDtlDao.delete(dictionaryDtl);
    }

    @Override
    public List<DictionaryDtl> findByDictCode(String dictCode) {
        return dictionaryDtlDao.findByDictCode(dictCode);
    }

    @Override
    public List<DictionaryDtl> findByDictId(String DictId) {
        return dictionaryDtlDao.findByDictId(DictId);
    }
}
