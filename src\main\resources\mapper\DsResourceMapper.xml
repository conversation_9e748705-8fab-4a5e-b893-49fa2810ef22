<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="iet.ustb.sf.mapper.DsResourceMapper">

    <resultMap type="iet.ustb.sf.vo.domain.Resource" id="DsResourceMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createDateTime" column="createdatetime" jdbcType="TIMESTAMP"/>
        <result property="createUserNo" column="createuserno" jdbcType="VARCHAR"/>
        <result property="updateDateTime" column="updatedatetime" jdbcType="TIMESTAMP"/>
        <result property="updateUserNo" column="updateuserno" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="icon" column="icon" jdbcType="VARCHAR"/>
        <result property="isShow" column="isshow" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="parentId" column="parentid" jdbcType="VARCHAR"/>
        <result property="serviceName" column="servicename" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="isSelected" column="isselected" jdbcType="VARCHAR"/>
        <result property="ip" column="ip" jdbcType="VARCHAR"/>
        <result property="port" column="port" jdbcType="VARCHAR"/>
        <result property="pluginSize" column="pluginsize" jdbcType="VARCHAR"/>
        <result property="deskIcon" column="deskicon" jdbcType="VARCHAR"/>
        <result property="parentCode" column="parent_code" jdbcType="VARCHAR"/>
        <result property="handleDeveloper" column="handledeveloper" jdbcType="VARCHAR"/>
        <result property="handleUser" column="handleuser" jdbcType="VARCHAR"/>
        <result property="approveUser" column="approveUser" jdbcType="VARCHAR"/>
        <result property="isExternalUrl" column="is_external_url" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into resource.ds_resource(
        createdatetime, createuserno, updatedatetime, updateuserno,
        code, description, icon, isshow, name, servicename,
        sort, status, type, url, isselected, ip, port, pluginsize, deskicon,
        parent_id, handledeveloper, handleuser, approveUser, is_external_url
        )
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.createDateTime}, #{entity.createUserNo}, #{entity.updateDateTime}, #{entity.updateUserNo},
            #{entity.code}, #{entity.description}, #{entity.icon}, #{entity.isShow}, #{entity.name},
            #{entity.serviceName}, #{entity.sort}, #{entity.status}, #{entity.type},
            #{entity.url}, #{entity.isSelected}, #{entity.ip}, #{entity.port}, #{entity.pluginSize},
            #{entity.deskIcon}, #{entity.parentId}, #{entity.handleDeveloper}, #{entity.handleUser},
            #{entity.approveUser}, #{entity.isExternalUrl})
        </foreach>
    </insert>

    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into resource.ds_resource(
        createdatetime, createuserno, updatedatetime, updateuserno,
        code, description, icon, isshow, name, servicename,
        sort, status, type, url, isselected, ip, port, pluginsize, deskicon,
        parent_id, handledeveloper, handleuser, approveUser, is_external_url
        )
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.createDateTime}, #{entity.createUserNo}, #{entity.updateDateTime}, #{entity.updateUserNo},
            #{entity.code}, #{entity.description}, #{entity.icon}, #{entity.isShow}, #{entity.name},
            #{entity.serviceName}, #{entity.sort}, #{entity.status}, #{entity.type},
            #{entity.url}, #{entity.isSelected}, #{entity.ip}, #{entity.port}, #{entity.pluginSize},
            #{entity.deskIcon}, #{entity.parentId}, #{entity.handleDeveloper}, #{entity.handleUser},
            #{entity.approveUser}, #{entity.isExternalUrl})
        </foreach>
        on duplicate key update
        createdatetime = VALUES(createdatetime),
        createuserno = VALUES(createuserno),
        updatedatetime = VALUES(updatedatetime),
        updateuserno = VALUES(updateuserno),
        code = VALUES(code),
        description = VALUES(description),
        icon = VALUES(icon),
        isshow = VALUES(isshow),
        name = VALUES(name),
        parent_id = VALUES(parent_id),
        servicename = VALUES(servicename),
        sort = VALUES(sort),
        status = VALUES(status),
        type = VALUES(type),
        url = VALUES(url),
        isselected = VALUES(isselected),
        ip = VALUES(ip),
        port = VALUES(port),
        pluginsize = VALUES(pluginsize),
        deskicon = VALUES(deskicon),
        handledeveloper = VALUES(handledeveloper),
        handleuser = VALUES(handleuser),
        approveUser = VALUES(approveUser),
        is_external_url = VALUES(is_external_url);
    </insert>

    <!-- 查询菜单信息 -->
    <select id="getResourceInfo" resultType="iet.ustb.sf.vo.domain.Resource">
        SELECT
        r.id, r.createdatetime, r.createuserno, r.updatedatetime, r.updateuserno,
        r.code, r.description, r.icon, r.isshow, r.name,r.parentid,r.parent_code, r.servicename,
        r.sort, r.status, r.type, r.url, r.isselected, r.ip, r.port, r.pluginsize, r.deskicon,
        r.handledeveloper, r.handleuser, r.approveUser, r.is_external_url,i.name as iconUrl,
        id.name as deskUrl
        FROM resource.ds_resource r
        left join icon_img i on r.icon = i.id
        left join icon_img id on r.deskicon = id.id
        <where>
            <if test="id != null">
                AND r.id = #{id}
            </if>
            <if test="createDateTime != null">
                AND r.createdatetime = #{createDateTime}
            </if>
            <if test="createUserNo != null">
                AND r.createuserno = #{createUserNo}
            </if>
            <if test="updateDateTime != null">
                AND r.updatedatetime = #{updateDateTime}
            </if>
            <if test="updateUserNo != null">
                AND r.updateuserno = #{updateUserNo}
            </if>
            <if test="code != null">
                AND r.code = #{code}
            </if>
            <if test="description != null">
                AND r.description = #{description}
            </if>
            <if test="icon != null">
                AND r.icon = #{icon}
            </if>
            <if test="isShow != null">
                AND r.isshow = #{isShow}
            </if>
            <if test="name != null">
                AND r.name = #{name}
            </if>
            <if test="parentCode != null">
                AND parent_code = #{parentCode}
            </if>
            <if test="parentId != null">
                AND r.parentid = #{parentId}
            </if>
            <if test="serviceName != null">
                AND r.servicename = #{serviceName}
            </if>
            <if test="sort != null">
                AND r.sort = #{sort}
            </if>
            <if test="status != null">
                AND r.status = #{status}
            </if>
            <if test="type != null">
                AND r.type = #{type}
            </if>
            <if test="url != null">
                AND r.url = #{url}
            </if>
            <if test="isSelected != null">
                AND r.isselected = #{isSelected}
            </if>
            <if test="ip != null">
                AND r.ip = #{ip}
            </if>
            <if test="port != null">
                AND r.port = #{port}
            </if>
            <if test="pluginSize != null">
                AND r.pluginsize = #{pluginSize}
            </if>
            <if test="deskIcon != null">
                AND r.deskicon = #{deskIcon}
            </if>
            <if test="handleDeveloper != null">
                AND r.handledeveloper = #{handleDeveloper}
            </if>
            <if test="handleUser != null">
                AND r.handleuser = #{handleUser}
            </if>
            <if test="approveUser != null">
                AND r.approveUser = #{approveUser}
            </if>
            <if test="isExternalUrl != null">
                AND r.is_external_url = #{isExternalUrl}
            </if>
        </where>
        ORDER BY r.createdatetime DESC
    </select>

    <!-- 获取当前最大一级菜单编号 -->
    <select id="getMaxTopLevelCode" resultType="String">
        SELECT LPAD(MAX(CAST(SUBSTRING_INDEX(code, '_', -1) AS UNSIGNED)), 3, '0')
        FROM resource.ds_resource
        WHERE parent_code IS NULL
          AND code REGEXP '^res_[0-9]+_[0-9]+$'
    </select>

    <!-- 获取当前最大子菜单编号 -->
    <select id="getMaxSubLevelCode" resultType="String">
        SELECT MAX(SUBSTRING_INDEX(code, '_', -1)) FROM resource.ds_resource WHERE parent_code = #{parentCode}
    </select>

    <select id="findResource" resultType="iet.ustb.sf.vo.domain.Resource">
        SELECT
        dr.*,
        i.name AS iconUrl,
        di.name AS deskUrl
        FROM ds_resource dr
        LEFT JOIN icon_img i ON dr.icon = i.id
        LEFT JOIN icon_img di ON dr.deskicon = di.id
        WHERE 1=1
        <if test="userNo != null and userNo != ''">
            AND EXISTS (
            SELECT 1 FROM ds_role_resourcelist drr
            JOIN ds_role_userlist dsu ON drr.Role_id = dsu.Role_id
            JOIN du_user du ON dsu.userList_id = du.id
            WHERE drr.resourceList_id = dr.id
            AND du.userNo = #{userNo}
            )
        </if>
        <if test="type != null and type != ''">
            AND dr.type = #{type}
        </if>
        <if test="serviceName != null and serviceName != ''">
            AND dr.serviceName = #{serviceName}
        </if>
    </select>


    <!-- 根据角色ID查询所有授权的菜单信息 -->
    <select id="filterResourceSelected" parameterType="string" resultType="iet.ustb.sf.vo.domain.Resource">
        SELECT DISTINCT tmp_.*
        FROM (
                 SELECT id, createDateTime, createUserNo, updateDateTime, updateUserNo, `code`,
                        description, icon, deskIcon, isShow, `name`, parentId, serviceName, sort,
                        `status`, type, url, ip, port, pluginSize, handleDeveloper, handleUser, approveUser,
                        1 AS isSelected
                 FROM resource.ds_resource AS rsc
                          JOIN resource.ds_role_resourcelist AS rr ON rr.resourceList_id = rsc.id
                 WHERE rr.Role_id = #{roleID} AND status = 1
                 UNION
                 SELECT id, createDateTime, createUserNo, updateDateTime, updateUserNo, `code`,
                        description, icon, deskIcon, isShow, `name`, parentId, serviceName, sort,
                        `status`, type, url, ip, port, pluginSize, handleDeveloper, handleUser, approveUser,
                        0 AS isSelected
                 FROM resource.ds_resource AS rsc
                 WHERE id NOT IN (
                     SELECT rsc.id
                     FROM resource.ds_resource AS rsc
                              JOIN resource.ds_role_resourcelist AS rr ON rr.resourceList_id = rsc.id
                     WHERE rr.Role_id = #{roleID}
                 ) AND status = 1
             ) AS tmp_
    </select>


    <!-- 调用存储过程查询节点下面所有的子节点 -->
    <select id="getChildResources" resultType="iet.ustb.sf.vo.domain.Resource">
        CALL GetAllChildren(#{parentId})
    </select>

</mapper>

