package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.ProgressMgt;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 进度管理
 *
 * <AUTHOR>
 * @create 2023-01-03
 */
public interface ProgressMgtService {

    /**
     * 查找所有内容
     *
     * @return {@link List }<{@link ProgressMgt }>
     * <AUTHOR>
     * @create 2023-01-03
     */
    List<ProgressMgt> findAll();

    /**
     * 按多条件查找分页
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link ProgressMgt }>
     * <AUTHOR>
     * @create 2023-01-03
     */
    Page<ProgressMgt> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param progressMgt 进度管理
     * @return {@link ProgressMgt }
     * <AUTHOR>
     * @create 2022-10-26
     */
    ProgressMgt save(ProgressMgt progressMgt);

    /**
     * 删除
     *
     * @param jsonObject json对象
     * <AUTHOR>
     * @create 2022-10-26
     */
    void delete(JSONObject jsonObject) throws Exception;
}
