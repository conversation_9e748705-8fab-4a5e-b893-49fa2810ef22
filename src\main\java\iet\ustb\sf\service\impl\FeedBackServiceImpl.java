package iet.ustb.sf.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import iet.ustb.sf.dao.FeedBackDao;
import iet.ustb.sf.dao.ResourceDao;
import iet.ustb.sf.vo.eo.FeedBackNumEO;
import iet.ustb.sf.service.*;
import iet.ustb.sf.utils.DateTools;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.utils.UserThreadUtil;
import iet.ustb.sf.vo.domain.*;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.Cleanup;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 问题反馈
 * <AUTHOR>
 * @create 2022-10-26
 */
@Service
@CommonsLog
public class FeedBackServiceImpl implements FeedBackService {
    @Autowired
    private FeedBackDao feedBackDao;
    @Autowired
    private AttachService attachService;
    @Autowired
    private CommentService commentService;
    @Autowired
    private UserService userService;
    @Autowired
    private ResourceDao resourceDao;
    @Autowired
    private OrgService orgService;

    @Override
    public List<FeedBack> findAll() {
        return feedBackDao.findAll();
    }

    @Override
    public Page<FeedBack> findPageByMultiCondition(JSONObject  jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);
        Page<FeedBack> feedBackPage = feedBackDao.findAll(createSpecs(jsonObject), pageable);

        Integer pageSize = jsonObject.getInteger("pageSize");
        if (pageSize != 1) {//  每个页面加载时查询未处理个数
            log.warn("FeedBack Detail....");
            //  获取所有反馈ID
            List<String> feedbackIds = feedBackPage.getContent()
                    .stream()
                    .map(FeedBack::getId)
                    .collect(Collectors.toList());

            //  获取反馈的附件列表
            Map<String, List<Attach>> attachMap = attachService.findByRelatedIds(feedbackIds)
                    .stream()
                    .collect(Collectors.groupingBy(Attach::getRelatedId));

            //  获取提出者与处理者的基本信息
            Set<String> userNos = feedBackPage
                    .stream()
                    .flatMap(feedBack -> Stream.of(feedBack.getQuesUserNo(), feedBack.getHandleUserNo()))
                    .collect(Collectors.toSet());

            Map<String, Map<String, String>> userOrgMap = userService.findUserOrgByUserNos(userNos)
                    .stream()
                    .collect(Collectors.toMap(map -> map.get("userNo"), map -> map));

            //  获取资源信息
            Set<String> resourceIds = feedBackPage
                    .stream()
                    .filter(feedBack -> StringUtils.isNotBlank(feedBack.getResourceId()))
                    .map(FeedBack::getResourceId)
                    .collect(Collectors.toSet());

            Map<String, Resource> resourceMap = resourceDao.findAllById(resourceIds)
                    .stream()
                    .collect(Collectors.toMap(Resource::getId, Resource -> Resource));

            //  循环处理反馈信息
            for (FeedBack feedBack : feedBackPage) {
                //  附件
                feedBack.setAttachList(attachMap.getOrDefault(feedBack.getId(), Collections.emptyList()));

                //  提出者基本信息
                Map<String, String> quesUserMap = userOrgMap.getOrDefault(feedBack.getQuesUserNo(), Collections.emptyMap());
                feedBack.setQuesUserName(quesUserMap.get("userName"));
                feedBack.setQuesOrgName(quesUserMap.get("orgAllName"));

                //  处理者基本信息
                Map<String, String> handleUserMap = userOrgMap.getOrDefault(feedBack.getHandleUserNo(), Collections.emptyMap());
                feedBack.setHandleUserName(handleUserMap.get("userName"));
                feedBack.setHandleOrgName(handleUserMap.get("orgAllName"));

                //  资源信息
                if (StringUtils.isNotBlank(feedBack.getResourceId())) {
                    Resource resource = resourceMap.get(feedBack.getResourceId());
                    if (resource != null) {
                        feedBack.setPageName(resource.getName());
                        String parentId = resource.getParentId();
                        if (StringUtils.isNotBlank(parentId)) {
                            Resource parentResource = resourceMap.get(parentId);
                            if (parentResource != null) {
                                feedBack.setParentName(parentResource.getName());
                            }
                        }
                    }
                }
            }
        }
        return feedBackPage;
    }

    private Specification<FeedBack> createSpecs(JSONObject json) {
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            String title = json.getString("title");// 标题
            String serviceNo = json.getString("serviceNo");// 应用编号
            String quesType = json.getString("quesType");// 问题类型 ：1-建议 2-bug
            Integer handleStatus = json.getInteger("handleStatus");// 处理状态 ：1-已创建 2-已解决 3-无效
            String quesUserNo = json.getString("quesUserNo");// 提出者编号
            String handleUserNo = json.getString("handleUserNo");// 处理者编号
            String anonymous = json.getString("anonymous");// 是否匿名
            String resourceId = json.getString("resourceId");// 资源id
            String startDate = json.getString("startDate");//起始日期
            String endDate = json.getString("endDate");//结束日期
            String quesOrgCode = json.getString("quesOrgCode");//提出者组织编号

            if (StringUtils.isNotBlank(title)) {
                list.add(cb.like(root.get("title"), "%" + title + "%"));
            }
            if (StringUtils.isNotBlank(serviceNo)) {
                list.add(cb.equal(root.get("serviceNo"), serviceNo));
            }
            if (StringUtils.isNotBlank(quesType)) {
                list.add(cb.equal(root.get("quesType"), quesType));
            }
            if (StringUtils.isNotBlank(quesOrgCode)) {
                // 获取所有子组织列表
                Set<String> allChildOrgList = orgService.getAllChildOrgList(quesOrgCode);
                list.add(root.get("quesOrgCode").in(allChildOrgList));
            }
            if (handleStatus != null) {
                if (handleStatus == 1) {// 未处理状态时，查询未处理、处理中、催办
                    List<Integer> handleStatusList = Arrays.asList(1, 3, 5);
                    list.add((root.get("handleStatus").in(handleStatusList)));
                } else {
                    list.add(cb.equal(root.get("handleStatus"), handleStatus));
                }
            }
            if (StringUtils.isNotBlank(quesUserNo)) {
                list.add(cb.equal(root.get("quesUserNo"), quesUserNo));
            }
            if (StringUtils.isNotBlank(handleUserNo)) {
                list.add(cb.equal(root.get("handleUserNo"), handleUserNo));
            }
            if (StringUtils.isNotBlank(anonymous)) {
                list.add(cb.equal(root.get("anonymous"), anonymous));
            }
            if (StringUtils.isNotBlank(resourceId)) {
                list.add(cb.equal(root.get("resourceId"), resourceId));
            }
            if (StringUtils.isNotBlank(startDate)) {
                list.add(cb.greaterThanOrEqualTo(root.get("createDateTime").as(String.class), DateTools.getDayStartTime(startDate)));
            }
            if (StringUtils.isNotBlank(endDate)) {
                list.add(cb.lessThanOrEqualTo(root.get("createDateTime").as(String.class), DateTools.getDayEndTime(endDate)));
            }

            query.where(list.toArray(new Predicate[list.size()]));
            query.orderBy(cb.desc(root.get("createDateTime")));
            return query.getRestriction();
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FeedBack save(FeedBack feedback) {
        User user = UserThreadUtil.getUser();
        String currUserNo =  user.getUserNo();
        String currOrgCode =  user.getOrgCode();
        if (StringUtils.isBlank(feedback.getId())) {// 新增情况
            feedback.setQuesUserNo(currUserNo);
        }
        Integer handleStatus = feedback.getHandleStatus();
        if (handleStatus == null) {
            handleStatus = 1;// 默认新增状态
            feedback.setHandleStatus(handleStatus);
            feedback.setQuesOrgCode(currOrgCode);
        }
        if (handleStatus == 2 || handleStatus == 6) {// 若状态为已处理或退回，则更新处理时间
            feedback.setHandleUserNo(currUserNo);
            feedback.setHandleTime(DateTools.getFullNowDateTime());
        }

        // 保存问题反馈信息
        FeedBack feedBackNew = feedBackDao.save(feedback);

        String relatedId = feedBackNew.getId();

        List<Attach> attachList = feedback.getAttachList();
        if (attachList!= null && attachList.size() > 0) {
            List<String> idList = attachList
                    .stream()
                    .map(Attach::getId)
                    .collect(Collectors.toList());

            // 更新附件关联业务id
            List<Attach> attaches = attachService.findByIds(idList);
            for (Attach attach : attaches) {
                attach.setRelatedId(relatedId);
            }
            attachService.saveAll(attaches);
        }

        List<Comment> commentList = feedback.getCommentList();
        if (commentList != null && commentList.size() > 0) {
            Comment comment = commentList.get(0);
            String content = comment.getContent();
            if (StringUtils.isNotBlank(content)) {
                commentService.save(comment);
            }
        }
        return feedBackNew;
    }

    @Override
    public void delete(JSONObject jsonObject) throws Exception {
        String id = jsonObject.getString("id");
        // 删除该问题反馈单上传的附件
        attachService.deleteByRelatedId(id);
        // 删除该问题反馈单
        feedBackDao.deleteById(id);
    }

    @Override
    public List<Map<String, Integer>> findCountByStatus(List<Integer> statusList, Date startDate, Date endDate) {
        return feedBackDao.findCountByStatus(statusList, startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> findUserFeedBackNum(String startDate, String endDate) {
        return feedBackDao.findUserFeedBackNum(startDate, endDate);
    }

    @Override
    public void exportUserFeedBackExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException {
        String startDate = jsonObject.getString("startDate");
        String endDate = jsonObject.getString("endDate");

        ExportParams exportParams = new ExportParams();
        exportParams.setTitle("用户反馈统计");
        exportParams.setSheetName("用户反馈统计");

        Map<String, Object>  oneExportMap = new HashMap<>();
        oneExportMap.put("title", exportParams);
        oneExportMap.put("entity", FeedBackNumEO.class);
        // 查找用户问题反馈数
        List<Map<String, Object>> mapList = feedBackDao.findUserFeedBackNum(startDate, endDate);
        List<FeedBackNumEO> eoList = JSON.parseObject(JSON.toJSONString(mapList), new TypeReference<List<FeedBackNumEO>>() {});
        oneExportMap.put("data", eoList);

        // 将sheet使用得map进行包装
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        sheetsList.add(oneExportMap);

        // 执行方法
        @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
        //设置编码格式
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        //设置内容类型
        response.setContentType("application/octet-stream");
        //设置头及文件命名。
        response.setHeader("Content-Disposition", "attachment;filename="
                + URLEncoder.encode("用户反馈统计("+startDate +"_"+endDate+").xls",
                StandardCharsets.UTF_8.name()));
        //写出流
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        workBook.write(outputStream);
    }

    @Override
    public List<Map<String, Object>> findOneOrgFeedBackNum(JSONObject jsonObject) {
        String startDate = jsonObject.getString("startDate");
        String endDate = jsonObject.getString("endDate");
        Integer handleStatus = jsonObject.getInteger("handleStatus");
        // 查找厂处问题反馈数量
        return feedBackDao.findOneOrgFeedBackNum(startDate, endDate, handleStatus);
    }
}
