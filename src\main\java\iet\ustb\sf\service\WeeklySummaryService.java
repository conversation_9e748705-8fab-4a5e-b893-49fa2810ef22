package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.WeeklySummary;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 周/月/日总结
 *
 * <AUTHOR>
 * @create 2023-01-04
 */
public interface WeeklySummaryService {

    /**
     * 查找所有内容
     *
     * @return {@link List }<{@link WeeklySummary }>
     * <AUTHOR>
     * @create 2023-01-04
     */
    List<WeeklySummary> findAll();

    /**
     * 按多条件查找分页
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link WeeklySummary }>
     * <AUTHOR>
     * @create 2023-01-04
     */
    Page<WeeklySummary> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 按多条件查找
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link WeeklySummary }>
     * <AUTHOR>
     * @create 2024-01-25
     */
    List<WeeklySummary> findByMultiCondition(JSONObject jsonObject);

    void saveList(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param weeklySummary
     * @return {@link WeeklySummary }
     * <AUTHOR>
     * @create 2023-01-04
     */
    WeeklySummary save(WeeklySummary weeklySummary);

    /**
     * 删除
     *
     * @param jsonObject json对象
     * <AUTHOR>
     * @create 2023-01-04
     */
    void delete(JSONObject jsonObject) throws Exception;
}
