package iet.ustb.sf.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import iet.ustb.sf.dao.AppLogDao;
import iet.ustb.sf.dao.PageLogDao;
import iet.ustb.sf.dao.UserDao;
import iet.ustb.sf.vo.eo.AccessNumEO;
import iet.ustb.sf.vo.eo.LeaderAccessNumEO;
import iet.ustb.sf.vo.eo.OrgAccessNumEO;
import iet.ustb.sf.service.*;
import iet.ustb.sf.utils.DateTools;
import iet.ustb.sf.utils.IpUtil;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.utils.UserThreadUtil;
import iet.ustb.sf.vo.domain.*;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.Cleanup;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用程序日志
 *
 * <AUTHOR>
 * @create 2022-09-22
 * @see AppLogService
 */
@Service
@CommonsLog
public class AppLogServiceImpl implements AppLogService {

    @Autowired
    private AppLogDao appLogDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private LoginLogService loginLogService;
//    @LoadBalanced
    @Resource
    private RestTemplate restTemplate;
    @Autowired
    private OrgService orgService;
    @Autowired
    private FeedBackService feedBackService;
    @Autowired
    private PageLogDao pageLogDao;
    @Autowired
    private RoleService roleService;
    @Autowired
    private DictionaryDtlService dictionaryDtlService;

    @Override
    public List<AppLog> findAll() {
        return appLogDao.findAll();
    }

    @Override
    public List<AppLog> findAllByMultiCondition(JSONObject jsonObject) {
        List<AppLog> appLogList = appLogDao.findAll(createSpecs(jsonObject));
        return appLogList;
    }

    @Override
    public Page<AppLog> findByMultiCondition(JSONObject jsonObject) {

        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<AppLog> pages = appLogDao.findAll(createSpecs(jsonObject), pageable);
        return pages;
    }

    @Override
    public List<AccessNumEO> findAccessNumByLoginTime(JSONObject jsonObj) {
        String loginTimeStart = jsonObj.getString("loginTimeStart");
        String loginTimeEnd = jsonObj.getString("loginTimeEnd");
        String type = jsonObj.getString("type");

        Map<String, Date> map = DateTools.getStartAndEndDate(loginTimeStart, loginTimeEnd);
        Date loginTimeStartDate = map.get("loginTimeStart");
        Date loginTimeEndDate = map.get("loginTimeEnd");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = sdf.format(loginTimeStartDate);
        String endTime = sdf.format(loginTimeEndDate);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("pageIndex", 1);
        jsonObject.put("pageSize", 5000);
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        List<AccessNumEO> accessNumEOList = new ArrayList<>();
        if (StringUtils.isNotBlank(type) && type.startsWith("org")) {
            // 查询组织访问次数
//            List<AccessNumEO> accessNumEOS = appLogDao.findGroupOrgByLoginTime(loginTimeStartDate, loginTimeEndDate);
            // 层级：1-厂处 2-科室 3-班组
            Integer level = Integer.parseInt(type.substring(type.length() - 1));
            accessNumEOList = this.calcOrgNum(startTime, endTime).stream()
                    .filter(eo -> eo.getLevel() != null && eo.getLevel() == level)
                    .map(eo -> {
                        eo.setName(eo.getName().replace("板材事业部", ""));
                        return eo;
                    })
                    .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                    .collect(Collectors.toList());
        }
        if ("user".equals(type)) {
            // 查询应用用户次数
            /*accessNumEOList = appLogDao.findGroupUserByLoginTime(loginTimeStartDate, loginTimeEndDate)
                    .stream()
                    .filter(eo -> StringUtils.isNotBlank(eo.getOrgName()))
                    .limit(20).collect(Collectors.toList());*/

            // 查找用户页面访问数据
//            Page<Map<String, Object>> userPageAccessData =
//                    pageLogDao.findUserPageAccessList(startTime, endTime, null , null, null, null, pageable);
//            List<Map<String, Object>> mapList = userPageAccessData.getContent();

            List<Map<String, Object>> mapList =
                    pageLogDao.findUserPageAccessList2(startTime, endTime, null , null, null, null);

            accessNumEOList = mapList.stream()
                    .map(m -> new AccessNumEO((String) m.get("userNo"),
                            (String) m.get("userName"),
                            Long.valueOf(m.get("accessNum").toString()),
                            ((String) m.get("orgAllName")).replace("板材事业部","")))
                    .collect(Collectors.toList());
        }
        if("app".equals(type)) {
            // 查询应用访问次数
            /*accessNumEOList = appLogDao.findGroupAppByLoginTime(loginTimeStartDate, loginTimeEndDate)
                    .stream()
                    .filter(accessNumEO -> !accessNumEO.getCode().contains("old") && !"res".equals(accessNumEO.getCode()))
                    .collect(Collectors.toList());*/

            List<Map<String, Object>> mapList = pageLogDao.findPageAccessNumByService(startTime, endTime);
            accessNumEOList = mapList.stream()
                    .map(m -> new AccessNumEO((String) m.get("code"),
                            Long.valueOf(m.get("num").toString())))
                    .collect(Collectors.toList());
            accessNumEOList = this.writeServiceName(accessNumEOList);
        }
        return accessNumEOList;
    }

    @Override
    public void exportExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");

        Map<String, Date> map = DateTools.getStartAndEndDate(loginTimeStart, loginTimeEnd);
        Date loginTimeStartDate = map.get("loginTimeStart");
        Date loginTimeEndDate = map.get("loginTimeEnd");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = sdf.format(loginTimeStartDate);
        String endTime = sdf.format(loginTimeEndDate);

        //功能描述：把同一个表格多个sheet测试结果重新输出
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("厂处排行");
        // 设置sheet表头名称
        exportParams.setSheetName("厂处排行");
        // 创建sheet1使用得map
        Map<String, Object> oneExportMap = new HashMap<>();
        // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
        oneExportMap.put("title", exportParams);
        // 模版导出对应得实体类型
        oneExportMap.put("entity", AccessNumEO.class);
        // 查找组织使用人数和访问次数
//        List<AccessNumEO> accessNumEOList = appLogDao.findGroupOrgByLoginTime(loginTimeStartDate, loginTimeEndDate);
        // 计算组织及所有子组织人数之和
        List<AccessNumEO> accessNumEOList = this.calcOrgNum(startTime, endTime);

        // 获取厂处数据 排除 板材事业部江苏南钢板材销售有限公司、板材事业部金石高新材料项目部
        List<AccessNumEO> dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 1)
                .filter(eo -> !"*********".equals(eo.getCode()) && !"*********".equals(eo.getCode()))
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        oneExportMap.put("data", dataList);

        exportParams = new ExportParams();
        exportParams.setTitle("科室排行");
        exportParams.setSheetName("科室排行");
        Map<String, Object>  twoExportMap = new HashMap<>();
        twoExportMap.put("title", exportParams);
        twoExportMap.put("entity", AccessNumEO.class);

        // 获取科室数据 排除 第一炼钢厂检修车间、综合处JIT+C2M项目部、营销处...、金润...
        dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 2)
                .filter(eo -> !"X73080000".equals(eo.getCode()) && !"X50010800".equals(eo.getCode())
                    && !eo.getCode().contains("X5005") && !eo.getCode().contains("X87"))
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        twoExportMap.put("data", dataList);

        exportParams = new ExportParams();
        exportParams.setTitle("班组排行");
        exportParams.setSheetName("班组排行");
        Map<String, Object>  threeExportMap = new HashMap<>();
        threeExportMap.put("title", exportParams);
        threeExportMap.put("entity", AccessNumEO.class);
        // 获取班组数据
        dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 3)
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        threeExportMap.put("data", dataList);

        exportParams = new ExportParams();
        exportParams.setTitle("用户排行");
        exportParams.setSheetName("用户排行");
        Map<String, Object> userExportMap = new HashMap<>();
        userExportMap.put("title", exportParams);
        userExportMap.put("entity", AccessNumEO.class);
        // 查询用户访问次数
        /*accessNumEOList = appLogDao.findGroupUserByLoginTime(loginTimeStartDate, loginTimeEndDate)
                .stream()
                .map(eo -> {
                    eo.setOrgName(eo.getOrgName().replace("板材事业部", ""));
                    return eo;
                }).collect(Collectors.toList());*/

        JSONObject paramJson = new JSONObject();
        paramJson.put("pageIndex", 1);
        paramJson.put("pageSize", 5000);
        Pageable pageable = ToolsUtil.initPage(paramJson);

        // 查找用户页面访问数据
//        Page<Map<String, Object>> userPageAccessData =
//                pageLogDao.findUserPageAccessList(startTime, endTime, null , null, null, null, pageable);
//        List<Map<String, Object>> mapList = userPageAccessData.getContent();

        List<Map<String, Object>> mapList =
                pageLogDao.findUserPageAccessList2(startTime, endTime, null , null, null, null);

        accessNumEOList = mapList.stream()
                .map(m -> new AccessNumEO((String) m.get("userNo"),
                        (String) m.get("userName"),
                        Long.valueOf(m.get("accessNum").toString()),
                        ((String) m.get("orgAllName")).replace("板材事业部","")))
                .collect(Collectors.toList());
        userExportMap.put("data", accessNumEOList);

        exportParams = new ExportParams();
        exportParams.setTitle("应用排行");
        exportParams.setSheetName("应用排行");
        Map<String, Object>  appExportMap = new HashMap<>();
        appExportMap.put("title", exportParams);
        appExportMap.put("entity", AccessNumEO.class);
        // 查询应用访问次数
        /*accessNumEOList =  appLogDao.findGroupAppByLoginTime(loginTimeStartDate, loginTimeEndDate)
                .stream()
                .filter(accessNumEO -> !accessNumEO.getCode().contains("old") && !"res".equals(accessNumEO.getCode()))
                .collect(Collectors.toList());*/

        mapList = pageLogDao.findPageAccessNumByService(startTime, endTime);
        accessNumEOList = mapList.stream()
                .map(m -> new AccessNumEO((String) m.get("code"),
                        Long.valueOf(m.get("num").toString())))
                .collect(Collectors.toList());
        accessNumEOList = this.writeServiceName(accessNumEOList);
        appExportMap.put("data", accessNumEOList);

        // 将sheet使用得map进行包装
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        //后续增加sheet组，则后面继续追加即可;
        sheetsList.add(oneExportMap);
        sheetsList.add(twoExportMap);
        sheetsList.add(threeExportMap);
        sheetsList.add(userExportMap);
        sheetsList.add(appExportMap);

        // 执行方法
        @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
        //设置编码格式
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        //设置内容类型
        response.setContentType("application/octet-stream");
        //设置头及文件命名。
        response.setHeader("Content-Disposition", "attachment;filename="
                + URLEncoder.encode("系统访问次数排行("+loginTimeStart
                +"_"+loginTimeEnd+").xls", StandardCharsets.UTF_8.name()));
        //写出流
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        workBook.write(outputStream);
    }

    @Override
    public void exportLeaderAndOrgExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");

        Map<String, Date> map = DateTools.getStartAndEndDate(loginTimeStart, loginTimeEnd);
        Date loginTimeStartDate = map.get("loginTimeStart");
        Date loginTimeEndDate = map.get("loginTimeEnd");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = sdf.format(loginTimeStartDate);
        String endTime = sdf.format(loginTimeEndDate);

        //功能描述：把同一个表格多个sheet测试结果重新输出
        // 创建参数对象（用来设定excel的sheet1内容等信息）
        ExportParams exportParams = new ExportParams();
        // 设置sheet得名称
        exportParams.setTitle("领导访问统计表");
        // 设置sheet表头名称
        exportParams.setSheetName("领导访问统计表");
        // 创建sheet1使用得map
        Map<String, Object> oneExportMap = new HashMap<>();
        // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
        oneExportMap.put("title", exportParams);
        // 模版导出对应得实体类型
        oneExportMap.put("entity", LeaderAccessNumEO.class);
        // 查找组织使用人数和访问次数
        JSONObject jsonObj = new JSONObject();
        jsonObj.put("id", "192a47ff-41a7-41d9-8cc6-f012dd729ed7");
        Role role = roleService.findOneRoleByID(jsonObj);
        Optional.ofNullable(role).map(Role::getUserList)
            .ifPresent(users -> {
                List<String> userNoList = users.stream()
                        .sorted(Comparator.comparing(User::getRemarks))
                        .map(User::getUserNo)
                        .collect(Collectors.toList());
                List<Map<String, Object>> mapList = pageLogDao.findLeaderAccessNum(startTime, endTime, userNoList);
                // List＜Map＞转List＜Entity＞
                List<LeaderAccessNumEO> dataList = JSON.parseObject(JSON.toJSONString(mapList), new TypeReference<List<LeaderAccessNumEO>>() {});
                oneExportMap.put("data", dataList);
            });

        List<AccessNumEO> accessNumEOList = this.calcOrgNum(startTime, endTime);
        exportParams = new ExportParams();
        exportParams.setTitle("单位访问统计表");
        exportParams.setSheetName("单位访问统计表");
        Map<String, Object>  twoExportMap = new HashMap<>();
        twoExportMap.put("title", exportParams);
        twoExportMap.put("entity", OrgAccessNumEO.class);

        // 获取厂处数据
        List<AccessNumEO> dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 1).collect(Collectors.toList());
        List<AccessNumEO> dataNewList = dataList.stream()
                .filter(eo -> !"X87000000".equals(eo.getCode())
                        && !"X50050000".equals(eo.getCode())
                        && !"*********".equals(eo.getCode())
                        && !"*********".equals(eo.getCode()))
                .sorted(Comparator.comparing(entity -> (double) entity.getNum() / entity.getUseNum(), Comparator.reverseOrder()))
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .collect(Collectors.toList());

        List<Map<String, Object>> cMapList = pageLogDao.findLevelAccessNum(startTime, endTime, "处长");
        List<Map<String, Object>> cMapList2 = pageLogDao.findLevelAccessNum(startTime, endTime, "厂长");
        cMapList.addAll(cMapList2);

        List<Map<String, Object>> eMapList = pageLogDao.findLevelAccessNum(startTime, endTime, "主任");
        List<Map<String, Object>> feedMapList = pageLogDao.findFeedBackNum(startTime, endTime);

        // 查找部门应该访问人数
        List<Map<String, Object>> shouldUseNumList = pageLogDao.findShouldUseNum();

        List<OrgAccessNumEO> combinedList = new ArrayList<>();
        for (AccessNumEO obj1 : dataNewList) {
            String orgCode = obj1.getCode();
            Map<String, Object> obj2 = findObjectByOrgCode(cMapList, orgCode);
            Map<String, Object> obj3 = findObjectByOrgCode(eMapList, orgCode);
            Map<String, Object> obj4 = findObjectByOrgCode(feedMapList, orgCode);

            Long shouldUseNum = shouldUseNumList.stream()
                    .filter(e -> orgCode.contains((String) e.get("subOrgCode")))
                    .map(e -> ((Double) e.get("num")).longValue())
                    .findFirst()
                    .orElse(obj1.getOrgNum());
            OrgAccessNumEO orgAccessNumEO = new OrgAccessNumEO(obj1.getCode(), obj1.getName(),
                    obj1.getOrgNum(), shouldUseNum, obj1.getUseNum(),
                    new BigDecimal(String.valueOf(obj1.getUseNum()*100/shouldUseNum)).setScale(0, BigDecimal.ROUND_HALF_UP),
                    obj1.getNum(),
                    (obj2 != null ? (BigDecimal) obj2.get("accessNum") : 0).longValue(),
                    (obj3 != null ? (BigDecimal) obj3.get("accessNum") : 0).longValue(),
                    (obj4 != null ? (BigDecimal) obj4.get("feedBackNum") : 0).longValue());
            combinedList.add(orgAccessNumEO);
        }

        List<OrgAccessNumEO> collect = combinedList.stream()
                .sorted(Comparator.comparing(e -> e.getNum() / e.getShouldUseNum(), Comparator.reverseOrder()))
                .map(e -> {
                    e.setAvgAccessRank(combinedList.indexOf(e) + 1);
                    return e;
                })
                .sorted(Comparator.comparing(e -> new BigDecimal(e.getFeedBackNum())
                                .divide(new BigDecimal(e.getUseNum()), 4, BigDecimal.ROUND_HALF_UP),
                        Comparator.reverseOrder()))
                .collect(Collectors.toList());

        for (int i = 0; i < collect.size(); i++) {
            collect.get(i).setAvgFeedBackRank(i + 1);
        }
        List<OrgAccessNumEO> collect1 = collect.stream()
                .sorted(Comparator.comparingInt(OrgAccessNumEO::getAvgAccessRank))
                .collect(Collectors.toList());

        twoExportMap.put("data", collect1);

        // 将sheet使用得map进行包装
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        //后续增加sheet组，则后面继续追加即可;
        sheetsList.add(oneExportMap);
        sheetsList.add(twoExportMap);

        // 执行方法
        @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
        //设置编码格式
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        //设置内容类型
        response.setContentType("application/octet-stream");
        //设置头及文件命名。
        response.setHeader("Content-Disposition", "attachment;filename="
                + URLEncoder.encode("领导与单位访问统计("+loginTimeStart
                +"_"+loginTimeEnd+").xls", StandardCharsets.UTF_8.name()));
        //写出流
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        workBook.write(outputStream);
    }

    private Map<String, Object> findObjectByOrgCode(List<Map<String, Object>> list, String orgCode) {
        for (Map<String, Object> obj : list) {
            if (orgCode.contains((String)obj.get("subOrgCode"))) {
                return obj;
            }
        }
        return null;
    }

    @Override
    public JSONObject exportWord(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");

        Map<String, Date> map = DateTools.getStartAndEndDate(loginTimeStart, loginTimeEnd);
        Date loginTimeStartDate = map.get("loginTimeStart");
        Date loginTimeEndDate = map.get("loginTimeEnd");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = sdf.format(loginTimeStartDate);
        String endTime = sdf.format(loginTimeEndDate);

        JSONObject json = new JSONObject();
        // 汇总所有使用人数和访问次数
        List<AccessNumEO> accessNumEOList = appLogDao.findSummaryNumByLoginTime(loginTimeStartDate, loginTimeEndDate);
        json.put("summary", accessNumEOList);

        // 查找组织使用人数和访问次数
//        accessNumEOList = appLogDao.findGroupOrgByLoginTime(loginTimeStartDate, loginTimeEndDate);
        // 计算组织及所有子组织人数之和
        accessNumEOList = this.calcOrgNum(startTime, endTime);
        // 获取厂处数据
        List<AccessNumEO> dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 1)
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        json.put("org-1", dataList);

        // 获取科室数据
        dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 2)
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        json.put("org-2", dataList);

        // 获取班组数据
        dataList = accessNumEOList.stream()
                .filter(eo -> eo.getLevel() != null && eo.getLevel() == 3)
                .map(eo -> {
                    eo.setName(eo.getName().replace("板材事业部", ""));
                    return eo;
                })
                .sorted(Comparator.comparing(AccessNumEO::getNum).reversed())
                .collect(Collectors.toList());
        json.put("org-3", dataList);


        // 查询用户访问次数
        /*dataList = appLogDao.findGroupUserByLoginTime(loginTimeStartDate, loginTimeEndDate).stream()
                .map(eo -> {
                    eo.setOrgName(eo.getOrgName().replace("板材事业部", ""));
                    return eo;
                }).collect(Collectors.toList());*/

        JSONObject paramJson = new JSONObject();
        paramJson.put("pageIndex", 1);
        paramJson.put("pageSize", 5000);
        Pageable pageable = ToolsUtil.initPage(paramJson);
        // 查找用户页面访问数据
//        Page<Map<String, Object>> userPageAccessData =
//                pageLogDao.findUserPageAccessList(startTime, endTime, null , null, null, null, pageable);
//        List<Map<String, Object>> mapList = userPageAccessData.getContent();

        List<Map<String, Object>> mapList =
                pageLogDao.findUserPageAccessList2(startTime, endTime, null , null, null, null);

        dataList = mapList.stream()
                .map(m -> new AccessNumEO((String) m.get("userNo"),
                        (String) m.get("userName"),
                        Long.valueOf(m.get("accessNum").toString()),
                        ((String) m.get("orgAllName")).replace("板材事业部","")))
                .collect(Collectors.toList());

        json.put("user", dataList);


        // 查询应用访问次数
        /*dataList = appLogDao.findGroupAppByLoginTime(loginTimeStartDate, loginTimeEndDate)
                .stream()
                .filter(accessNumEO -> !accessNumEO.getCode().contains("old") && !"res".equals(accessNumEO.getCode()))
                .collect(Collectors.toList());*/

        mapList = pageLogDao.findPageAccessNumByService(startTime, endTime);
        dataList = mapList.stream()
                .map(m -> new AccessNumEO((String) m.get("code"),
                        Long.valueOf(m.get("num").toString())))
                .collect(Collectors.toList());
        dataList = this.writeServiceName(dataList);
        json.put("app", dataList);

        Integer recomHandledNum  = 0;
        Integer bugHandledNum  = 0;
        Integer recomUnHandledNum = 0;
        Integer bugUnHandledNum = 0;
        // 查询问题反馈和合理化建议处理数
        List<Integer> statusList = Arrays.asList(2, 4);
        List<Map<String, Integer>> handledNumList = feedBackService.findCountByStatus(statusList, loginTimeStartDate, loginTimeEndDate);
        for (Map<String, Integer> handledNumMap : handledNumList) {
            if (handledNumMap.get("quesType") == 1) {// 合理化建议
                String num = String.valueOf(handledNumMap.get("num"));
                recomHandledNum = Integer.parseInt(num);
            }
            if (handledNumMap.get("quesType") == 2) {// 问题反馈
                String num = String.valueOf(handledNumMap.get("num"));
                bugHandledNum = Integer.parseInt(num);
            }
        }

        statusList = Arrays.asList(1, 3);
        List<Map<String, Integer>> unHandledNumList = feedBackService.findCountByStatus(statusList, loginTimeStartDate, loginTimeEndDate);
        for (Map<String, Integer> unHandledNumMap : unHandledNumList) {
            if (unHandledNumMap.get("quesType") == 1) {// 合理化建议
                String num = String.valueOf(unHandledNumMap.get("num"));
                recomUnHandledNum = Integer.parseInt(num);
            }
            if (unHandledNumMap.get("quesType") == 2) {// 问题反馈
                String num = String.valueOf(unHandledNumMap.get("num"));
                bugUnHandledNum = Integer.parseInt(num);
            }
        }

        JSONObject jsonObj1 = new JSONObject();
        jsonObj1.put("handledNum", recomHandledNum);
        jsonObj1.put("unHandledNum", recomUnHandledNum);
        jsonObj1.put("totalNum", recomHandledNum + recomUnHandledNum);
        json.put("feedBack-recom", jsonObj1);

        JSONObject jsonObj2 = new JSONObject();
        jsonObj2.put("handledNum", bugHandledNum);
        jsonObj2.put("unHandledNum", bugUnHandledNum);
        jsonObj2.put("totalNum", bugHandledNum + bugUnHandledNum);
        json.put("feedBack-bug", jsonObj2);

        return json;
    }

    /**
     * 计算组织及所有子组织人数之和
     *
     * @param startTime 登录时间开始
     * @param endTime   登录时间结束
     * @return {@link List }<{@link AccessNumEO}>
     * <AUTHOR>
     * @create 2022-11-10
     */
    private List<AccessNumEO> calcOrgNum(String startTime, String endTime) {
        List<AccessNumEO> eoNewList = new ArrayList<>();
        // 查找板材事业部所有组织
        List<Map<String, Object>> mapList = appLogDao.findOrgUsers(startTime, endTime);
        // List＜Map＞转List＜Entity＞
        List<AccessNumEO> list = JSON.parseObject(JSON.toJSONString(mapList), new TypeReference<List<AccessNumEO>>() {});

        for (AccessNumEO accessNumEO : list) {
            Long orgNum = 0L;
            Long shouldUseNum = 0L;
            Long useNum = 0L;
            Long num = 0L;
            if ("*********".equals(accessNumEO.getParentCode())) {
                String code = accessNumEO.getCode();
                orgNum += accessNumEO.getOrgNum() != null ? accessNumEO.getOrgNum() : 0;
                useNum += accessNumEO.getUseNum() != null ? accessNumEO.getUseNum() : 0;
                num += accessNumEO.getNum() != null ? accessNumEO.getNum() : 0;
                if (orgNum == 0) {// 过滤部门人数为0
                    continue;
                }
                for (AccessNumEO eo : list) {
                    Long orgNum1 = 0L;
                    Long shouldUseNum1 = 0L;
                    Long useNum1 = 0L;
                    Long num1 = 0L;
                    if (code.equals(eo.getParentCode())) {
                        String code1 = eo.getCode();
                        orgNum += eo.getOrgNum() != null ? eo.getOrgNum() : 0;
                        orgNum1 += eo.getOrgNum() != null ? eo.getOrgNum() : 0;
                        shouldUseNum += eo.getShouldUseNum() != null ? eo.getShouldUseNum() : 0;
                        shouldUseNum1 += eo.getShouldUseNum() != null ? eo.getShouldUseNum() : 0;
                        useNum += eo.getUseNum() != null ? eo.getUseNum() : 0;
                        useNum1 += eo.getUseNum() != null ? eo.getUseNum() : 0;
                        num += eo.getNum() != null ? eo.getNum() : 0;
                        num1 += eo.getNum() != null ? eo.getNum() : 0;

                        for (AccessNumEO eo2 : list) {
                            if (code1.equals(eo2.getParentCode())) {
                                orgNum += eo2.getOrgNum() != null ? eo2.getOrgNum() : 0;
                                orgNum1 += eo2.getOrgNum() != null ? eo2.getOrgNum() : 0;
                                shouldUseNum += eo2.getShouldUseNum() != null ? eo2.getShouldUseNum() : 0;
                                shouldUseNum1 += eo2.getShouldUseNum() != null ? eo2.getShouldUseNum() : 0;
                                useNum += eo2.getUseNum() != null ? eo2.getUseNum() : 0;
                                useNum1 += eo2.getUseNum() != null ? eo2.getUseNum() : 0;
                                num += eo2.getNum() != null ? eo2.getNum() : 0;
                                num1 += eo2.getNum() != null ? eo2.getNum() : 0;

                                Long orgNum2 = eo2.getOrgNum();
                                Long shouldUseNum2 = eo2.getShouldUseNum();
                                Long useNum2 = eo2.getUseNum();
                                eo2.setOrgNum(orgNum2);
                                eo2.setShouldUseNum(shouldUseNum2);
                                eo2.setUseNum(useNum2);
                                eo2.setNum(eo2.getNum());
                                eo2.setAvgNum(useNum2 != 0? eo2.getNum()/useNum2 : 0);
                                BigDecimal rate = shouldUseNum2 != 0 ? new BigDecimal(useNum2).multiply(new BigDecimal(100))
                                        .divide(new BigDecimal(shouldUseNum2),0, BigDecimal.ROUND_HALF_UP)
                                        : BigDecimal.ZERO;
                                eo2.setRate(rate);
                                eo2.setLevel(3);
                                eoNewList.add(eo2);
                            }
                        }
                        eo.setOrgNum(orgNum1);
                        eo.setShouldUseNum(shouldUseNum1);
                        eo.setUseNum(useNum1);
                        eo.setNum(num1);
                        eo.setAvgNum(useNum1 != 0? num1/useNum1 : 0);
                        BigDecimal rate = shouldUseNum1 != 0 ? new BigDecimal(useNum1).multiply(new BigDecimal(100))
                                .divide(new BigDecimal(shouldUseNum1),0, BigDecimal.ROUND_HALF_UP)
                                : BigDecimal.ZERO;
                        eo.setRate(rate);
                        eo.setLevel(2);
                        eoNewList.add(eo);
                    }
                }
                accessNumEO.setOrgNum(orgNum);
                accessNumEO.setShouldUseNum(shouldUseNum);
                accessNumEO.setUseNum(useNum);
                accessNumEO.setNum(num);
                accessNumEO.setAvgNum(useNum != 0 ? num/useNum : 0);
                BigDecimal rate = shouldUseNum != 0 ? new BigDecimal(useNum).multiply(new BigDecimal(100))
                        .divide(new BigDecimal(shouldUseNum),0, BigDecimal.ROUND_HALF_UP)
                        : BigDecimal.ZERO;
                accessNumEO.setRate(rate);
                accessNumEO.setLevel(1);
                eoNewList.add(accessNumEO);
            }
        }
        return eoNewList;
    }

    /**
     * 写入应用名称
     *
     * @param accessNumEOList
     * <AUTHOR>
     * @create 2022-11-01
     * @return
     */
    private List<AccessNumEO> writeServiceName(List<AccessNumEO> accessNumEOList) {


//        BasicDataConfig basicDataConfig=basicDataConfigService.findBDCbyType("serviceInfo");
//        String content=basicDataConfig.getContent();
//
//
//
////        // 数据中台 数据字典 可获取应用名称
////        String url = "http://************:9800/idm/basicDataConfig/findBasicDataConfigByType.form";
//////        String url = "http://iet-idm-service/basicDataConfig/findBasicDataConfigByType.form";
////
////        JSONObject json = new JSONObject();
////        json.put("type", "serviceInfo");
////        JSONObject resJson = restTemplate.postForObject(url, json, JSONObject.class);
////
////        String content = resJson.getString("content");
//        JSONArray jsonArray = JSONObject.parseArray(content);
//        List<Map<String, String>> serviceList = new ArrayList<>();
//        for (int i = 0; i < jsonArray.size(); i++) {
//            String obj = jsonArray.getString(i);
//            JSONObject jsonObj = JSONObject.parseObject(obj);
//
//            Map<String, String> map = new HashMap<>();
//            map.put("serviceNo", jsonObj.getString("name"));
//            map.put("serviceName", jsonObj.getString("cname"));
//            serviceList.add(map);
//        }

        List<DictionaryDtl> dictionaryDtlList= dictionaryDtlService.findByDictId("e38d0f03-3755-42ba-a17a-d078f01cfab3");
        List<Map<String, String>> serviceList = new ArrayList<>();

        JSONArray jsonArray =new JSONArray();
        for(int i = 0; i < dictionaryDtlList.size(); i++){
            JSONObject jsonObj =JSONObject.parseObject(dictionaryDtlList.get(i).getValue());
            Map<String, String> map = new HashMap<>();
            map.put("PORT",jsonObj.getString("PORT").toString());
            map.put("CODE",dictionaryDtlList.get(i).getCode());
            map.put("IP",jsonObj.getString("IP").toString());
            map.put("NAME",jsonObj.getString("NAME").toString());
            serviceList.add(map);
        }
        for (AccessNumEO accessNumEO : accessNumEOList) {
            String serviceNo = accessNumEO.getCode();
            for (Map<String, String> map : serviceList) {
                if (map.get("CODE").equals(serviceNo)) {
                    accessNumEO.setName(map.get("NAME"));
                    break;
                }
            }
        }
        // 过滤掉应用名称不存在的应用
        accessNumEOList = accessNumEOList.stream()
                .filter(accessNum -> StringUtils.isNotBlank(accessNum.getName()))
                .collect(Collectors.toList());
        return accessNumEOList;
    }

    @Override
    public void save(AppLog appLog, HttpServletRequest request) {
        Assert.hasText(appLog.getServiceNo(), "应用编号不能为空");

        User user = UserThreadUtil.getUser();
        Assert.notNull(user, "该员工编号不存在");
        String userNo = user.getUserNo();

        // 开发人员不保存应用访问记录
        if ("dev".equals(user.getRemarks())) {
            return;
        }

        JSONObject json = new JSONObject();
        json.put("userNo", user.getUserNo());
        json.put("serviceNo", appLog.getServiceNo());
        json.put("startTime", DateTools.getDayStartTime());
        json.put("endTime", DateTools.getDayEndTime());
        // 按多条件查找全部
        List<AppLog> appLogList = this.findAllByMultiCondition(json);
        if (appLogList.size() > 0) {
            // 今天已保存应用访问记录
            return;
        }
        appLog.setUserNo(userNo);
        String orgCode = user.getOrgCode();
        appLog.setOrgCode(orgCode);// 组织编号

        Map<String, Object> map = this.getOneOrgCode(orgCode, 1);
        String oneOrgCode = (String) map.get("orgCode");
        Integer level = (Integer) map.get("level");
        appLog.setOneOrgCode(oneOrgCode);
        appLog.setLevel(level);
        String ipAddress = IpUtil.getIpAddress(request);
        appLog.setIpAddress(ipAddress);// ip地址
//        appLog.setLoginCity(IpUtil.getCityByIP(ipAddress));// 登录城市
        appLogDao.save(appLog);

    }

    @Override
    public List<AccessNumEO> findGroupAppByLoginTime(JSONObject jsonObject) {
        String loginTimeStart = jsonObject.getString("loginTimeStart");
        String loginTimeEnd = jsonObject.getString("loginTimeEnd");
        Map<String, Date> map = DateTools.getStartAndEndDate(loginTimeStart, loginTimeEnd);
        Date loginTimeStartDate = map.get("loginTimeStart");
        Date loginTimeEndDate = map.get("loginTimeEnd");
        // 查询应用访问次数
        List<AccessNumEO> accessNumEOList = appLogDao.findGroupAppByLoginTime(loginTimeStartDate, loginTimeEndDate);
        return accessNumEOList;
    }

    /**
     * 获取厂处组织编号
     *
     * @param orgCode 组织编号
     * @return {@link String }
     * <AUTHOR>
     * @create 2022-11-03
     */
    private Map<String, Object> getOneOrgCode(String orgCode, Integer level) {
        Org org = orgService.findByOrgCode(orgCode);
        String parentOrgCode = org.getParentOrgCode();
        Map<String, Object> map = new HashMap<>();
        if ("*********".equals(parentOrgCode)) {// 父级是板材事业部
            map.put("orgCode", orgCode);
            map.put("level", level);
            return map;
        } else if ("X".equals(parentOrgCode)) {// 根级是总公司
            if ("*********".equals(orgCode)) {// 板材事业部
                map.put("level", 0);
            } else {// 与板材事业部同级的其他部门
                map.put("level", -1);
            }
            map.put("orgCode", "X");
            return map;
        } else {
            return getOneOrgCode(parentOrgCode, level+1);
        }
    }

    private Specification<AppLog> createSpecs(JSONObject json) {
        Specification<AppLog> specs = (root, query, cb) -> {
            List<Predicate> list = new LinkedList<>();

            String serviceNo = json.getString("serviceNo");// 应用编号
            String userNo = json.getString("userNo");// 员工编号
            String orgCode = json.getString("orgCode");// 组织编号
            String startTime = json.getString("startTime");//起始日期
            String endTime = json.getString("endTime");//结束日期

            if (StringUtils.isNotBlank(serviceNo)) {
                list.add(cb.equal(root.get("serviceNo"), serviceNo));
            }
            if (StringUtils.isNotBlank(userNo)) {
                list.add(cb.equal(root.get("userNo"), userNo));
            }
            if (StringUtils.isNotBlank(orgCode)) {
                list.add(cb.equal(root.get("orgCode"), orgCode));
            }
            if (StringUtils.isNotBlank(startTime)) {
                list.add(cb.greaterThanOrEqualTo(root.get("loginTime").as(String.class), startTime));
            }
            if (StringUtils.isNotBlank(endTime)) {
                list.add(cb.lessThanOrEqualTo(root.get("loginTime").as(String.class), endTime));
            }
            return query.where(list.toArray(new Predicate[list.size()])).getRestriction();
        };
        return specs;
    }
}
