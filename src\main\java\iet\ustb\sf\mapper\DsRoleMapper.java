package iet.ustb.sf.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import iet.ustb.sf.vo.domain.Role;
import org.apache.ibatis.annotations.Param;

/**
 * (Role)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-13 08:22:56
 */
public interface DsRoleMapper extends BaseMapper<Role> {

    /**
    * 批量新增数据（MyBatis原生foreach方法）
    *
    * @param entities List<Role> 实例对象列表
    * @return 影响行数
    */
    int insertBatch(@Param("entities") List<Role> entities);

    /**
    * 批量新增或按主键更新数据（MyBatis原生foreach方法）
    *
    * @param entities List<Role> 实例对象列表
    * @return 影响行数
    * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
    */
    int insertOrUpdateBatch(@Param("entities") List<Role> entities);

    /**
     * 根据ID删除相关角色
     * @param role
     * @return
     */
    int deleteByID(Role role);

    /**
     * 级联删除角色和菜单的关联关系
     * @param role
     * @return
     */
    int deleteResByRoleID(Role role);

    /**
     * 级联删除角色和用户的关联关系
     * @param role
     * @return
     */
    int deleteUserByRoleID(Role role);

    /**
     * 获取角色信息
     * @param role
     * @return
     */
    List<Role> getRoleInfo(Role role);

}

