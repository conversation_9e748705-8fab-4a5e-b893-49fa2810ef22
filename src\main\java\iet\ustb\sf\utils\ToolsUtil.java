package iet.ustb.sf.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.BaseEntity;
import lombok.Cleanup;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.data.domain.*;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @title: ToolsUtil
 * @projectName IOM
 * @description: TODO
 * @date 2021/11/1615:03
 */

public class ToolsUtil {
    //首字母转大写
    public static String toUpperCaseFirstOne(String s) {
        if (Character.isUpperCase(s.charAt(0)))
            return s;
        else
            return (new StringBuilder()).append(Character.toUpperCase(s.charAt(0))).append(s.substring(1)).toString();
    }


    public static <T> Predicate[] buildPredicateArray(JSONObject jsonObject, CriteriaBuilder builder, Root<T> entity) {
        List<Predicate> predicateList = new ArrayList<>();
        Predicate p = null;
        for (Map.Entry<String, Object> item : jsonObject.entrySet()) {

            String key = item.getKey();
            Object value = item.getValue();

            if ("pageSize".equals(key)) {
                continue;
            }

            if ("pageIndex".equals(key)) {
                continue;
            }

            if ("startTime".equals(key)) {
                continue;
            }

            if ("endTime".equals(key)) {
                continue;
            }

            if ("isFinish".equals(key)) {
                continue;
            }

            if (!StringUtils.isEmpty(value) && !"null".equals(value)) {
                p = builder.and(builder.equal(entity.get(key), value));
                predicateList.add(p);
            }
        }

        Predicate[] predArray = new Predicate[predicateList.size()];
        predicateList.toArray(predArray);
        return predArray;
    }


    public static <T> List setToList(Set<T> set) {
        List<T> list = new ArrayList(set.size() + 1);
        list.addAll(set);
        return list;
    }


    public static <T> List subList(List<T> targetList, int pageIndex, int pageSize) {
        if (isEmpty(targetList)) {
            return new ArrayList();
        } else {
            if (pageIndex * pageSize > targetList.size()) {
                return targetList.subList((pageIndex - 1) * pageSize, targetList.size());
            } else {
                return targetList.subList((pageIndex - 1) * pageSize, pageIndex * pageSize);
            }
        }
    }


    public static String buildPagerOracleSql(String innerSql, int pageIndex, int pageSize) {
        StringBuilder sb = new StringBuilder("select * " +
                "from (select t.*, rownum as no " +
                "      from ( ");
        sb.append(innerSql);
        sb.append(" ) t) where no between " + ((pageIndex - 1) * pageSize + 1) + " and " + pageIndex * pageSize);
        return sb.toString();
    }


    public static String buildPagerMySql(String innerSql, int pageIndex, int pageSize) {
        StringBuilder sb = new StringBuilder(innerSql);
        sb.append(" limit ");
        sb.append((pageIndex - 1) * pageSize);
        sb.append(" , ");
        sb.append(pageSize);
        return sb.toString();
    }

    public static boolean isEmpty(@Nullable Object obj) {
        if (obj == null) {
            return true;
        }

        if (obj instanceof Optional) {
            return !((Optional<?>) obj).isPresent();
        }
        if (obj instanceof CharSequence) {
            return ((CharSequence) obj).length() == 0;
        }
        if (obj.getClass().isArray()) {
            return Array.getLength(obj) == 0;
        }
        if (obj instanceof Collection) {
            return ((Collection<?>) obj).isEmpty();
        }
        if (obj instanceof Map) {
            return ((Map<?, ?>) obj).isEmpty();
        }

        if (obj instanceof String) {
            return " ".equals(obj) || StringUtils.isEmpty(obj) || "null".equals(obj);
        }

        // else
        return false;
    }


    public static String filterEmptyData(Object object) {
        if (isEmpty(object)) {
            return "";
        } else {
            if (object instanceof Date) {
                return DateUtils.formatDate((Date) object);
            }
            return String.valueOf(object);
        }
    }

    public static <T> T jsonObjectToEntity(JSONObject jsonObject, Class<T> clazz) {
        try {
            if (isEmpty(jsonObject)) {
                return null;
            } else {
                return JSONObject.parseObject(JSON.toJSONString(jsonObject), clazz);
            }
        } catch (Exception e) {
            return null;
        }
    }

    public static <T> T jsonObjectToEntity(Object object, Class<T> clazz) {
        try {
            if (isEmpty(object)) {
                return null;
            } else {
                return JSONObject.parseObject(JSON.toJSONString(object), clazz);
            }
        } catch (Exception e) {
            return null;
        }
    }

    public static <T> List<T> jsonObjectToEntityList(JSONObject jsonObject, Class<T> clazz) {
        try {
            if (isEmpty(jsonObject)) {
                return null;
            } else {
                return JSONArray.parseArray(JSON.toJSONString(jsonObject), clazz);
            }
        } catch (Exception e) {
            return null;
        }
    }


    public static <T> List<T> jsonObjectToEntityList(Object object, Class<T> clazz) {
        try {
            if (isEmpty(object)) {
                return null;
            } else {
                return JSONArray.parseArray(JSON.toJSONString(object), clazz);
            }
        } catch (Exception e) {
            return null;
        }
    }

    public static Pageable initPage(JSONObject jsonObject) {

        int pageIndex = isEmpty(jsonObject.get("pageIndex")) ? 1 : jsonObject.getIntValue("pageIndex");
        int pageSize = isEmpty(jsonObject.get("pageSize")) ? 10 : jsonObject.getIntValue("pageSize");

        String sort = jsonObject.getString("sort");// 排序字段
        String order = jsonObject.getString("order");// 排序类型 DESC ASC

        Pageable pageable;
        if (!isEmpty(sort)) {
            Sort.Direction sortType;
            if (!isEmpty(order)) {// 默认排序：倒序
                sortType = Sort.Direction.DESC;
            } else {
                sortType = Sort.Direction.valueOf(order.toUpperCase());
            }
            pageable = PageRequest.of(pageIndex - 1, pageSize, Sort.by(sortType, sort));
        } else {
            pageable = PageRequest.of(pageIndex - 1, pageSize);
        }
        return pageable;

    }

    public static <T extends BaseEntity> void removeItemFormList(List<T> list, T obj) {
        Iterator<T> it = list.iterator();
        while (it.hasNext()) {
            T item = it.next();
            if (item.getId().equals(obj.getId())) {
                it.remove();
            }
        }
    }

    /**
     * List转Page
     *
     * @param list     列表
     * @param pageable 可分页
     * @return {@link Page }<{@link T }>
     * <AUTHOR>
     * @create 2022-09-29
     */
    public static <T> Page<T> listToPage(List<T> list, Pageable pageable) {
        if (list == null) {
            throw new IllegalArgumentException("list不能为空");
        }

        int startOfPage = pageable.getPageNumber() * pageable.getPageSize();
        if (startOfPage > list.size()) {
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }

        int endOfPage = Math.min(startOfPage + pageable.getPageSize(), list.size());
        return new PageImpl<>(list.subList(startOfPage, endOfPage), pageable, list.size());
    }

    /**
     * 实体类转map
     *
     * @param object 对象
     * @return {@link Map }<{@link String }, {@link String }>
     * <AUTHOR>
     * @create 2022-11-03
     */
    public static Map<String, String> entity2Map(Object object) throws Exception {
        Map<String, String> map = new HashMap<>();
        Class<?> clazz = object.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String value = field.get(object) != null ? field.get(object).toString() : "";
            map.put(field.getName(), value);
        }
        return map;
    }


    /**
     * 判断字符串是否为数字（包括整数和小数）
     * @param str
     * @return
     */
    public static boolean isNumeric(String str) {

        Pattern pattern = Pattern.compile("-?[0-9]+.?[0-9]+");
        Matcher isNum = pattern.matcher(str);

        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    public static String replaceLast(String origin , String regex, String replacement){
        StringBuilder stringBuilder = new StringBuilder(origin);
        String reverse = stringBuilder.reverse().toString();
        return new StringBuilder(reverse.replaceFirst(regex , replacement)).reverse().toString();
    }

    /**
     * excel下载
     *
     * @param fileName 下载时的文件名称
     * @param response
     * @param workbook excel数据
     * <AUTHOR>
     * @create 2023-04-07
     */
    public static void downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook) {
        try {
            @Cleanup OutputStream out = response.getOutputStream();
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xls", "UTF-8"));
            @Cleanup ByteArrayOutputStream baos = new ByteArrayOutputStream();
            workbook.write(baos);
            response.setHeader("Content-Length", String.valueOf(baos.size()));
            out.write(baos.toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    //去除字符串最后一个字符
    public static String cutLastChar(String string){
        if(isEmpty(string) || string.length() == 1){
            return "";
        }
        return string.substring(0, string.toString().length() - 1);
    }
}
