package iet.ustb.sf.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import iet.ustb.sf.dao.SysNoticeDao;
import iet.ustb.sf.mapper.SysNoticeUserMapper;
import iet.ustb.sf.vo.domain.SysNotice;
import iet.ustb.sf.vo.domain.SysNoticeUser;
import iet.ustb.sf.vo.domain.User;
import iet.ustb.sf.service.SysNoticeService;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.utils.UserThreadUtil;
import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 系统公告
 *
 * <AUTHOR>
 * @create 2024-02-18
 */
@Service
public class SysNoticeServiceImpl implements SysNoticeService {
    @Autowired
    private SysNoticeDao sysNoticeDao;

    @Autowired
    private SysNoticeUserMapper sysNoticeUserMapper;

    @Override
    public List<SysNotice> findAll() {
        return sysNoticeDao.findAll();
    }

    @Override
    public List<SysNotice> findAllByMultiCondition(JSONObject jsonObject) {
        return sysNoticeDao.findAll(createSpecs(jsonObject));
    }

    @Override
    public Page<SysNotice> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        return sysNoticeDao.findAll(createSpecs(jsonObject), pageable);
    }

    /**
     * 根据用户账号获取公告
     * @param sysNotice
     * @return
     */
    @Override
    public List<SysNotice> getNoticeByUserNo(SysNotice sysNotice) {
        List<SysNotice> sysNoticeList = new ArrayList<>();
        sysNoticeList = sysNoticeUserMapper.getNoticeByUserNo(sysNotice);

        return sysNoticeList;
    }


    private Specification<SysNotice> createSpecs(JSONObject json) {
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            String title = json.getString("title");// 标题
            String content = json.getString("content");// 内容
            String startTime = json.getString("startTime");// 开始时间
            String endTime = json.getString("endTime");// 结束时间
            Integer status = json.getInteger("status");// 状态 ：1-草稿 2-发布 3-废弃
            Integer noticeCategory = json.getInteger("noticeCategory");// 公告分类 ：1-信息发布 2-系统公告
            Integer noticeType = json.getInteger("noticeType");// 公告类型 ：1-文字 2-图片 3-视频
            Integer noticeDept = json.getInteger("noticeDept");// 公告部门 ：0-事业部 1-板卷厂 2-宽板厂 3-中板厂 4-一炼钢厂 5-金石厂 6-金润厂

            if (StringUtils.isNotBlank(title)) {
                list.add(cb.like(root.get("title"), "%" + title + "%"));
            }
            if (StringUtils.isNotBlank(content)) {
                list.add(cb.like(root.get("content"), "%" + content + "%"));
            }
            if (StringUtils.isNotBlank(startTime)) {
                list.add(cb.greaterThanOrEqualTo(root.get("startTime").as(String.class), startTime));
            }
            if (StringUtils.isNotBlank(endTime)) {
                list.add(cb.lessThanOrEqualTo(root.get("endTime").as(String.class), endTime));
            }
            if (status != null) {
                list.add(cb.equal(root.get("status"), status));
            }
            if (noticeCategory != null) {
                list.add(cb.equal(root.get("noticeCategory"), noticeCategory));
            }
            if (noticeType != null) {
                list.add(cb.equal(root.get("noticeType"), noticeType));
            }
            if (noticeDept != null) {
                if (noticeDept == 0) {// 事业部 只展示事业部公告
                    list.add(cb.equal(root.get("noticeDept"), noticeType));
                } else {// 分厂 展示事业部与该分厂的公告
                    List<Integer> noticeDeptList = Arrays.asList(0, noticeDept);
                    list.add(root.get("noticeDept").in(noticeDeptList));
                }
            }
            query.where(list.toArray(new Predicate[list.size()]));
            query.orderBy(cb.desc(root.get("createDateTime")));
            return query.getRestriction();
        };
    }

    /**
     * 保存公告信息（包括更新和新增）
     * @param sysNotice
     * @return
     */
    @Override
    public SysNotice save(SysNotice sysNotice) {
        String id = sysNotice.getId();
        Integer status = sysNotice.getStatus();
        // 更新操作 且 发布状态
        if (StringUtils.isNotBlank(id) && status == 2) {
            sysNoticeDao.findById(id).ifPresent(sn -> {
                if (sn.getStatus() != 2) {// 原先不为发布状态
                    User user = UserThreadUtil.getUser();
                    sysNotice.setPublishUserName(user.getUserName());
                }
            });
        }

        //如果是更新操作，则需要清空公告已阅读的数据
        if(StringUtils.isNotBlank(id)){
            sysNoticeUserMapper.deleteByNoticeId(id);
        }

        return sysNoticeDao.save(sysNotice);
    }

    @Override
    public void saveAll(List<SysNotice> sysNotices) {
        sysNoticeDao.saveAll(sysNotices);
    }

    @Override
    public void delete(SysNotice sysNotice) throws Exception {
        sysNoticeDao.delete(sysNotice);
    }

    /**
     * 保存公告已阅读的用户
     * @param sysNotice
     */
    @Override
    public void saveNoticeByUser(SysNotice sysNotice) {
        List<SysNoticeUser> sysNoticeList = new ArrayList<>();
        if(CollUtil.isEmpty(sysNotice.getIdList())){
            return;
        }
        List<String> noticeIds = sysNotice.getIdList();
        noticeIds.forEach(e -> {
            SysNoticeUser sysNoticeUser = new SysNoticeUser();
            sysNoticeUser.setNoticeId(e);
            sysNoticeUser.setUserNo(sysNotice.getUserNo());
            sysNoticeList.add(sysNoticeUser);
        });
        sysNoticeUserMapper.insertOrUpdateBatch(sysNoticeList);

    }

    /**
     * 分页查询所有历史公告
     * @param sysNotice
     * @return
     */
    @Override
    public IPage<SysNotice> getNoticeByPage(SysNotice sysNotice) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<SysNotice> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(sysNotice.getPageIndex(), sysNotice.getPageSize());
        sysNoticeUserMapper.getNoticeByPage(page, sysNotice);

        return null;
    }
}
