package iet.ustb.sf.service.impl;

import iet.ustb.sf.mapper.SysNoticeUserMapper;
import iet.ustb.sf.vo.domain.SysNoticeUser;
import iet.ustb.sf.service.SysNoticeUserService;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import javax.annotation.Resource;

/**
 * 公告和用户关系表(SysNoticeUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-28 17:09:36
 */
@Service("sysNoticeUserService")
public class SysNoticeUserServiceImpl implements SysNoticeUserService {
    @Resource
    private SysNoticeUserMapper sysNoticeUserDao;


    /**
     * 分页查询
     *
     * @param sysNoticeUser 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    @Override
    public Page<SysNoticeUser> queryByPage(SysNoticeUser sysNoticeUser, PageRequest pageRequest) {
        long total = this.sysNoticeUserDao.count(sysNoticeUser);
        return new PageImpl<>(this.sysNoticeUserDao.queryAllByLimit(sysNoticeUser, pageRequest), pageRequest, total);
    }

    /**
     * 新增数据
     *
     * @param sysNoticeUser 实例对象
     * @return 实例对象
     */
    @Override
    public SysNoticeUser insert(SysNoticeUser sysNoticeUser) {
        this.sysNoticeUserDao.insert(sysNoticeUser);
        return sysNoticeUser;
    }


}
