//package iet.ustb.sf.schedule;
//
//import com.alibaba.fastjson.JSONObject;
//import iet.ustb.sf.dao.MessagePushMapDao;
//import iet.ustb.sf.dao.UserDao;
//import iet.ustb.sf.domain.MessagePushMap;
//import iet.ustb.sf.domain.User;
//import iet.ustb.sf.service.OrgService;
//import iet.ustb.sf.service.WarningInfoService;
//import iet.ustb.sf.service.WeComMessagePushService;
//import iet.ustb.sf.utils.DateUtils;
//import iet.ustb.sf.utils.ToolsUtil;
//import iet.ustb.sf.vo.DoneRateVo;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// *
// * @Author: Dr.Monster
// * @Title: BasicDataConfigTask
// * @Date: 23/12/27 09:0434
// * @Slogan: The never-compromising angry youth
// * @Remark:$ 定时推送：
// *
// * 规则1：前一天6点到第二天6点所有的报警
// * 推送时间：07:00:00
// *
// * 规则2：当天0点到17点所有的报警
// * 推送时间：17:30:00
// */
//
//@Component
//@EnableScheduling
//public class WarningAlertMessageTask {
//
//    //Test
////    List<String> userNoList = Arrays.asList(
////            "340502199406170210");
//
//    @Autowired
//    MessagePushMapDao messagePushMapDao;
//
//    @Autowired
//    WarningInfoService warningInfoService;
//
//    @Autowired
//    WeComMessagePushService weComMessagePushService;
//
//    @Value("${spring.profiles.active}")
//    String activeModel;
//
//    @Autowired
//    UserDao userDao;
//
//    @Autowired
//    OrgService orgService;
//
//    /**
//     * 推送报警统计消息（规则1）
//     */
//    @Scheduled(cron = "0 0 7 * * ?")
//    public void pushWarningCountMessageMorning() {
//
//        if("test".equals(activeModel)){
//            return;
//        }
//
////        Date date = new Date();
////        String timeStr = DateUtils.formatDate(date, "yyyy-MM-dd");
////
////        String startTime = DateUtils.calDate(timeStr, Calendar.DAY_OF_YEAR, -1, "yyyy-MM-dd") + " 06:00:00";
////        String endTime = timeStr + " 06:00:00";
////        sendMessageToLevel2(startTime, endTime);
////        sendMessageToLevel1(startTime, endTime);
////        sendMessageToLevel0(startTime, endTime);
////        sendMessageToLevel3(startTime, endTime);
//
//        sendMessageToLevel4();
//
//    }
//
//
//
//    /**
//     * 推送报警统计消息（规则2）
//     */
//    @Scheduled(cron = "0 30 17 * * ?")
//    public void pushWarningCountMessageAfternoon() {
//
//        if("test".equals(activeModel)){
//            return;
//        }
//
////        Date date = new Date();
////        String timeStr = DateUtils.formatDate(date, "yyyy-MM-dd");
////
////        String startTime = timeStr + " 00:00:00";
////        String endTime = timeStr + " 17:00:00";
////
////        sendMessageToLevel2(startTime, endTime);
////        sendMessageToLevel1(startTime, endTime);
////        sendMessageToLevel0(startTime, endTime);
////        sendMessageToLevel3(startTime, endTime);
//
//        sendMessageToLevel4();
//    }
//
//    //车间
//    public void sendMessageToLevel2(String startTime, String endTime) {
//        List<MessagePushMap> messagePushMapList_2 = messagePushMapDao.findMessagePushMapsBySummaryType(2);
//        List<String> roleIDs = new ArrayList<>();
//
//        DoneRateVo pRateVo;
//
//        for (MessagePushMap messagePushMap : messagePushMapList_2) {
//
//            List<String> userNoList = Arrays.stream(messagePushMap.getUserNos().split(",")).distinct().collect(Collectors.toList());
////            userNoList = Collections.singletonList("340502199406170210");
//
//            roleIDs = Arrays.stream(messagePushMap.getCheckRoleIDs().split(",")).distinct().collect(Collectors.toList());
//
//            JSONObject params = new JSONObject();
//            params.put("roleIDs", roleIDs);
//            params.put("pageIndex", 1);
//            params.put("pageSize", Integer.MAX_VALUE);
//            params.put("startTime", startTime);
//            params.put("endTime", endTime);
//
//            List<DoneRateVo> doneRateVoList = warningInfoService.getDoneRateByRoleIDs(params);
//
//            if(ToolsUtil.isEmpty(doneRateVoList)){
//                continue;
//            }
//
//            pRateVo = new DoneRateVo();
//            pRateVo.setRoleName(messagePushMap.getFactoryName());
//
//            int pTotal = 0;
//            int pDone = 0;
//            int pUndone = 0;
//
//            StringBuilder sb = new StringBuilder();
//            sb.append("统计时间：" + startTime + "~" + endTime);
//            sb.append("\n");
//            for (DoneRateVo doneRateVo : doneRateVoList) {
//                sb.append("岗位名:" + doneRateVo.getRoleName());
//                sb.append("\n");
//                sb.append("今日总报警数:" + doneRateVo.getTotal());
//                sb.append("\n");
//                sb.append("今日已处理报警数:" + doneRateVo.getDone());
//                sb.append("\n");
//                sb.append("今日未处理报警数:" + doneRateVo.getUnDone());
//                sb.append("\n");
//            }
//            pRateVo.setTotal(pTotal);
//            pRateVo.setDone(pDone);
//            pRateVo.setUnDone(pUndone);
//            weComMessagePushService.sendToUsersByUserNosByTextMessage(userNoList, sb.toString());
//        }
//    }
//
//    //厂
//    public void sendMessageToLevel1(String startTime, String endTime) {
//
//        List<MessagePushMap> messagePushMapList_1 = messagePushMapDao.findMessagePushMapsBySummaryType(1);
//        List<String> roleIDs = new ArrayList<>();
//
//        for (MessagePushMap messagePushMap_1 : messagePushMapList_1) {
//            List<MessagePushMap> messagePushMapList_2 = messagePushMapDao.findMessagePushMapsByParentID(messagePushMap_1.getId());
//
//            List<String> userNoList = Arrays.stream(messagePushMap_1.getUserNos().split(",")).distinct().collect(Collectors.toList());
//
////            userNoList = Collections.singletonList("340502199406170210");
//
//            List<DoneRateVo> pDoneRateVoList = new ArrayList<>();
//            DoneRateVo pRateVo;
//
//            for (MessagePushMap messagePushMap_2 : messagePushMapList_2) {
//                roleIDs = Arrays.stream(messagePushMap_2.getCheckRoleIDs().split(",")).distinct().collect(Collectors.toList());
//
//                JSONObject params = new JSONObject();
//                params.put("roleIDs", roleIDs);
//                params.put("pageIndex", 1);
//                params.put("pageSize", Integer.MAX_VALUE);
//                params.put("startTime", startTime);
//                params.put("endTime", endTime);
//
//                List<DoneRateVo> doneRateVoList = warningInfoService.getDoneRateByRoleIDs(params);
//
//                if(ToolsUtil.isEmpty(doneRateVoList)){
//                    continue;
//                }
//
//                pRateVo = new DoneRateVo();
//                pRateVo.setRoleName(messagePushMap_2.getFactoryName());
//
//                int pTotal = 0;
//                int pDone = 0;
//                int pUndone = 0;
//
//                StringBuilder sb = new StringBuilder();
//                for (DoneRateVo doneRateVo : doneRateVoList) {
//                    pTotal += doneRateVo.getTotal();
//                    pDone += doneRateVo.getDone();
//                    pUndone += doneRateVo.getUnDone();
//                }
//                pRateVo.setTotal(pTotal);
//                pRateVo.setDone(pDone);
//                pRateVo.setUnDone(pUndone);
//                pDoneRateVoList.add(pRateVo);
//
//            }
//
//            StringBuilder sb = new StringBuilder();
//            sb.append("统计时间：" + startTime + "~" + endTime);
//            sb.append("\n");
//            for (DoneRateVo doneRateVo : pDoneRateVoList) {
//                sb.append("岗位名:" + doneRateVo.getRoleName());
//                sb.append("\n");
//                sb.append("今日总报警数:" + doneRateVo.getTotal());
//                sb.append("\n");
//                sb.append("今日已处理报警数:" + doneRateVo.getDone());
//                sb.append("\n");
//                sb.append("今日未处理报警数:" + doneRateVo.getUnDone());
//                sb.append("\n");
//            }
//
//            if("第一炼钢厂".equals(messagePushMap_1.getFactoryName())){
//                roleIDs = new ArrayList<>();
//                roleIDs = Arrays.stream(messagePushMap_1.getCheckRoleIDs().split(",")).distinct().collect(Collectors.toList());
//                JSONObject params = new JSONObject();
//                params.put("roleIDs", roleIDs);
//                params.put("pageIndex", 1);
//                params.put("pageSize", Integer.MAX_VALUE);
//                params.put("startTime", startTime);
//                params.put("endTime", endTime);
//                List<DoneRateVo> doneRateVoList = warningInfoService.getUndoneListByRoleIDsWithTime(params);
//                if(!ToolsUtil.isEmpty(doneRateVoList)){
//                    sb.append("其中:" + "\n");
//                    for(DoneRateVo doneRateVo1 : doneRateVoList){
//                        sb.append(doneRateVo1.getModuleName() + ":" + "\n");
//                        sb.append("总计:" + doneRateVo1.getTotal() + "条" + "\n");
//                        sb.append("已处理:" + doneRateVo1.getDone() + "条" + "\n");
//                        sb.append("未处理:" + doneRateVo1.getUnDone() + "条" + "\n");
//                        sb.append("误报:" + doneRateVo1.getIsFalse() + "条" + "\n");
//                    }
//                }
//            }
//            weComMessagePushService.sendToUsersByUserNosByTextMessage(userNoList, sb.toString());
//        }
//
//    }
//
//    //事业部
//    public void sendMessageToLevel0(String startTime, String endTime) {
//        List<MessagePushMap> messagePushMapList_0 = messagePushMapDao.findMessagePushMapsBySummaryType(0);
//
//        List<String> roleIDs = new ArrayList<>();
//
//        for (MessagePushMap messagePushMap_0 : messagePushMapList_0) {
//            List<MessagePushMap> messagePushMapList_1 = messagePushMapDao.findMessagePushMapsByParentID(messagePushMap_0.getId());
//
//            List<String> userNoList = Arrays.stream(messagePushMap_0.getUserNos().split(",")).distinct().collect(Collectors.toList());
////            userNoList = Collections.singletonList("340502199406170210");
//
//            List<DoneRateVo> pDoneRateVoList = new ArrayList<>();
//            DoneRateVo pRateVo;
//
//            for (MessagePushMap messagePushMap_1 : messagePushMapList_1) {
//
//                roleIDs = Arrays.stream(messagePushMap_1.getCheckRoleIDs().split(",")).distinct().collect(Collectors.toList());
//
//                JSONObject params = new JSONObject();
//                params.put("roleIDs", roleIDs);
//                params.put("pageIndex", 1);
//                params.put("pageSize", Integer.MAX_VALUE);
//                params.put("startTime", startTime);
//                params.put("endTime", endTime);
//
//                List<DoneRateVo> doneRateVoList = warningInfoService.getDoneRateByRoleIDs(params);
//
//                if(ToolsUtil.isEmpty(doneRateVoList)){
//                    continue;
//                }
//
//                pRateVo = new DoneRateVo();
//                pRateVo.setRoleName(messagePushMap_1.getFactoryName());
//
//                int pTotal = 0;
//                int pDone = 0;
//                int pUndone = 0;
//
//                StringBuilder sb = new StringBuilder();
//                for (DoneRateVo doneRateVo : doneRateVoList) {
//                    pTotal += doneRateVo.getTotal();
//                    pDone += doneRateVo.getDone();
//                    pUndone += doneRateVo.getUnDone();
//                }
//                pRateVo.setTotal(pTotal);
//                pRateVo.setDone(pDone);
//                pRateVo.setUnDone(pUndone);
//                pDoneRateVoList.add(pRateVo);
//
//            }
//            StringBuilder sb = new StringBuilder();
//            sb.append("统计时间：" + startTime + "~" + endTime);
//            sb.append("\n");
//            for (DoneRateVo doneRateVo : pDoneRateVoList) {
//                sb.append("岗位名:" + doneRateVo.getRoleName());
//                sb.append("\n");
//                sb.append("今日总报警数:" + doneRateVo.getTotal());
//                sb.append("\n");
//                sb.append("今日已处理报警数:" + doneRateVo.getDone());
//                sb.append("\n");
//                sb.append("今日未处理报警数:" + doneRateVo.getUnDone());
//                sb.append("\n");
//            }
////            weComMessagePushService.sendToUsersByUserNosByTextMessage(userNoList, sb.toString());
//            weComMessagePushService.sendToUsersByUserNosByTextCard(userNoList);
//        }
//    }
//
//    //按模块统计
//    public void sendMessageToLevel3(String startTime, String endTime) {
//        List<MessagePushMap> messagePushMapList_3 = messagePushMapDao.findMessagePushMapsBySummaryType(3);
//
//        for (MessagePushMap messagePushMap_3 : messagePushMapList_3) {
//
//            List<String> userNoList = Arrays.stream(messagePushMap_3.getUserNos().split(",")).distinct().collect(Collectors.toList());
//            List<String> moduleCodes = Arrays.asList(messagePushMap_3.getModuleCodes().split(","));
//
//            JSONObject params = new JSONObject();
//            params.put("moduleCodes", moduleCodes);
//            params.put("pageIndex", 1);
//            params.put("pageSize", Integer.MAX_VALUE);
//            params.put("startTime", startTime);
//            params.put("endTime", endTime);
//
//            List<DoneRateVo> doneRateVoList = warningInfoService.getDoneRateByModuleCodes(params);
//
//            if(ToolsUtil.isEmpty(doneRateVoList)){
//                continue;
//            }
//
//            StringBuilder sb = new StringBuilder();
//
//            sb.append("统计时间：" + startTime + "~" + endTime);
//            for (DoneRateVo doneRateVo : doneRateVoList) {
//                sb.append("模块名:" + doneRateVo.getRoleName());
//                sb.append("\n");
//                sb.append("今日总报警数:" + doneRateVo.getTotal());
//                sb.append("\n");
//                sb.append("今日已处理报警数:" + doneRateVo.getDone());
//                sb.append("\n");
//                sb.append("今日未处理报警数:" + doneRateVo.getUnDone());
//                sb.append("\n");
//            }
//            weComMessagePushService.sendToUsersByUserNosByTextMessage(userNoList, sb.toString());
//        }
//    }
//
//
//
//    public void sendMessageToLevel4() {
//        List<MessagePushMap> messagePushMapList_4 = messagePushMapDao.findMessagePushMapsBySummaryType(4);
//
//        List<User> userList = new ArrayList<>();
//
//        List<String> userNos = new ArrayList<>();
//
//        for (MessagePushMap messagePushMap_4 : messagePushMapList_4) {
//            List<String> orgCodes = Arrays.asList(messagePushMap_4.getOrgCodes().split(","));
//
//            for(String orgCode : orgCodes){
//                if(!"X50000000".equals(orgCode)){
//                    orgService.getAllChildOrgList(orgCode);
//                    userList.addAll(userDao.findUsersByOrgCodes(orgService.getAllChildOrgList(orgCode).stream().collect(Collectors.toList())));
//                }else{
//                    userList.addAll(userDao.findUsersByOrgCodes(Arrays.asList("X50000000").stream().collect(Collectors.toList())));
//                }
//            }
//        }
//        userNos = userList.stream().map(User::getUserNo).distinct().collect(Collectors.toList());
//        System.out.println(userNos.size());
//        //            weComMessagePushService.sendToUsersByUserNosByTextCard(userNos);
//
//    }
//
//}
