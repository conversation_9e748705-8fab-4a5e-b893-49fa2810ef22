package iet.ustb.sf.vo.domain;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "DM_BASIC_DATA_MODEL")
@Data
public class BasicDataModel {

    @Id
    @GeneratedValue
    private Integer id;

    private String plant;
    private Integer groupId;
    private String groupName;
    private String nodePath;

    @Column(length = 100, nullable = false)
    private String name;

    private Date toc;
    private Date tom;

    @Column(length = 60)
    private String mop;
    private String description;

    //    private Integer procedure; procedure 是mysql的关键字
    private Integer prodFactors;
    private Integer varType;
    private String dataType;
    private String addInfo;
    private Integer enable;
    private String propType;
    private String propId;


    private Integer dataAcquisitionMethod;
    @Column(length = 500)
    private String remark;
    private Integer version;

    private String execEvent;
    private Integer delay;
    private String outPutEvent;

    @Column(length = 4000)
    private String ruleEngineFormula;
    @Column(length = 4000)
    private String ruleEngineVariables;

    @Column
    private String aliasName;
    @Column(length = 4000)
    private String dataInterface;

}

