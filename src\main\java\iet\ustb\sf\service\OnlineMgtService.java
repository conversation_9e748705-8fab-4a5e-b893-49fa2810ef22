package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.OnlineMgt;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 上线进度管理
 *
 * <AUTHOR>
 * @create 2023-02-13
 */
public interface OnlineMgtService {

    /**
     * 查找所有内容
     *
     * @return {@link List }<{@link OnlineMgt }>
     * <AUTHOR>
     * @create 2023-02-13
     */
    List<OnlineMgt> findAll();

    /**
     * 按多条件查找分页
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link OnlineMgt }>
     * <AUTHOR>
     * @create 2023-02-13
     */
    Page<OnlineMgt> findPageByMultiCondition(JSONObject jsonObject);

    /**
     * 保存
     *
     * @param progressMgt 进度管理
     * @return {@link OnlineMgt }
     * <AUTHOR>
     * @create 2023-02-13
     */
    OnlineMgt save(OnlineMgt progressMgt);

    /**
     * 删除
     *
     * @param jsonObject json对象
     * <AUTHOR>
     * @create 2023-02-13
     */
    void delete(JSONObject jsonObject) throws Exception;
}
