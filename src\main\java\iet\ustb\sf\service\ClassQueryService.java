package iet.ustb.sf.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.utils.DateUtils;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.vo.ClassRulesVo;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ClassQueryService {

    public final static String DATA_ = "[{\"classes\":\"0\",\"endTime\":1644796800000,\"id\":1,\"startTime\":1644768000000,\"team\":\"1\"},{\"classes\":\"1\",\"endTime\":1644825600000,\"id\":2,\"startTime\":1644796800000,\"team\":\"2\"},{\"classes\":\"2\",\"endTime\":1644854400000,\"id\":3,\"startTime\":1644825600000,\"team\":\"3\"},{\"classes\":\"0\",\"endTime\":1644883200000,\"id\":4,\"startTime\":1644854400000,\"team\":\"1\"},{\"classes\":\"1\",\"endTime\":1644912000000,\"id\":5,\"startTime\":1644883200000,\"team\":\"2\"},{\"classes\":\"2\",\"endTime\":1644940800000,\"id\":6,\"startTime\":1644912000000,\"team\":\"3\"},{\"classes\":\"0\",\"endTime\":1644969600000,\"id\":7,\"startTime\":1644940800000,\"team\":\"0\"},{\"classes\":\"1\",\"endTime\":1644998400000,\"id\":8,\"startTime\":1644969600000,\"team\":\"1\"},{\"classes\":\"2\",\"endTime\":1645027200000,\"id\":9,\"startTime\":1644998400000,\"team\":\"2\"},{\"classes\":\"0\",\"endTime\":1645056000000,\"id\":10,\"startTime\":1645027200000,\"team\":\"0\"},{\"classes\":\"1\",\"endTime\":1645084800000,\"id\":11,\"startTime\":1645056000000,\"team\":\"1\"},{\"classes\":\"2\",\"endTime\":1645113600000,\"id\":12,\"startTime\":1645084800000,\"team\":\"2\"},{\"classes\":\"0\",\"endTime\":1645142400000,\"id\":13,\"startTime\":1645113600000,\"team\":\"3\"},{\"classes\":\"1\",\"endTime\":1645171200000,\"id\":14,\"startTime\":1645142400000,\"team\":\"0\"},{\"classes\":\"2\",\"endTime\":1645200000000,\"id\":15,\"startTime\":1645171200000,\"team\":\"1\"},{\"classes\":\"0\",\"endTime\":1645228800000,\"id\":16,\"startTime\":1645200000000,\"team\":\"3\"},{\"classes\":\"1\",\"endTime\":1645257600000,\"id\":17,\"startTime\":1645228800000,\"team\":\"0\"},{\"classes\":\"2\",\"endTime\":1645286400000,\"id\":18,\"startTime\":1645257600000,\"team\":\"1\"},{\"classes\":\"0\",\"endTime\":1645315200000,\"id\":19,\"startTime\":1645286400000,\"team\":\"2\"},{\"classes\":\"1\",\"endTime\":1645344000000,\"id\":20,\"startTime\":1645315200000,\"team\":\"3\"},{\"classes\":\"2\",\"endTime\":1645372800000,\"id\":21,\"startTime\":1645344000000,\"team\":\"0\"},{\"classes\":\"0\",\"endTime\":1645401600000,\"id\":22,\"startTime\":1645372800000,\"team\":\"2\"},{\"classes\":\"1\",\"endTime\":1645430400000,\"id\":23,\"startTime\":1645401600000,\"team\":\"3\"},{\"classes\":\"2\",\"endTime\":1645459200000,\"id\":24,\"startTime\":1645430400000,\"team\":\"0\"}]";

    public Map<String, String> queryClasses(Date createTime) {
        Map<String, String> result = new HashMap<>();
        String classes = "";
        String team = "";
        List<ClassRulesVo> list = JSON.parseArray(DATA_ , ClassRulesVo.class);
        System.out.println(JSONObject.toJSONString(list));
        //规则开始时间
        Date dateStart = list.get(0).getStartTime();
        //规则结束时间
        Date dateEnd = list.get(list.size() - 1).getEndTime();
        //一个周期多少小时
        int hours = DateUtils.getDifferHour(dateStart, dateEnd);
        Date transformDate = createTime;
        if (createTime.getTime() < dateStart.getTime()) {
            int i = DateUtils.getDifferHour(createTime, dateEnd) / hours;
            transformDate = DateUtils.addDateHours(createTime, i * hours);
            if (transformDate.getTime() == dateEnd.getTime()) {
                transformDate = DateUtils.addDateHours(transformDate, -1 * hours);
            }
        }
        if (createTime.getTime() >= dateEnd.getTime()) {
            int i = DateUtils.getDifferHour(dateStart, createTime) / hours;
            transformDate = DateUtils.addDateHours(createTime, -1 * i * hours);
        }
        for (ClassRulesVo classRulesEntity : list) {
            if (transformDate.getTime() >= classRulesEntity.getStartTime().getTime() &&
                    transformDate.getTime() < classRulesEntity.getEndTime().getTime()) {
                classes = classRulesEntity.getClasses();
                team = classRulesEntity.getTeam();
            }
        }
        result.put("classes", classes);
        result.put("team", team);
        return result;
    }
}
