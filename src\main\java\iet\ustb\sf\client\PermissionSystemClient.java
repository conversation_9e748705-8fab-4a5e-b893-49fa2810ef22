package iet.ustb.sf.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.utils.RedisUtil;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.vo.CustomExceptionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

/**
 * 权限系统客户端
 * 负责调用权限系统接口获取用户信息，实现双重匹配策略和Redis缓存机制
 *
 * <AUTHOR> <PERSON>改造项目
 * @date 2025-07-02
 */
@Component
@Slf4j
public class PermissionSystemClient {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${permission.system.base.url:http://localhost:8285}")
    private String permissionSystemBaseUrl;

    @Value("${permission.system.timeout:5000}")
    private int timeoutMs;

    @Value("${permission.system.cache.expire-minutes:30}")
    private int cacheExpireMinutes;

    /**
     * Redis缓存key前缀
     */
    private static final String CACHE_KEY_PREFIX = "permission_user_";

    /**
     * 默认userid，用于降级处理
     */
    private static final Long DEFAULT_USER_ID = 1938155631131365376L;

    /**
     * 根据用户信息获取权限系统中的真实userid
     * 实现双重匹配策略：userName匹配userName，userNo匹配account
     *
     * @param userId 用户ID（对应userNo）
     * @param userName 用户名（对应userName）
     * @return 权限系统中的真实userid，失败时返回默认值
     */
    public Long getUserIdByUserInfo(String userId, String userName) {
        log.debug("开始获取权限系统用户ID，userId: {}, userName: {}", userId, userName);

        try {
            // 1. 检查Redis缓存
            String cacheKey = generateCacheKey(userId, userName);
            Long cachedUserId = getCachedUserId(cacheKey);
            if (cachedUserId != null) {
                log.debug("从缓存中获取到用户ID: {}", cachedUserId);
                return cachedUserId;
            }

            // 2. 调用权限系统接口
            Long realUserId = fetchUserIdFromPermissionSystem(userId, userName);

            // 3. 缓存结果
            if (realUserId != null) {
                cacheUserId(cacheKey, realUserId);
                log.info("成功获取权限系统用户ID: {}, userId: {}, userName: {}", realUserId, userId, userName);
                return realUserId;
            } else {
                log.warn("权限系统中未找到匹配用户，使用默认ID，userId: {}, userName: {}", userId, userName);
                return DEFAULT_USER_ID;
            }

        } catch (Exception e) {
            log.error("获取权限系统用户ID失败，使用默认ID进行降级，userId: {}, userName: {}, error: {}",
                     userId, userName, e.getMessage(), e);
            return DEFAULT_USER_ID;
        }
    }

    /**
     * 从权限系统获取用户ID
     */
    private Long fetchUserIdFromPermissionSystem(String userId, String userName) {
        try {
            // 构建请求URL
            String url = permissionSystemBaseUrl + "/users/getUserList";

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体（空JSON对象）
            JSONObject requestBody = new JSONObject();
            HttpEntity<String> request = new HttpEntity<>(requestBody.toJSONString(), headers);

            log.debug("调用权限系统接口: {}", url);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return parseUserIdFromResponse(response.getBody(), userId, userName);
            } else {
                log.warn("权限系统接口调用失败，状态码: {}", response.getStatusCode());
                return null;
            }

        } catch (RestClientException e) {
            log.error("权限系统接口调用异常: {}", e.getMessage(), e);
            throw new CustomExceptionVo("500", "权限系统接口调用失败: " + e.getMessage());
        }
    }

    /**
     * 解析权限系统响应，实现双重匹配策略
     */
    private Long parseUserIdFromResponse(String responseBody, String userId, String userName) {
        try {
            JSONObject response = JSON.parseObject(responseBody);
            JSONArray dataArray = response.getJSONArray("data");

            if (dataArray == null || dataArray.isEmpty()) {
                log.warn("权限系统返回空数据");
                return null;
            }

            // 双重匹配策略：优先userName匹配，其次userNo匹配account
            Long matchedUserId = null;

            // 第一轮：userName匹配userName字段
            if (!ToolsUtil.isEmpty(userName)) {
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject user = dataArray.getJSONObject(i);
                    String userNameField = user.getString("userName");
                    if (userName.equals(userNameField)) {
                        matchedUserId = user.getLong("userId");
                        log.debug("通过userName匹配到用户: userName={}, userId={}", userName, matchedUserId);
                        break;
                    }
                }
            }

            // 第二轮：如果userName匹配失败，使用userNo匹配account字段
            if (matchedUserId == null && !ToolsUtil.isEmpty(userId)) {
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject user = dataArray.getJSONObject(i);
                    String accountField = user.getString("account");
                    if (userId.equals(accountField)) {
                        matchedUserId = user.getLong("userId");
                        log.debug("通过userNo匹配account到用户: userNo={}, account={}, userId={}",
                                 userId, accountField, matchedUserId);
                        break;
                    }
                }
            }

            if (matchedUserId == null) {
                log.warn("双重匹配策略均未找到匹配用户，userId: {}, userName: {}", userId, userName);
            }

            return matchedUserId;

        } catch (Exception e) {
            log.error("解析权限系统响应失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成缓存key
     */
    private String generateCacheKey(String userId, String userName) {
        return CACHE_KEY_PREFIX + userId + "_" + userName;
    }

    /**
     * 从缓存获取用户ID
     */
    private Long getCachedUserId(String cacheKey) {
        try {
            if (redisUtil.hasKey(cacheKey)) {
                Object cachedValue = redisUtil.get(cacheKey);
                if (cachedValue != null) {
                    // 从String转换回Long
                    return Long.valueOf(cachedValue.toString());
                }
            }
        } catch (Exception e) {
            log.warn("从缓存获取用户ID失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 缓存用户ID
     */
    private void cacheUserId(String cacheKey, Long userId) {
        try {
            // 将Long转换为String存储，避免Redis序列化问题
            redisUtil.set(cacheKey, userId.toString(), cacheExpireMinutes * 60);
            log.debug("用户ID已缓存: key={}, userId={}, expireMinutes={}", cacheKey, userId, cacheExpireMinutes);
        } catch (Exception e) {
            log.warn("缓存用户ID失败: {}", e.getMessage());
        }
    }
}
