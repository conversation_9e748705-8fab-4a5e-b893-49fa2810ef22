package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.AjaxJson;
import iet.ustb.sf.webSocket.WebSocket;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/webSocket")
@Api(value = "webSocket", tags = "webSocket")
public class WebSocketController {

    @Autowired
    private WebSocket webSocket;


    @PostMapping("/setMessageToUser")
    @ApiOperation(value = "发送消息到对应用户", notes = "发送消息到对应用户,{\n" +
            "    \"serviceName\":\"res\",\n" +
            "    \"userNo\":\"021179\",\n" +
            "    \"message\":\"message-1\"\n" +
            "}", produces = "application/json")
    public void setMessageToUser(@RequestBody JSONObject jsonObject) {
        try {
            webSocket.setMessageToUser(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @PostMapping("/setMessageToRole")
    @ApiOperation(value = "发送消息到对应角色下的用户", notes = "发送消息到对应角色下的用户,{\n" +
            "    \"serviceName\":\"res\",\n" +
            "    \"roleID\":\"1d0e5254-cd89-4c84-8dd2-47c365ae27cc\",\n" +
            "    \"message\":\"message-1\"\n" +
            "}", produces = "application/json")
    public void setMessageToRole(@RequestBody JSONObject jsonObject) {
        try {
            webSocket.setMessageToRole(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/setMessageToRoles")
    @ApiOperation(value = "发送消息到对应角色下的用户(多个角色)", notes = "发送消息到对应角色下的用户(多个角色),{\n" +
            "    \"serviceName\":\"res\",\n" +
            "    \"message\": \"123456\",\n" +
            "    \"roleIDs\": [\n" +
            "        \"181b07ea-3e39-42b6-8c7a-8b1613bdff4f\",\n" +
            "        \"1d0e5254-cd89-4c84-8dd2-47c365ae27cc\"\n" +
            "    ]\n" +
            "}", produces = "application/json")
    public AjaxJson setMessageToRoles(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setSuccess(true);
            ajaxJson.setData(webSocket.setMessageToRoles(jsonObject));
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }


    @PostMapping("/setMessageToOrg")
    @ApiOperation(value = "发送消息到对应组织下的用户", notes = "发送消息到对应组织下的用户,{\n" +
            "    \"serviceName\":\"res\",\n" +
            "    \"orgCode\":\"X15090302\",\n" +
            "    \"message\":\"message-1\"\n" +
            "}", produces = "application/json")
    public void setMessageToOrg(@RequestBody JSONObject jsonObject) {
        try {
            webSocket.setMessageToOrg(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/setMessageToSelectedUsers")
    @ApiOperation(value = "发送消息到自定义多选的用户", notes = "发送消息到自定义多选的用户,{\n" +
            "    \"serviceName\":\"res\",\n" +
            "    \"userNos\": [\n" +
            "        \"000003\",\n" +
            "        \"000002\"\n" +
            "    ],\n" +
            "    \"message\": \"message-1\"\n" +
            "}", produces = "application/json")
    public void setMessageToSelectedUsers(@RequestBody JSONObject jsonObject) {
        try {
            webSocket.setMessageToSelectedUsers(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/setMessageToSameOrgAndRoleUser")
    @ApiOperation(value = "发送消息到该组织与角色下所有用户", notes = "发送消息到自定义多选的用户,{\n" +
            "    \"serviceName\":\"res\",\n" +
            "    \"userNo\": \"007132\",\n" +
            "    \"message\": \"message-1\"\n" +
            "}", produces = "application/json")
    public void setMessageToSameOrgAndRoleUser(@RequestBody JSONObject jsonObject) {
        try {
            webSocket.setMessageToSameOrgAndRoleUser(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @PostMapping("/finishAlert")
    @ApiOperation(value = "报警完成通知", notes = "报警完成通知,{\n" +
            "    \"msgID\": \"\",\n" +
            "    \"userNos\": \"\",\n" +
            "    \"warningInfo\": {\n" +
            "        \"id\": \"1234567890\",\n" +
            "        \"areaID\":\"\",\n" +
            "        \"areaName\":\"热处理\",\n" +
            "        \"pointID\":\"34005\",\n" +
            "        \"pointName\":\"加热炉\",\n" +
            "        \"deviceID\":\"3400501\",\n" +
            "        \"deviceName\":\"加热炉\",\n" +
            "        \"alertContent\": \"2023-11-03 00:00:00-2023-11-04 00:00:00,加热炉温度超标\",\n" +
            "        \"alertAdvice\" :\"温度超标\",\n" +
            "        \"diagnosticMessage\":\"温度超标\",\n" +
            "        \"humanAdvice\":\"关注温度变化\",\n" +
            "        \"dealTime\":\"2023-11-22 16:00:00\",\n" +
            "        \"alertAnalysis\":\"温度报警\",\n" +
            "        \"alertValue\":330,\n" +
            "        \"warningType\":3,\n" +
            "        \"alertLevel\": 3,\n" +
            "        \"alertTypeName\": \"超标报警\",\n" +
            "        \"isConfirm\": 1,\n" +
            "        \"moduleCode\": \"ems\",\n" +
            "        \"moduleName\": \"智慧能源\",\n" +
            "        \"remark\": \"\",\n" +
            "        \"status\": 1\n" +
            "    }\n" +
            "}", produces = "application/json")
    public void finishAlert(@RequestBody JSONObject jsonObject) {
        try {
            webSocket.finishAlert(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @PostMapping("/finishAlertTmp")
    @ApiOperation(value = "报警完成通知", notes = "报警完成通知,{\n" +
            "    \"msgID\": \"\",\n" +
            "    \"userNos\": \"\",\n" +
            "    \"warningInfo\": {\n" +
            "        \"id\": \"1234567890\",\n" +
            "        \"areaID\":\"\",\n" +
            "        \"areaName\":\"热处理\",\n" +
            "        \"pointID\":\"34005\",\n" +
            "        \"pointName\":\"加热炉\",\n" +
            "        \"deviceID\":\"3400501\",\n" +
            "        \"deviceName\":\"加热炉\",\n" +
            "        \"alertContent\": \"2023-11-03 00:00:00-2023-11-04 00:00:00,加热炉温度超标\",\n" +
            "        \"alertAdvice\" :\"温度超标\",\n" +
            "        \"diagnosticMessage\":\"温度超标\",\n" +
            "        \"humanAdvice\":\"关注温度变化\",\n" +
            "        \"dealTime\":\"2023-11-22 16:00:00\",\n" +
            "        \"alertAnalysis\":\"温度报警\",\n" +
            "        \"alertValue\":330,\n" +
            "        \"warningType\":3,\n" +
            "        \"alertLevel\": 3,\n" +
            "        \"alertTypeName\": \"超标报警\",\n" +
            "        \"isConfirm\": 1,\n" +
            "        \"moduleCode\": \"ems\",\n" +
            "        \"moduleName\": \"智慧能源\",\n" +
            "        \"remark\": \"\",\n" +
            "        \"status\": 1\n" +
            "    }\n" +
            "}", produces = "application/json")
    public void finishAlertTmp(@RequestBody JSONObject jsonObject) {
        try {
            webSocket.finishAlertTmp(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


//    @PostMapping("/saveOfflineMessage")
//    @ApiOperation(value = "保存离线消息", notes = "保存离线消息,{}", produces = "application/json")
//    public void saveOfflineMessage(@RequestBody JSONObject jsonObject) {
//        try {
//            webSocket.saveOfflineMessage(jsonObject);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
}