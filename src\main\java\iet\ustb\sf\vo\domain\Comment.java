package iet.ustb.sf.vo.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 问题反馈评论
 * <AUTHOR>
 * @create 2022-11-03
 */
@Data
@Entity
@Table(name = "COMMENT")
@ApiModel(value = "问题反馈评论")
public class Comment extends BaseEntity {

    /**
     * 户编号
     */
    @ApiModelProperty(value = "⽤户编号")
    private String userNo;

    /**
     * 评论内容
     */
    @ApiModelProperty(value = "评论内容")
    private String content;

    /**
     * 问题反馈业务id
     */
    @ApiModelProperty(value = "问题反馈业务id ")
    private String feedBackId;

    /**
     * 回复评论id
     */
    @ApiModelProperty(value = "回复评论id")
    private String parentId;

    /**
     * 子集
     */
    @Transient
    private List<Comment> children;
}
