package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.WarningInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: Dr.Monster
 * @Title: WaringInfoDao
 * @Date: 23/10/31 11:4149
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */
public interface WarningInfoDao extends JpaSpecificationExecutor<WarningInfo>, JpaRepository<WarningInfo, Serializable> {

    @Query(value = "select * from warning_info where status = 0 and isConfirm = 0 and msgID = ?1" , nativeQuery = true)
    List<WarningInfo> findWarningInfosByMsgID(String msgID);

    @Query(value = "select a.ruleName , a.total\n" +
            "from (select wr.ruleName, count(wi.id) as total\n" +
            "      from warning_info wi\n" +
            "               join warning_rule wr\n" +
            "                    on wi.warningRuleID = wr.id\n" +
            "      group by wr.ruleName) a\n" , nativeQuery = true)
    List<Map<String , Object>> findRuleAlertCount();

    @Query(value = "select * from warning_info where warningRuleID = ?1 order by createDateTime desc" , nativeQuery = true)
    List<WarningInfo> findListByRuleID(String ruleID);

    @Query(value = "select * from warning_info where roleID = ?1 and createDateTime between ?2 and ?3 and warningType != 5" , nativeQuery = true)
    List<WarningInfo> findAlertListByRoleIDAndTime(String roleID , Date startTime , Date endTime);

    @Query(value = "select * from warning_info where moduleCode = ?1 and createDateTime between ?2 and ?3 and warningType != 5" , nativeQuery = true)
    List<WarningInfo> findAlertListByModuleCodeAndTime(String roleID , Date startTime , Date endTime);


    @Query(value = "select * from warning_info where roleID = ?1 and createDateTime between ?2 and ?3" , nativeQuery = true)
    List<WarningInfo> findAlertListByRoleIDAndTimeNew(String roleID , Date startTime , Date endTime);


    @Query(value = "select count(id)                                    as total,\n" +
            "       sum(case when status = 2 then 1 else 0 end)  as done,\n" +
            "       sum(case when status != 2 then 1 else 0 end)  as undone,\n" +
            "       sum(case when alertLevel = 1 then 1 else 0 end)  as firstLevel,\n" +
            "       sum(case when alertLevel = 2 then 1 else 0 end)  as secondLevel,\n" +
            "       sum(case when alertLevel = 3 then 1 else 0 end) as thirdLevel,\n" +
            "       sum(case when alertAdvice is null and isFalse = 0 and status = 2 then 1 else 0 end ) as adviceAtypia,\n" +
            "       sum(case when alertAnalysis is null and isFalse = 0 and status = 2 then 1 else 0 end ) as analysisAtypia," +
            "       sum(case\n" +
            "               when (status = 2 and warningType != 5 and  cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) < 2) then 1\n" +
            "               else 0 end)                                                                   as twoHourDone,\n" +
            "       sum(case\n" +
            "               when (status = 2 and warningType != 5 and  cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) >= 2 and\n" +
            "                     cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) < 4)\n" +
            "                   then 1\n" +
            "               else 0 end)                                                                   as fourHourDone,\n" +
            "       sum(case\n" +
            "               when (status = 2 and warningType != 5 and  cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) >= 4 and\n" +
            "                     cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) < 8)\n" +
            "                   then 1\n" +
            "               else 0 end)                                                                   as eightHourDone,\n" +
            "       sum(case\n" +
            "               when (status = 2 and warningType != 5 and cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) >= 8) then 1\n" +
            "               else 0 end)                                                                   as gtEightHourDone," +
            "       sum(case when isFalse = 1 then 1 else 0 end) as isFalse\n" +
            "from warning_info\n" +
            "where roleID = ?1\n" +
            "  and createDateTime between ?2 and ?3 and warningType != 5" , nativeQuery = true)
    Map<String , Object> getUndoneRateByRoleID(String roleID , Date startTime , Date endTime);


    @Query(value = "select count(id)                                    as total,\n" +
            "       moduleName,\n" +
            "       moduleCode,\n" +
            "       sum(case when alertAdvice is not null then 1 else 0 end)                              as alertAdviceCount," +
            "       sum(case when alertLevel = 1 then 1 else 0 end)  as firstLevel,\n" +
            "       sum(case when alertLevel = 2 then 1 else 0 end)  as secondLevel,\n" +
            "       sum(case when alertLevel = 3 then 1 else 0 end) as thirdLevel,\n" +
            "       sum(case when status = 2 then 1 else 0 end)  as done,\n" +
            "       sum(case when status != 2 then 1 else 0 end)  as undone,\n" +
            "       sum(case when alertAdvice is null and isFalse = 0 and status = 2 then 1 else 0 end ) as adviceAtypia,\n" +
            "       sum(case when alertAnalysis is null and isFalse = 0 and status = 2 then 1 else 0 end ) as analysisAtypia," +
            "       sum(case\n" +
            "               when (status = 2 and warningType != 5 and cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) <2) then 1\n" +
            "               else 0 end)                                                                   as twoHourDone,\n" +
            "       sum(case\n" +
            "               when (status = 2 and warningType != 5 and cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) >= 2 and\n" +
            "                     cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) < 4)\n" +
            "                   then 1\n" +
            "               else 0 end)                                                                   as fourHourDone,\n" +
            "       sum(case\n" +
            "               when (status = 2 and warningType != 5 and cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) >= 4 and\n" +
            "                     cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) < 8)\n" +
            "                   then 1\n" +
            "               else 0 end)                                                                   as eightHourDone,\n" +
            "       sum(case\n" +
            "               when (status = 2 and warningType != 5 and cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) >= 8) then 1\n" +
            "               else 0 end)                                                                   as gtEightHourDone," +
            "       sum(case when isFalse = 1 then 1 else 0 end) as isFalse\n" +
            "from warning_info\n" +
            "where moduleCode = ?1\n" +
            "  and createDateTime between ?2 and ?3 and warningType != 5\n" +
            "group by moduleName, moduleCode" , nativeQuery = true)
    Map<String , Object> getUndoneRateByModuleCode(String moduleCode , Date startTime , Date endTime);


    @Query(value = "select moduleName,\n" +
            "       moduleCode,\n" +
            "       count(id)                                    as total," +
            "       sum(case when status = 2 then 1 else 0 end)  as done,\n" +
            "       sum(case when status != 2 then 1 else 0 end)  as undone,\n" +
            "       sum(case when isFalse = 1 then 1 else 0 end) as isFalse\n" +
            "from warning_info \n" +
            "where roleID in (?1) \n" +
            " and createDateTime between ?2 and ?3 and warningType != 5 \n" +
            "group by moduleName, moduleCode" , nativeQuery = true)
    List<Map<String , Object>> getUndoneListByRoleIDsWithTime(List<String> roleIDs , Date startTime , Date endTime);

    @Query(value = "select wi.alertAdvice, count(wi.id) as count\n" +
            "from warning_info wi\n" +
            "         join warning_event we on we.warningRuleID = wi.warningRuleID\n" +
            "where we.id = ?1\n" +
            "  and wi.status = 2\n" +
            "  and wi.isFalse = 0\n" +
            "  and warningType != 5\n" +
            "group by wi.alertAdvice\n" +
            "order by count desc " , nativeQuery = true)
    List<Map<String , Object>> findHistoryByWaringEventID(String waringEventID);


    @Query(value = "select * from warning_info where msgID = ?1" , nativeQuery = true)
    List<WarningInfo> findWariningByMsgID(String msgID);


    @Query(value = "select wi.alertAdvice, wr.ruleName, count(wi.id) as count\n" +
            "from warning_info wi\n" +
            "         join warning_rule wr\n" +
            "              on wi.warningRuleID = wr.id\n" +
            "where wi.warningRuleID = ?1\n" +
            "  and wi.status = 2\n" +
            "  and wi.isFalse = 0\n" +
            "group by wi.alertAdvice, wr.ruleName\n" +
            "order by count desc" , nativeQuery = true)
    List<Map<String , Object>> findSummaryByWarningRuleID(String warningRuleID);


    @Query(value = "select wi.alertAdvice, wr.ruleName, count(wi.id) as count\n" +
            "from warning_info wi\n" +
            "         join warning_rule wr\n" +
            "              on wi.warningRuleID = wr.id\n" +
            "where wi.warningRuleID = ?1\n" +
            "  and wi.status = 2\n" +
            "  and wi.isFalse = 0\n" +
            "  and wi.createDateTime between ?2 and ?3\n" +
            "group by wi.alertAdvice, wr.ruleName\n" +
            "order by count desc" , nativeQuery = true)
    List<Map<String , Object>> findSummaryByWarningRuleIDWithTimeRange(String warningRuleID , Date start , Date end);

    @Query(value = "select distinct moduleCode from warning_info" , nativeQuery = true)
    List<String> findModuleCodes();


    @Query(value = "select wi.roleID,\n" +
            "       dr.rolename,\n" +
            "       cast(date_format(wi.createDateTime, '%Y-%m-%d') as char)      as datetime,\n" +
            "       count(wi.id)                                   as total,\n" +
            "       sum(case when wi.alertLevel = 1 then 1 else 0 end)  as firstLevel,\n" +
            "       sum(case when wi.alertLevel = 2 then 1 else 0 end)  as secondLevel,\n" +
            "       sum(case when wi.alertLevel = 3 then 1 else 0 end) as thirdLevel,\n" +
            "       sum(case when wi.status = 2 then 1 else 0 end)  as done,\n" +
            "       sum(case when wi.status != 2 then 1 else 0 end)  as undone,\n" +
            "       sum(case when wi.isFalse = 1 then 1 else 0 end) as isFalse\n" +
            "from warning_info wi\n" +
            "         join ds_role dr\n" +
            "              on wi.roleID = dr.id\n" +
            "where wi.roleID = ?1\n" +
            "  and wi.createDateTime between ?2 and ?3\n" +
            "group by datetime" , nativeQuery = true)
    List<Map<String , Object>> findRateByRoleIDsWithTime(String roleID , Date startTime , Date endTime);


    @Query(value = "select wi.moduleName,\n" +
            "       wi.moduleCode,\n" +
            "       cast(date_format(wi.createDateTime, '%Y-%m-%d') as char)      as datetime,\n" +
            "       count(wi.id)                                   as total,\n" +
            "       sum(case when wi.alertLevel = 1 then 1 else 0 end)  as firstLevel,\n" +
            "       sum(case when wi.alertLevel = 2 then 1 else 0 end)  as secondLevel,\n" +
            "       sum(case when wi.alertLevel = 3 then 1 else 0 end) as thirdLevel,\n" +
            "       sum(case when wi.status = 2 then 1 else 0 end)  as done,\n" +
            "       sum(case when wi.status != 2 then 1 else 0 end)  as undone,\n" +
            "       sum(case when wi.isFalse = 1 then 1 else 0 end) as isFalse\n" +
            "from warning_info wi\n" +
            "where wi.moduleCode = ?1\n" +
            "  and wi.createDateTime between ?2 and ?3\n" +
            "  and warningType != 5\n" +
            "group by datetime, moduleName, moduleCode" , nativeQuery = true)
    List<Map<String , Object>> findRateByModuleCodesWithTime(String roleID , Date startTime , Date endTime);

    @Query(value = "select wi.moduleName,\n" +
            "       wi.moduleCode,\n" +
            "       cast(date_format(wi.createDateTime, '%Y-%m-%d') as char)       as datetime,\n" +
            "       count(wi.id)                                    as total,\n" +
            "       sum(case when wi.isFalse = 1 then 1 else 0 end) as isFalse\n" +
            "from warning_info wi\n" +
            "where wi.moduleCode = ?1\n" +
            "  and wi.createDateTime between ?2 and ?3\n" +
            "group by datetime, moduleName, moduleCode" , nativeQuery = true)
    List<Map<String , Object>> findIsFalseByModuleCodesWithTime(String roleID , Date startTime , Date endTime);


    @Query(value = "select * from warning_info where roleID = ?1 and status = ?2 and createDateTime between ?3 and ?4 and warningType != 5" , nativeQuery = true)
    Page<WarningInfo> findHistoryByRoleAndTime(String roleID , String status , Date startTime , Date endTime , Pageable pageable);

    @Query(value = "select * from warning_info where status = 2 and dealTime is null" , nativeQuery = true)
    List<WarningInfo> findWarningInfoByStatusAndTime();

    @Query(value = "select count(id)                                                                             as total,\n" +
            "       sum(case when status = 2 and warningType != 5 then 1 else 0 end)                                           as done,\n" +
            "       sum(case\n" +
            "               when (status = 2 and warningType != 5 and cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) <2) then 1\n" +
            "               else 0 end)                                                                   as twoHourDone,\n" +
            "       sum(case\n" +
            "               when (status = 2 and warningType != 5 and cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) >= 2 and\n" +
            "                     cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) < 4)\n" +
            "                   then 1\n" +
            "               else 0 end)                                                                   as fourHourDone,\n" +
            "       sum(case\n" +
            "               when (status = 2 and warningType != 5 and cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) >= 4 and\n" +
            "                     cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) < 8)\n" +
            "                   then 1\n" +
            "               else 0 end)                                                                   as eightHourDone,\n" +
            "       sum(case\n" +
            "               when (status = 2 and warningType != 5 and cast(timestampdiff(HOUR, createDateTime, dealTime) as SIGNED) >= 8) then 1\n" +
            "               else 0 end)                                                                   as gtEightHourDone\n" +
            "from warning_info\n" +
            "where createDateTime between ?1 and ?2\n" +
            "  and status = 2\n" +
            "  and roleID in ?3\n" +
            "  and warningType != 5;" , nativeQuery = true)
    List<Map<String , Object>> findFinishSummary(Date sTime , Date eTime , List<String> roleIDs);


    @Query(value = "select * from warning_info where moduleCode in ?1" , nativeQuery = true)
    List<WarningInfo> findWarningRulesByModuleCodes(List<String> moduleCodes);
}
