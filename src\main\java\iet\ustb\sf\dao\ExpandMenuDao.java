package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.ExpandMenu;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.dao
 * @title: ExpandMenuDao
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/2919:10
 */
public interface ExpandMenuDao extends JpaSpecificationExecutor<ExpandMenu>, JpaRepository<ExpandMenu, String> {

    @Query(value = "select * from ExpandMenu where rscID = ?1 and userNo = ?2", nativeQuery = true)
    ExpandMenu findExpandMenuByUserNoAndRscID(String rscID, String userNo);
}
