package iet.ustb.sf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import iet.ustb.sf.vo.PortMonitoringInfo;
import iet.ustb.sf.vo.domain.IconImg;

/**
 * 接口监控(PortMonitoringInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-21 17:05:46
 */
public interface PortMonitoringInfoService {

    IPage<PortMonitoringInfo> getPortMonitoringInfoByPage(PortMonitoringInfo portMonitoringInfo);

    PortMonitoringInfo getPortMonitoringDetail (PortMonitoringInfo portMonitoringInfo);
}

