package iet.ustb.sf.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @PackageName: com.nercar.iet.ustb.devicePrecision.vo
 * @title: PageVo
 * @projectName iet-iet-service
 * @description: TODO
 * @date 2021/11/2310:25
 */

@Data
public class PageVo<T> {

    int totalElements;

    int totalPages;

    Map<String , Integer> pageable;

    List<T> content;

}

@Data
@AllArgsConstructor
class Pageable{
    int pageIndex;
    int pageSize;

}
