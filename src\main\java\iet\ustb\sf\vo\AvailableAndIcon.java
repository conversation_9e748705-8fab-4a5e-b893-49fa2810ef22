package iet.ustb.sf.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.sql.Blob;

/**
 * @Title:
 * @author: 赵荣超
 * @date: 2024/1/8 11:31
 * @Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AvailableAndIcon implements Serializable {
    String id;
    String code;
    String icon;
    int isShow;
    String name;
    String parentId;
    String serviceName;
    int sort;
    int status;
    String type;
    String url;
    String isSelected;
    String ip;
    String port;
    String pluginSize;
    String iconType;
    String deskIcon;
    String handleDeveloper;
    String handleUser;
    String imgIconName;
    String imgIconResource;
}
