package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.OnlineMgt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface OnlineMgtDao extends JpaSpecificationExecutor<OnlineMgt>, JpaRepository<OnlineMgt, String> {

    @Query("from OnlineMgt where parentId = ?1")
    public List<OnlineMgt> findByParentId(String parentId);
}
