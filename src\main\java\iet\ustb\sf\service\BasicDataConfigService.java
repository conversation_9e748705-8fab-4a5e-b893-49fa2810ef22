package iet.ustb.sf.service;

import iet.ustb.sf.vo.domain.BasicDataConfig;
import iet.ustb.sf.vo.domain.DictionaryDtl;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Title: BasicDataConfigService
 * @Date: 23/12/27 09:0147
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */
public interface BasicDataConfigService {
    public BasicDataConfig findBDCbyType(String type);

    List<DictionaryDtl> getBasicDataInfo(BasicDataConfig basicDataConfig);
}
