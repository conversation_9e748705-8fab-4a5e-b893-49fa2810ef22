package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.WarningInfo;
import iet.ustb.sf.vo.DoneRateVo;
import iet.ustb.sf.vo.PageVo;
import iet.ustb.sf.vo.WarningInfoVo;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @Author: Dr.Monster
 * @Title: WaringInfoService
 * @Date: 23/10/31 11:4534
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */
public interface WarningInfoService {

    Page<WarningInfo> findPageByMultiCondition(JSONObject jsonObject);

    Page<WarningInfo> findFalsePageByMultiCondition(JSONObject jsonObject);

    PageVo<WarningInfoVo> findPageByMultiConditionNew(JSONObject jsonObject);

    void exportInfos(JSONObject jsonObject, HttpServletResponse response);
    void exportFalseInfos(JSONObject jsonObject, HttpServletResponse response);

    void batchSaveInfos(JSONObject jsonObject);

    void saveFalseInfo(JSONObject jsonObject);

    List<Map<String , String>> findModuleInfoList();

    List<Map<String , String>> findAreaNameList();

    List<DoneRateVo> getDoneRateByRoleIDs(JSONObject jsonObject);

    List<DoneRateVo> getDoneRateGroupRoles(JSONObject jsonObject);

    List<DoneRateVo> getUndoneListByRoleIDsWithTime(JSONObject jsonObject);

    void exportDoneRate(JSONObject jsonObject, HttpServletResponse response);

    PageVo<JSONObject> findRuleAlertCount(JSONObject jsonObject);

    Page<WarningInfo> findHistoryByAlertID(JSONObject jsonObject);

    Page<WarningInfo> findHistoryByWarningRuleID(JSONObject jsonObject);

    void exportHistory(JSONObject jsonObject, HttpServletResponse response);

    void exportHistoryByWarningRuleID(JSONObject jsonObject, HttpServletResponse response);

    void exportAlertInfoByRoleIDs(JSONObject jsonObject, HttpServletResponse response);

    List<Map<String , Object>> findHistoryByWaringEventID(JSONObject jsonObject);

    List<Map<String , Object>> findSummaryByWarningRuleID(JSONObject jsonObject);

    List<DoneRateVo> getDoneRateByModuleCodes(JSONObject jsonObject);

    void exportDoneRateByModuleCodes(JSONObject jsonObject, HttpServletResponse response);

    Map<String , List<DoneRateVo>> findRateByRoleIDsWithTime(JSONObject jsonObject);

    Map<String , List<DoneRateVo>> findRateByModuleCodesWithTime(JSONObject jsonObject);

    Map<String , List<DoneRateVo>> findIsFalseByModuleCodesWithTime(JSONObject jsonObject);

    List<DoneRateVo> getUnDoneRateByRoleIDs(JSONObject jsonObject);

    List<DoneRateVo> getUnDoneRateGroupRoles(JSONObject jsonObject);

    Page<WarningInfo> findHistoryByRoleAndTime(JSONObject jsonObject);

    Map<String , Object> findFinishSummary(JSONObject jsonObject);

}
