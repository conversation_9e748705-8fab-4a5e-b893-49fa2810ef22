package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.WeeklySummary;
import iet.ustb.sf.service.WeeklySummaryService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 周/月/日总结
 *
 * <AUTHOR>
 * @create 2023-01-04
 */
@RestController
@RequestMapping("/weeklySummary")
@Api(value = "周/月/日总结", tags = "周/月/日总结")
public class WeeklySummaryController {

    @Autowired
    private WeeklySummaryService weeklySummaryService;

    @PostMapping("/findAll")
    @ApiOperation(value = "查询所有", notes = "查询所有")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<WeeklySummary> weeklySummaryList = weeklySummaryService.findAll();
            ajaxJson.setData(weeklySummaryList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findPageByMultiCondition")
    @ApiOperation(value = "按多条件查找分页", notes = "入参：{\"startDate\": \"2022-12-30\",\"endDate\": \"2023-01-01\",\"type\": 2}")
    public AjaxJson findPageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<WeeklySummary> weeklySummaryPage = weeklySummaryService.findPageByMultiCondition(jsonObject);
            ajaxJson.setData(weeklySummaryPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存", notes = "保存")
    public AjaxJson save(@RequestBody WeeklySummary weeklySummary) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            weeklySummaryService.save(weeklySummary);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }
    @PostMapping("/saveList")
    @ApiOperation(value = "保存", notes = "保存")
    public AjaxJson saveList(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {

            weeklySummaryService.saveList(jsonObject);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "入参：{\"id\":\"f356e461-0565-4ed4-bed1-29fb810bb160\"}")
    public AjaxJson delete(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            weeklySummaryService.delete(jsonObject);
            ajaxJson.setData("删除成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

}
