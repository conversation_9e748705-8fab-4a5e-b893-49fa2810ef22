package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.DictionaryDtl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface DictionaryDtlDao extends JpaSpecificationExecutor<DictionaryDtl>, JpaRepository<DictionaryDtl, String> {

    @Query(value = "select "
            + " new iet.ustb.sf.vo.domain.DictionaryDtl"
            + "(b.code,b.value,b.value1,b.value2,b.value3,b.description) "
            + " from Dictionary a "
            + " left join DictionaryDtl b on a.id = b.dict.id "
            + " where a.code = ?1 order by b.createDateTime")
    List<DictionaryDtl> findByDictCode(String dictCode);
    @Query(value = "select "
            + " new iet.ustb.sf.vo.domain.DictionaryDtl"
            + "(b.code,b.value,b.value1,b.value2,b.value3,b.description) "
            + " from Dictionary a "
            + " left join DictionaryDtl b on a.id = b.dict.id "
            + " where a.id = ?1 order by b.createDateTime")
    List<DictionaryDtl> findByDictId(String dictId);
}
