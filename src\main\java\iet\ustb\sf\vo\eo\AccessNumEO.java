package iet.ustb.sf.vo.eo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 访问次数排行（组织、用户、应用）
 *
 * <AUTHOR>
 * @create 2022-11-03
 * @see Serializable
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccessNumEO implements Serializable {

    /**
     * 编码
     */
    @Excel(name = "编码",width = 20)
    private String code;
    /**
     * 父编码
     */
    private String parentCode;
    /**
     * 名称
     */
    @Excel(name = "名称", width = 50)
    private String name;
    /**
     * 父名称
     */
    private String parentName;
    /**
     * 部门名称
     */
    @Excel(name = "部门名称")
    private String orgName;
    /**
     * 部门人数
     */
    @Excel(name = "部门人数")
    private Long orgNum;
    /**
     * 应访问人数
     */
    @Excel(name = "应访问人数")
    private Long shouldUseNum;
    /**
     * 使用人数
     */
    @Excel(name = "使用人数")
    private Long useNum;
    /**
     * 占比(%)
     */
    @Excel(name = "占比(%)")
    private BigDecimal rate;
    /**
     * 访问次数
     */
    @Excel(name = "访问次数")
    private Long num;
    /**
     * 人均访问次数
     */
    @Excel(name = "人均访问次数")
    private Long avgNum;
    /**
     * 层级：1-厂处 2-科室 3-班组
     */
    private Integer level;

    /**
     * 岗位名称
     */
    private String postName;

    public AccessNumEO(Long useNum, Long num) {
        this.useNum = useNum;
        this.num = num;
    }

    public AccessNumEO(String code, Long num) {
        this.code = code;
        this.num = num;
    }

    public AccessNumEO(String code, String name, Long num) {
        this.code = code;
        this.name = name;
        this.num = num;
    }

    public AccessNumEO(String code, String parentName, String name, Long num) {
        this.code = code;
        this.parentName = parentName;
        this.name = name;
        this.num = num;
    }

    public AccessNumEO(String code, String name, Long num, String orgName) {
        this.code = code;
        this.name = name;
        this.num = num;
        this.orgName = orgName;
    }

    public AccessNumEO(String code, String name, String postName, Long num, String orgName) {
        this.code = code;
        this.name = name;
        this.postName = postName;
        this.num = num;
        this.orgName = orgName;
    }

    public AccessNumEO(String code, String name, Integer level, Long useNum, Long num) {
        this.code = code;
        this.name = name;
        this.level = level;
        this.useNum = useNum;
        this.num = num;
    }
}
