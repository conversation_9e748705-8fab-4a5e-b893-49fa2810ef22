package iet.ustb.sf.vo.domain;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 问题反馈
 *
 * <AUTHOR>
 * @create 2022-10-26
 */
@Data
@Entity
@Table(name = "FEED_BACK")
@ApiModel(value = "问题反馈")
public class FeedBack extends BaseEntity{

    /**
     * 标题
     */
    @Column(nullable = false)
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 描述
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "description", columnDefinition = "CLOB")
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 应用编号
     */
    @Column(nullable = false)
    @ApiModelProperty(value = "应用编号")
    private String serviceNo;

    /**
     * 资源id
     */
    @Column(length = 64)
    private String resourceId;

    /**
     * 问题类型 ：1-建议 2-bug 3-开发者bug
     */
    @Column(nullable = false)
    @ApiModelProperty(value = "问题类型 ：1-建议 2-bug 3-开发者bug")
    private Integer quesType;

    /**
     * 提出者编号
     */
    @Column(nullable = false)
    @ApiModelProperty(value = "提出者编号")
    private String quesUserNo;

    /**
     * 处理者编号
     */
    @ApiModelProperty(value = "处理者编号")
    private String handleUserNo;

    /**
     * 提出者组织编号
     */
    @ApiModelProperty(value = "提出者组织编号")
    private String quesOrgCode;

    @Transient
    @ApiModelProperty(value = "提出者用户名称")
    private String quesUserName;

    @Transient
    @ApiModelProperty(value = "处理者用户名称")
    private String handleUserName;

    @Transient
    @ApiModelProperty(value = "提出者组织名称")
    private String quesOrgName;

    @Transient
    @ApiModelProperty(value = "处理者组织名称")
    private String handleOrgName;

    @Transient
    @ApiModelProperty(value = "父集菜单")
    private String parentName;

    @Transient
    @ApiModelProperty(value = "页面名称")
    private String pageName;

    /**
     * 提出者联系方式
     */
    @ApiModelProperty(value = "提出者联系方式")
    private String quesContact;

    /**
     * 处理者联系方式
     */
    @ApiModelProperty(value = "处理者联系方式")
    private String handleContact;

    /**
     * 处理状态 ：1-待处理 2-已处理 3-催办 4-关闭 5-处理中 6-退回
     */
    @Column(columnDefinition = "int default 1")
    @ApiModelProperty(value = "处理状态 ：1-待处理 2-已处理 3-催办 4-关闭 5-处理中 6-退回")
    private Integer handleStatus;

    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间")
    private String handleTime;

    /**
     * 处理者描述
     */
    @ApiModelProperty(value = "处理者描述")
    private String handleDesc;

    /**
     * 计划完成日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划完成日期")
    private Date planFinishDate;

    /**
     * 附件集合
     */
    @Transient
    @ApiModelProperty(value = "附件集合")
    private List<Attach> attachList;

    /**
     * 问题反馈评价集合
     */
    @Transient
    @ApiModelProperty(value = "问题反馈评价集合")
    private List<Comment> commentList;
}
