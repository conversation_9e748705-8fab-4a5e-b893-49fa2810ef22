package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.DashConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.dao
 * @title: DashConfigDao
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/2710:56
 */
public interface DashConfigDao extends JpaSpecificationExecutor<DashConfig>, JpaRepository<DashConfig, String> {

    @Query(value = "select * from DashConfig where id = ?1 and userNo = ?2", nativeQuery = true)
    DashConfig findOneByIDAndUserNo(String id, String userNo);
}
