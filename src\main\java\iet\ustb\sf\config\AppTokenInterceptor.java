package iet.ustb.sf.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.User;
import iet.ustb.sf.service.UserService;
import iet.ustb.sf.utils.JwtUtil;
import iet.ustb.sf.utils.UserThreadUtil;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.apachecommons.CommonsLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 应用令牌拦截器
 *
 * <AUTHOR>
 * @create 2022-10-26
 * @see HandlerInterceptor
 */
@Component
@CommonsLog
public class AppTokenInterceptor implements HandlerInterceptor {
    @Lazy
    @Autowired
    private UserService userService;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String authorization = request.getHeader("authorization");
        if (StringUtils.isBlank(authorization)){
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("{\"message\": \"token can't be empty\", \"success\": false}");
            return false;
        }
        String userNo = JwtUtil.getUserNoByToken(authorization.replace("Bearer ",""));
        if (StringUtils.isBlank(userNo)){
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("{\"message\": \"token invalid\", \"success\": false}");
            return false;
        }
        Map<Object, Object> map = redisTemplate.opsForHash().entries("loginUser_" + userNo);
        User user;
        // redis中没有查到数据，则从数据库中查
        if (map.isEmpty()) {
            JSONObject json = new JSONObject();
            json.put("userNo", userNo);
            user = userService.findOneUserByUserNo(json);
        } else {
            user = JSON.parseObject(JSON.toJSONString(map), User.class);
        }

        if (user == null) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("{\"message\": \"user not found\", \"success\": false}");
            return false;
        }
        // 保存到当前线程：每一个请求都是一个线程
        UserThreadUtil.setUser(user);
        return true;

    }
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable ModelAndView modelAndView){
    }
}
