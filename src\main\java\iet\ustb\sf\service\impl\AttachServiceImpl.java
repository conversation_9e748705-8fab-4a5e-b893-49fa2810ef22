package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.AttachDao;
import iet.ustb.sf.vo.domain.Attach;
import iet.ustb.sf.vo.domain.User;
import iet.ustb.sf.service.AttachService;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.utils.UserThreadUtil;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.Cleanup;
import lombok.extern.apachecommons.CommonsLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.criteria.Predicate;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 附件
 *
 * <AUTHOR>
 * @create 2022-10-27
 */
@Service
@CommonsLog
public class AttachServiceImpl implements AttachService {

//    private final static String ROOT_PATH = "/Users/<USER>/IdeaProjects/iet/iet-resources-service/var/files/";// 本地
//    private final static String ROOT_PATH = "D:\\project\\Zhzmproject\\iet_resource_service\\var\\files\\";// 本地
    private final static String ROOT_PATH = "/var/files/";// 正式

    @Autowired
    private AttachDao attachDao;

    @Override
    public List<Attach> findAll() {
        return attachDao.findAll();
    }

    @Override
    public List<Attach> findByRelatedId(String relatedId) {
        return attachDao.findByRelatedId(relatedId);
    }

    @Override
    public List<Attach> findByRelatedIds(List<String> relatedIdList) {
        return attachDao.findByRelatedIds(relatedIdList);
    }

    @Override
    public List<Attach> findAllByMultiCondition(JSONObject jsonObject) {
        return attachDao.findAll(createSpecs(jsonObject));
    }

    @Override
    public Page<Attach> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<Attach> feedBackPage = attachDao.findAll(createSpecs(jsonObject), pageable);
        return feedBackPage;
    }

    /**
     * 按多条件查找
     *
     * @param json json
     * @return {@link Specification }<{@link Attach }>
     * <AUTHOR>
     * @create 2022-10-27
     */
    private Specification<Attach> createSpecs(JSONObject json) {
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            JSONArray idJsonArray = json.getJSONArray("ids");
            String serviceNo = json.getString("serviceNo");// 应用编号
            String name = json.getString("name");// 附件名称
            String relatedId = json.getString("relatedId");// 业务id

            if (idJsonArray != null && idJsonArray.size() > 0) {
                list.add(root.get("id").in(idJsonArray));
            }
            if (StringUtils.isNotBlank(serviceNo)) {
                list.add(cb.equal(root.get("serviceNo"), serviceNo));
            }
            if (StringUtils.isNotBlank(name)) {
                list.add(cb.like(root.get("name"), "%" + name + "%"));
            }
            if (StringUtils.isNotBlank(relatedId)) {
                list.add(cb.equal(root.get("relatedId"), relatedId));
            }
            return query.where(list.toArray(new Predicate[list.size()])).getRestriction();
        };
    }

    @Override
    public Attach save(Attach attach) {
        return attachDao.save(attach);
    }

    @Override
    public List<Attach> saveAll(List<Attach> attachList) {
        return attachDao.saveAll(attachList);
    }

    @Override
    public List<Attach> uploadFile(MultipartFile[] files, String relatedId, String serviceNo) throws Exception {
        if (files == null || files.length == 0) {
            return null;
        }
        List<Attach> attachList = new ArrayList<>();
        for (MultipartFile multipartFile : files) {
            double size = multipartFile.getSize() / 1024L;// KB

            String fileRealName = multipartFile.getOriginalFilename();// 原始名称

            String[] str = fileRealName.split("\\.");

            String fileType = str[str.length - 1];
            String fileName = UUID.randomUUID() + "." + fileType;
            log.info("java 135"+fileName);

            String userNo;
            User user = UserThreadUtil.getUser();
            if (user != null) {
                userNo = user.getUserNo() != null ? user.getUserNo() : "admin";
            } else {
                userNo = "admin";
            }
            String fileDir = ROOT_PATH + userNo;
            log.info("java 145"+fileDir);
            File file = new File(fileDir);
            if (!file.exists()) {
                Boolean b =file.mkdirs();
                log.info("java file.mkdir(): "+b);
                Files.createDirectory(file.toPath());
            }

            File uploadedFile = new File(fileDir, fileName);
            log.info("java 150"+file+"uploadedFile:"+uploadedFile);
            multipartFile.transferTo(uploadedFile);
            log.info("java 152"+file+"uploadedFile:"+uploadedFile);
            Attach attach = new Attach();
            attach.setName(fileRealName);// 真实名称
            attach.setPath(fileDir + "/" + fileName);// 路径 + uuid名称
            attach.setFileSize(size);
            attach.setFileType(fileType);
//            attach.setDescription("");
//            attach.setRelatedType("");
            if (StringUtils.isNotBlank(serviceNo)) {
                attach.setServiceNo(serviceNo);
            }
            if (StringUtils.isNotBlank(relatedId)) {
                attach.setRelatedId(relatedId);
            }
//            attach.setRelatedId("");// 关联的业务ID 后面更新时写入

            attachList.add(attach);
            log.info("java 171 attach "+attach);
        }
        return attachDao.saveAll(attachList);
    }

    @Override
    public void downloadFileById(String id, HttpServletResponse response) throws Exception {
        Assert.notNull(id, "附件id不能为空");

        Attach attach = attachDao.findById(id).orElse(null);
        if (attach == null) {
            throw new Exception("附件id不存在");
        }
        //1.要获取下载文件的路径
        String path = attach.getPath();

        //2.获取下载的文件名
        String fileName = attach.getName();

        //3.让浏览器能够支持下载文件，中文名有可能出现乱码(所以要将文件名转为UTF-8格式)
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

        //4.获取下载文件的输入流
        @Cleanup FileInputStream in = new FileInputStream(path);

        //5.创建buffer缓冲区
        int len = 0;

        byte[] buffer = new byte[1024];

        //6.获取OutputStream对象
        @Cleanup ServletOutputStream out = response.getOutputStream();

        //7.将FileOutputStream写入缓冲区，使用OutputStream将缓冲区的内容写到客户端！
        while ((len = in.read(buffer)) > 0) {
            out.write(buffer, 0, len);
        }
    }

    @Override
    public void deleteByIds(JSONObject jsonObject) throws Exception {
        JSONArray jsonArray = jsonObject.getJSONArray("ids");
        for (int i = 0; i < jsonArray.size(); i++) {
            String id = jsonArray.getString(i);
            Attach attach = attachDao.findById(id).orElse(null);
            // 删除附件和附件表记录
            deleteAttach(attach);
        }
    }

    @Override
    public void deleteByRelatedId(String relatedId) {
        List<Attach> attachList = attachDao.findByRelatedId(relatedId);
        for (Attach attach : attachList) {
            // 删除附件和附件表记录
            this.deleteAttach(attach);
        }
    }

    /**
     * 删除附件和附件表记录
     *
     * @param attach 贴上
     * <AUTHOR>
     * @create 2022-10-29
     */
    private void deleteAttach(Attach attach) {
        if (attach != null) {
            String path = attach.getPath();
            new File(path).delete();// 删除附件
            attachDao.deleteById(attach.getId());// 删除附件表记录
        }
    }

    @Override
    public List<Attach> findByIds(List<String> idList) {
        return attachDao.findAllById(idList);
    }
}
