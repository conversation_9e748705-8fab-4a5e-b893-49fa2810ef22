package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.MessagePushMap;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: Dr.<PERSON>
 * @Title: MessagePushMapDao
 * @Date: 24/01/02 16:4319
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */
public interface MessagePushMapDao extends JpaRepository<MessagePushMap, Serializable> {

    @Query(value = "select * from message_push_map where summaryType = ?1" , nativeQuery = true)
    List<MessagePushMap> findMessagePushMapsBySummaryType(int summaryType);

    @Query(value = "select * from message_push_map where parentID = ?1" , nativeQuery = true)
    List<MessagePushMap> findMessagePushMapsByParentID(String parentID);


}
