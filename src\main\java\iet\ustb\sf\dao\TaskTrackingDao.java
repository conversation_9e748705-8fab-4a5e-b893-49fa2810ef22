package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.TaskTracking;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;

public interface TaskTrackingDao extends JpaSpecificationExecutor<TaskTracking>, JpaRepository<TaskTracking, String> {

    /**
     * 获取任务评分统计排行
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * @Author: 023958 茆荣伟
     * @Create: 2024-04-07 01:00
     */
    @Query(value = "SELECT"
            + "    t.serviceUser,"
            + "    AVG(t.score)                             AS avgScore,"
            + "    COUNT(1)                                 AS totalTasks,"
            + "    COUNT(CASE WHEN t.status = 2 THEN 1 END) AS scheduleCompletedTasks,"
            + "    COUNT(CASE WHEN t.status = 5 THEN 1 END) AS overdueCompletedTasks,"
            + "    COUNT(CASE WHEN t.status = 7 THEN 1 END) AS delayCompletedTasks,"
            + "    COUNT(CASE WHEN t.status = 6 THEN 1 END) AS delayUncompletedTasks"
            + " FROM"
            + "    task_tracking t"
            + " GROUP BY"
            + "    t.serviceUser"
            + " ORDER BY"
            + "    avgScore DESC", nativeQuery = true)
    List<Map<String, Object>> findTaskScoreStatistics();

}
