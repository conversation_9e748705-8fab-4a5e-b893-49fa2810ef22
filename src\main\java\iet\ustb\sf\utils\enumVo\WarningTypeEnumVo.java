package iet.ustb.sf.utils.enumVo;

import lombok.Data;

/**
 * @Author: Dr.Monster
 * @Title: WarningTypeEnumVo
 * @Date: 23/12/02 11:2041
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */

public enum WarningTypeEnumVo {
    WT_0(0 , "生产保供"),
    WT_1(1,"仪表跳变"),
    WT_2(2 , "通信异常"),
    WT_3(3 , "超标报警"),
    WT_4(4 , "经济运行制度报警"),
    WT_5(5 , "生产过程监测"),
    WT_6(6 , "趋势校验报警"),
    WT_7(7 , "故障诊断报警");

    WarningTypeEnumVo(int code, String name) {
        this.code = code;
        this.name = name;
    }

    int code;
    String name;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
