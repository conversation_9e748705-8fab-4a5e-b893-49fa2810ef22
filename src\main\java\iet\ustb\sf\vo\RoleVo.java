package iet.ustb.sf.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: Dr.Monster
 * @Slogan: The never-compromising angry youth.
 * @PackageName: iet.ustb.sf.vo
 * @Date: 2022/12/02/17:17
 * @Description:
 */
@Data
public class RoleVo {

    String id;

    String createUserNo;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    Date createDateTime;

    String updateUserNo;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    Date updateDateTime;

    String roleCode;

    String roleName;

    String description;

    String type;

    String isDef;

    int status;

    //角色类型，1-页面访问角色，2-报警推送角色
    Integer roleType;

    //组织架构ID，宽版，一炼钢等
    String orgID;

    //模块名称,能源，设备
    String moduleName;

    //模块code
    String moduleCode;
}
