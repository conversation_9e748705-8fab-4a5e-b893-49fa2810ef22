package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.AjaxJson;
import iet.ustb.sf.vo.domain.BasicDataConfig;
import iet.ustb.sf.service.BasicDataConfigService;
import iet.ustb.sf.vo.domain.IconImg;
import io.swagger.annotations.Api;
import lombok.extern.apachecommons.CommonsLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户控制器
 * Created by t<PERSON><PERSON><PERSON><PERSON><PERSON> on 16/6/4.
 */

@RestController
@RequestMapping("/basicDataConfig")
@Api(tags = "IDM-基本数据配置")
@CommonsLog
public class BasicDataConfigController {


    @Autowired
    protected BasicDataConfigService basicDataConfigService;


    @PostMapping("/findBasicDataConfigByType.form")
    public BasicDataConfig findBasicDataConfigByType(@RequestBody JSONObject content) {
        return basicDataConfigService.findBDCbyType(content.getString("type"));
    }


    @PostMapping("/getBasicDataInfo")
    public AjaxJson getBasicDataInfo(@RequestBody BasicDataConfig basicDataConfig) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(basicDataConfigService.getBasicDataInfo(basicDataConfig));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.getMessage());
        }
        return ajaxJson;
    }
}


