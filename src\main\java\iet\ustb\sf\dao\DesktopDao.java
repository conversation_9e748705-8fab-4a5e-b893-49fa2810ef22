package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.Desktop;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.dao
 * @title: DesktopDao
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/2516:24
 */
public interface DesktopDao extends JpaSpecificationExecutor<Desktop>, JpaRepository<Desktop, String> {

    @Query(value = "select * from desktop where name = ?1 and deleteTag = 0", nativeQuery = true)
    Desktop findDesktopByName(String name);

    @Query(value = "select * from desktop where id = ?1 and createUserNo = ?2 and deleteTag = 0", nativeQuery = true)
    Desktop findDesktopByIDAndUserNo(String id, String userNo);

    @Query(value = "select distinct tmp.* " +
            "from ( " +
            "         select * " +
            "         from desktop " +
            "         where userNo = ?1 and deleteTag = 0 " +
            "         union all " +
            "         select * " +
            "         from desktop\n" +
            "         where name in ('仪表板', '主页面', '工作台')) tmp", nativeQuery = true)
    List<Desktop> findDesktopByUserNo(String userNo);
}
