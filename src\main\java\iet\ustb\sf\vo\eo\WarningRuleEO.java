package iet.ustb.sf.vo.eo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import javax.persistence.Column;

/**
 * @Author: Dr.Monster
 * @Title: WarningRuleEO
 * @Date: 23/09/21 15:3201
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */

@Data
public class WarningRuleEO {

    //模块名称
    @Excel(name = "模块名称")
    String moduleName;

    //模块代码
    @Excel(name = "模块代码")
    String moduleCode;

    //规则名称
    @Excel(name = "规则名称")
    String ruleName;

    //规则描述
    @Excel(name = "规则描述")
    String ruleDesc;

    //指标数值
    @Excel(name = "指标数值")
    String ruleValue;

    //区域名称
    @Excel(name = "区域名称")
    String areaName;

    //产线名称
    @Excel(name = "产线名称")
    String productionLineName;

    @Excel(name = "设备名称")
    String deviceName;

    //点位名称
    @Excel(name = "点位名称")
    String pointName;

    //报警等级
    @Excel(name = "报警等级")
    String alertLevel;

    //推送角色名称
    @Excel(name = "推送角色名称")
    String pushRoleName;

    //责任人名称
    @Excel(name = "责任人名称")
    String liablePersonName;

    @Excel(name = "规则类型")
    String ruleTypeName;
}
