package iet.ustb.sf.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Getter
@Component
@RefreshScope
public class NacosConfig {

    @Value("${api.bd}")
    private String apiBd;

    @Value("${api.bd-name}")
    private String apiBdName;

    @Value("${api.bd-menu}")
    private String apiBdMenu;

    @Value("${auth.appId}")
    private String authAppId;

    @Value("${auth.appSecret}")
    private String authAppSecret;

    @Value("${minio.url}")
    private String minioUrl;

    @Value(value = "${minio.bucket}")
    private String minioBucket;

    @Value(value = "${minio.host}")
    private String minioHost;

    @Value(value = "${minio.access-key}")
    private String minioAccessKey;

    @Value(value = "${minio.secret-key}")
    private String minioSecretKey;

}
