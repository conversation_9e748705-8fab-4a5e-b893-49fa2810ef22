package iet.ustb.sf.vo.domain;

import lombok.Data;
import org.hibernate.annotations.Proxy;

import javax.persistence.*;

/**
 * @Author: Dr.Monster
 * @Title: WarningRule
 * @Date: 23/09/21 11:2934
 * @Slogan: The never-compromising angry youth
 * @Remark:$
 */

@Data
@Entity
@Table(name = "Warning_Rule")
@Proxy(lazy = false)
public class WarningRule extends BaseEntity{

    //模块名称
    @Column
    public String moduleName;

    //模块代码
    @Column
    public String moduleCode;

    //规则名称
    @Column
    public String ruleName;

    //规则描述
    @Column
    public String ruleDesc;

    //指标数值
    @Column
    public String ruleValue;

    //区域ID
    @Column
    public String areaID;

    //区域名称
    @Column
    public String areaName;

    //产线ID
    @Column
    public String productionLineID;

    //产线名称
    @Column
    public String productionLineName;

    //设备ID
    @Column
    public String deviceID;

    //设备名称
    @Column
    public String deviceName;

    //点位ID
    @Lob
    public String pointID;

    //点位名称
    @Column
    public String pointName;

    //报警等级
    @Column
    public String alertLevel;

    //推送角色ID
    @Column
    public String pushRoleID;

    //推送角色名称
    @Column
    public String pushRoleName;

    //责任人ID
    @Column
    public String liablePersonID;

    //责任人名称
    @Column
    public String liablePersonName;

    //0-生产保供，1-仪表跳变，2-通信异常，3-超标报警，4-经济运行制度报警，5-生产过程监测，6-趋势校验报,7-故障诊断
    @Column
    public Integer ruleType;

    //0-禁用，1-启用
    @Column
    public Integer status;

    @Transient
    public String ruleTypeName;

    //0-不推送,1-推送
    @Column
    Integer pushMode = 1;
}
