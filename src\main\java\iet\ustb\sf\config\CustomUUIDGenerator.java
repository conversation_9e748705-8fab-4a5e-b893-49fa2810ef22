package iet.ustb.sf.config;


import iet.ustb.sf.vo.domain.BaseEntity;
import io.micrometer.core.instrument.util.StringUtils;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.UUIDGenerator;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 自定义id生成策略：若id存值(不为空)时,直接取id值,否则取uuid
 * @create 2022/6/18
 */
public class CustomUUIDGenerator extends UUIDGenerator {

    @Override
    public Serializable generate(SharedSessionContractImplementor session, Object object) throws HibernateException {
        BaseEntity baseEntity = (BaseEntity) object;
        if (baseEntity != null) {
            String id = baseEntity.getId();
            if (StringUtils.isNotBlank(id)) {
                return id;
            }
        }
        UUIDGenerator uuidGenerator = buildSessionFactoryUniqueIdentifierGenerator();
        Serializable serializable = uuidGenerator.generate(session, object);
        return serializable;
    }

}
