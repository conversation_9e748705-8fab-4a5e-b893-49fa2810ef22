package iet.ustb.sf.utils;

import iet.ustb.sf.service.impl.OrgServiceImpl;
import iet.ustb.sf.service.impl.ResourceServiceImpl;
import iet.ustb.sf.utils.constant.EnumConstant;


/**
 *  编码生成器
 * <AUTHOR>
 */

public class CodeGenerator {

    // 机器ID = 1, 数据中心ID = 1
    private static final SnowflakeIdGenerator SNOWFLAKE = new SnowflakeIdGenerator(1, 1);

    /**
     * 生成用户编码 user_雪花算法
     * @return
     */
    public static String generateUserCode() {
        return EnumConstant.USER_CODE + EnumConstant.UNDERLINE + SNOWFLAKE.nextId();
    }

    /**
     * 生成角色编码 role_雪花算法
     * @return
     */
    public static String generateRoleCode() {
        return EnumConstant.ROLE_CODE + EnumConstant.UNDERLINE + SNOWFLAKE.nextId();
    }


    /**
     * 生成菜单编码 res_雪花算法_层级编码
     * @param parentCode
     * @return
     */
    public static String generateMenuCode(ResourceServiceImpl resourceService,String parentCode) {

        long uniqueId = SNOWFLAKE.nextId();
        if (parentCode == null || parentCode.isEmpty()) {
            // 例如 "001", "002"
            String nextTopLevelCode = resourceService.getNextTopLevelCode();
            return EnumConstant.RES_CODE + EnumConstant.UNDERLINE + uniqueId + EnumConstant.UNDERLINE +  nextTopLevelCode;
        } else {
            // 计算下一个子菜单编号
            // 例如 "002-001", "002-002"
            String nextSubLevelCode = resourceService.getNextSubLevelCode(parentCode);
            return EnumConstant.RES_CODE + EnumConstant.UNDERLINE + uniqueId + EnumConstant.UNDERLINE + nextSubLevelCode;
        }
    }

    /**
     * 生成组织编码 org_雪花算法_层级编码
     * @param parentCode
     * @return
     */
    public static String generateOrgCode(OrgServiceImpl orgService,String parentCode) {
        long uniqueId = SNOWFLAKE.nextId();
        if (parentCode == null || parentCode.isEmpty()) {
            // 例如 "001", "002"
            String nextTopLevelCode = orgService.getNextTopLevelCode();
            return EnumConstant.RES_CODE + EnumConstant.UNDERLINE + uniqueId + EnumConstant.UNDERLINE +  nextTopLevelCode;
        } else {
            // 获取当前父级菜单的层级编号部分
//            String parentLevelCode = parentCode.substring(parentCode.lastIndexOf(EnumConstant.UNDERLINE) + 1);

            // 计算下一个子菜单编号
            // 例如 "002-001", "002-002"
            String nextSubLevelCode = orgService.getNextSubLevelCode(parentCode);
            return EnumConstant.RES_CODE + EnumConstant.UNDERLINE + uniqueId + EnumConstant.UNDERLINE + nextSubLevelCode;
        }
    }



}
