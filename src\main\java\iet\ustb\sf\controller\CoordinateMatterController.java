package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.CoordinateMatter;
import iet.ustb.sf.service.CoordinateMatterService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 协调事宜Controller
 *
 * <AUTHOR>
 * @create 2022-12-16
 */
@RestController
@RequestMapping("/coordinateMatter")
@Api(value = "协调事宜", tags = "协调事宜")
public class CoordinateMatterController {

    @Autowired
    private CoordinateMatterService coordinateMatterService;

    @PostMapping("/findAll")
    @ApiOperation(value = "查询所有", notes = "查询所有")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<CoordinateMatter> coordinateMatterList = coordinateMatterService.findAll();
            ajaxJson.setData(coordinateMatterList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findPageByMultiCondition")
    @ApiOperation(value = "按多条件查找分页", notes = "入参：{\"matter\": \"mrw\"}")
    public AjaxJson findPageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            // 按多条件查找分页
            Page<CoordinateMatter> coordinateMatterPage = coordinateMatterService.findPageByMultiCondition(jsonObject);
            // 按事项类型分组统计次数
            String statistics = coordinateMatterService.findGroupByMatterType(jsonObject);
            JSONObject json = new JSONObject();
            json.put("list", coordinateMatterPage);
            json.put("statistics", statistics);

            ajaxJson.setData(json);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存", notes = "保存")
    public AjaxJson save(@RequestBody CoordinateMatter coordinateMatter) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            coordinateMatterService.save(coordinateMatter);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/topping")
    @ApiOperation(value = "置顶", notes = "入参：{\"id\": \"4b594c81-9272-4879-be1f-da5ac616e6f8\",\"flag\": true}")
    public AjaxJson topping(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            coordinateMatterService.topping(jsonObject);
            ajaxJson.setData("操作成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/deleteByIds")
    @ApiOperation(value = "删除", notes = "入参：{\"ids\":[\"f356e461-0565-4ed4-bed1-29fb810bb160\",\"f356e461-0565-4ed4-bed1-29fb810bb161\"]}")
    public AjaxJson deleteByIds(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            coordinateMatterService.deleteByIds(jsonObject);
            ajaxJson.setData("删除成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/exportExcel")
    @ApiOperation(value = "导出Excel", notes = "入参：{\"handleStatus\":\"1\"}")
    public AjaxJson exportExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            coordinateMatterService.exportExcel(jsonObject, response);
            ajaxJson.setData("导出成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

}
