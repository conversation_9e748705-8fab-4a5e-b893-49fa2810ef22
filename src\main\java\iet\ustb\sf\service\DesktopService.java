package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.DashConfig;
import iet.ustb.sf.vo.domain.Desktop;
import iet.ustb.sf.vo.domain.DesktopConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.service
 * @title: DesktopService
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/2516:22
 */
public interface DesktopService {

    List<Desktop> getDefaultDesktop(JSONObject jsonObject);

    List<Desktop> getDesktopByUserNo(JSONObject jsonObject);

    Desktop getDesktopByIDAndUserNo(JSONObject jsonObject);

    List<DesktopConfig> getDesktopConfigsByUserNo(JSONObject jsonObject);

    String createDesktop(JSONObject jsonObject);

    String deleteDesktop(JSONObject jsonObject);

//    public String createIcon(JSONObject jsonObject);
//
//    public String createFolder(JSONObject jsonObject);
//
//    public String mergeIcon(JSONObject jsonObject);
//
//    public String folderAddIcon(JSONObject jsonObject);
//
//    public String updateIcon(JSONObject jsonObject);
//
//    public String updateFolder(JSONObject jsonObject);
//
//    public void deleteIcon(JSONObject jsonObject);
//
//    public void deleteFolder(JSONObject jsonObject);

    String saveDesktopConfig(JSONObject jsonObject);

    DesktopConfig getDesktopConfig(JSONObject jsonObject);

    String saveDashConfig(JSONObject jsonObject);

    DashConfig getDashConfig(JSONObject jsonObject);
}
