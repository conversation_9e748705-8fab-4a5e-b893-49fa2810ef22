package iet.ustb.sf.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.Cleanup;

import javax.net.ssl.HttpsURLConnection;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.*;


/**
 * IP工具类
 *
 * <AUTHOR>
 * @create 2022-09-21
 */
public class IpUtil {

	/**
	 * 获取用户真实IP地址，不使用request.getRemoteAddr();的原因是有可能用户使用了代理软件方式避免真实IP地址,
	 *
	 * 可是，如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值，究竟哪个才是真正的用户端的真实IP呢？
	 * 答案是取X-Forwarded-For中第一个非unknown的有效IP字符串。
	 *
	 * 如：X-Forwarded-For：*************, *************, *************,
	 * *************
	 *
	 * 用户真实IP为： *************
	 *
	 * @param request
	 * @return
	 */
	public static String getIpAddress(HttpServletRequest request) {
		String ipAddress = null;
		try {
			ipAddress = request.getHeader("x-forwarded-for");
			if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
				ipAddress = request.getHeader("Proxy-Client-IP");
			}
			if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
				ipAddress = request.getHeader("WL-Proxy-Client-IP");
			}
			if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
				ipAddress = request.getRemoteAddr();
				if (ipAddress.equals("127.0.0.1")) {
					ipAddress = getHostAddress();
				}
			}
			// 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
			// "***.***.***.***".length() == 15
			if (ipAddress != null && ipAddress.length() > 15) {
				if (ipAddress.indexOf(",") > 0) {
					ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
				}
			}

			// 解决请求和响应的IP一致且通过浏览器请求时，request.getRemoteAddr()为"0:0:0:0:0:0:0:1"
			if ("0:0:0:0:0:0:0:1".equals(ipAddress)) {
				ipAddress = getHostAddress();
			}
		} catch (Exception e) {
			ipAddress = "";
		}

		return ipAddress;
	}

	/**
	 * 获取主机地址
	 *
	 * @return {@link String }
	 * <AUTHOR>
	 * @create 2022-09-22
	 */
	public static String getHostAddress() {
		// 根据网卡取本机配置的IP
		InetAddress inet = null;
		try {
			inet = InetAddress.getLocalHost();
		} catch (UnknownHostException e) {
			e.printStackTrace();
		}
		return inet.getHostAddress();
	}

	/**
	 * 通过ip获取城市
	 *
	 * @param ip ip地址
	 * @return {@link String }
	 * <AUTHOR>
	 * @create 2022-09-22
	 */
	public static String getCityByIP(String ip) throws JSONException {
		if("0:0:0:0:0:0:0:1".equals(ip) || "127.0.0.1".equals(ip)){
			return "本地";
		}
		HttpsURLConnection conn = null;
		try {
			URL url = new URL("http://opendata.baidu.com/api.php?query=" + ip + "&co=&resource_id=6006&t=1433920989928&ie=utf8&oe=utf-8&format=json");
			conn = (HttpsURLConnection) url.openConnection();
			@Cleanup InputStreamReader inputStreamReader = new InputStreamReader(conn.getInputStream(), "utf-8");
			@Cleanup BufferedReader reader = new BufferedReader(inputStreamReader);
			String line;
			StringBuffer result = new StringBuffer();
			while ((line = reader.readLine()) != null) {
				result.append(line);
			}
			JSONObject jsonObj = JSONObject.parseObject(result.toString());
			JSONArray jsonArr = (JSONArray) jsonObj.get("data");
			if (jsonArr.size() > 0) {
				//位置
				JSONObject json = (JSONObject) jsonArr.get(0);
				return (String) json.get("location");
			} else {
				return "查无数据";
			}
		} catch (Exception e) {
			return "读取失败";
		} finally {
			if (conn != null) {
				conn.disconnect();
			}
		}
	}
}
