package iet.ustb.sf.controller;

import iet.ustb.sf.vo.domain.SysNoticeUser;
import iet.ustb.sf.service.SysNoticeUserService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 公告和用户关系表(SysNoticeUser)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-28 17:09:33
 */
@RestController
@RequestMapping("sysNoticeUser")
public class SysNoticeUserController {
    /**
     * 服务对象
     */
    @Resource
    private SysNoticeUserService sysNoticeUserService;

    /**
     * 分页查询
     *
     * @param sysNoticeUser 筛选条件
     * @param pageRequest      分页对象
     * @return 查询结果
     */
    @GetMapping
    public ResponseEntity<Page<SysNoticeUser>> queryByPage(SysNoticeUser sysNoticeUser, PageRequest pageRequest) {
        return ResponseEntity.ok(this.sysNoticeUserService.queryByPage(sysNoticeUser, pageRequest));
    }

    /**
     * 新增数据
     *
     * @param sysNoticeUser 实体
     * @return 新增结果
     */
    @PostMapping
    public ResponseEntity<SysNoticeUser> add(SysNoticeUser sysNoticeUser) {
        return ResponseEntity.ok(this.sysNoticeUserService.insert(sysNoticeUser));
    }


}

