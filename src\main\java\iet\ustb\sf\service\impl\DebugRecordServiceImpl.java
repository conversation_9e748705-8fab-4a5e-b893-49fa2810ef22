package iet.ustb.sf.service.impl;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.DebugRecordDao;
import iet.ustb.sf.vo.domain.DebugRecord;
import iet.ustb.sf.vo.domain.User;
import iet.ustb.sf.service.DebugRecordService;
import iet.ustb.sf.utils.ToolsUtil;
import iet.ustb.sf.utils.UserThreadUtil;
import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

/**
 * 调试记录
 *
 * <AUTHOR>
 * @create 2023-04-19
 */
@Service
public class DebugRecordServiceImpl implements DebugRecordService {

    @Autowired
    private DebugRecordDao debugRecordDao;

    @Override
    public List<DebugRecord> findAll() {
        return debugRecordDao.findAll();
    }

    @Override
    public Page<DebugRecord> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);

        Page<DebugRecord> debugRecordPage = debugRecordDao.findAll(createSpecs(jsonObject), pageable);
        return debugRecordPage;
    }

    private Specification<DebugRecord> createSpecs(JSONObject json) {
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            String startTime = json.getString("startTime");
            String endTime = json.getString("endTime");
            Integer serviceNo = json.getInteger("serviceNo");
            Integer category = json.getInteger("category");
            Integer status = json.getInteger("status");
            if (StringUtils.isNotBlank(startTime)) {
                list.add(cb.greaterThanOrEqualTo(root.get("startTime"), startTime));
            }
            if (StringUtils.isNotBlank(endTime)) {
                list.add(cb.lessThanOrEqualTo(root.get("startTime"), endTime));
            }
            if (serviceNo != null) {
                list.add(cb.equal(root.get("serviceNo"), serviceNo));
            }
            if (category != null) {
                list.add(cb.equal(root.get("category"), category));
            }
            if (status != null) {
                list.add(cb.equal(root.get("status"), status));
            }
            query.where(list.toArray(new Predicate[list.size()]));

            List<Order> orderList = new ArrayList<>();
            orderList.add(cb.desc(root.get("startTime")));
            query.orderBy(orderList);

            return query.getRestriction();
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DebugRecord save(DebugRecord debugRecord) {

        if (debugRecord != null) {
            String id = debugRecord.getId();
            if (StringUtils.isBlank(id)) {
//                debugRecord.setStartTime(DateUtils.now());
                User user = UserThreadUtil.getUser();
                String userName = user != null ? user.getUserName() : null;
                debugRecord.setFillUser(userName);
            }/* else {
                if (debugRecord.getStatus() == 1) {
                    debugRecord.setEndTime(DateUtils.now());
                }
            }*/
        }
        return debugRecordDao.save(debugRecord);
    }

    @Override
    public void delete(JSONObject jsonObject) throws Exception {
        String id = jsonObject.getString("id");
        debugRecordDao.deleteById(id);
    }
}
