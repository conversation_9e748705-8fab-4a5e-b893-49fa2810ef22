package iet.ustb.sf.service;

import iet.ustb.sf.dao.BasicDataModelDao;
import iet.ustb.sf.vo.domain.BasicDataModel;
import lombok.extern.apachecommons.CommonsLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @remark
 * @create 2022-02-19 15:48
 **/
@Service
@CommonsLog
public class TestService {

    @Value("$my.test.param1")
    private String myTestParam1;

    @Autowired
    private BasicDataModelDao basicDataModelDao;


    @PersistenceContext
    private EntityManager entityManagerPrimary;

    public void entityManagerPrimaryTest() {
        String sql = "select * from DM_BASIC_DATA_MODEL";
        Query nativeQuery = entityManagerPrimary.createNativeQuery(sql);
        List resultList = nativeQuery.getResultList();

    }

    @Transactional
    public void save() {
        List<BasicDataModel> list = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            BasicDataModel basicDataModel = new BasicDataModel();
            basicDataModel.setName("test" + i);
            list.add(basicDataModel);
        }
        basicDataModelDao.saveAll(list);
    }


    public List<BasicDataModel> findAllByName() {
        String name = "test1%";
        List<BasicDataModel> allByName = basicDataModelDao.findAllByName(name);
        List<BasicDataModel> byNameHql = basicDataModelDao.findByNameHql(name);
        List<BasicDataModel> byNameNative = basicDataModelDao.findByNameNative(name);
//        log.info(JSONObject.toJSONString(allByName));
//        log.info(JSONObject.toJSONString(byNameHql));
//        log.info(JSONObject.toJSONString(byNameNative));
        return allByName;
    }


}
