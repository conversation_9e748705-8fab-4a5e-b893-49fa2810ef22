package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.TaskTracking;
import iet.ustb.sf.service.TaskTrackingService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 任务进度跟踪
 *
 * <AUTHOR>
 * @create 2024-04-01
 */
@RestController
@RequestMapping("/taskTracking")
@Api(value = "任务进度跟踪", tags = "任务进度跟踪")
public class TaskTrackingController {

    @Autowired
    private TaskTrackingService taskTrackingService;

    @PostMapping("/findAll")
    @ApiOperation(value = "查询所有", notes = "查询所有")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<TaskTracking> taskTrackingList = taskTrackingService.findAll();
            ajaxJson.setData(taskTrackingList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findPageByMultiCondition")
    @ApiOperation(value = "按多条件查找分页", notes = "入参：{\"serviceNo\": \"dap\", \"modelNo\": \"1\", \"valueType\":\"2\", \"inputUnit\": \"1\", \"startDate\":\"2023-12-15\", \"endDate\":\"2023-12-15\"}")
    public AjaxJson findPageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<TaskTracking> taskTrackingPage = taskTrackingService.findPageByMultiCondition(jsonObject);
            ajaxJson.setData(taskTrackingPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存", notes = "保存")
    public AjaxJson save(@RequestBody TaskTracking taskTracking) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            taskTrackingService.save(taskTracking);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/saveAll")
    @ApiOperation(value = "批量保存", notes = "批量保存")
    public AjaxJson saveAll(@RequestBody List<TaskTracking> taskTrackings) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            taskTrackingService.saveAll(taskTrackings);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "删除")
    public AjaxJson delete(@RequestBody TaskTracking taskTracking) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            taskTrackingService.delete(taskTracking);
            ajaxJson.setData("删除成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/sendMessageToWecom")
    @ApiOperation(value = "任务超时企业微信推送", notes = "任务超时企业微信推送")
    public AjaxJson sendMessageToWecom() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            taskTrackingService.sendMessageToWecom();
            ajaxJson.setData("推送成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findTaskScoreStatistics")
    @ApiOperation(value = "获取任务评分统计排行", notes = "获取任务评分统计排行")
    public AjaxJson findTaskScoreStatistics() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<Map<String, Object>> list = taskTrackingService.findTaskScoreStatistics();
            ajaxJson.setData(list);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }
}
