package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import iet.ustb.sf.vo.domain.SysNotice;
import iet.ustb.sf.service.SysNoticeService;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统公告
 *
 * <AUTHOR>
 * @create 2023-02-18
 */
@RestController
@RequestMapping("/sysNotice")
@Api(value = "系统公告", tags = "系统公告")
public class SysNoticeController {

    @Autowired
    private SysNoticeService sysNoticeService;

    @PostMapping("/findAll")
    @ApiOperation(value = "查询所有", notes = "查询所有")
    public AjaxJson findAll() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<SysNotice> appEvalList = sysNoticeService.findAll();
            ajaxJson.setData(appEvalList);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/findPageByMultiCondition")
    @ApiOperation(value = "按多条件查找分页", notes = "入参：{\"title\": \"系统升级\", \"content\": \"系统将在2024-02-20升级....\", \"startTime\": \"2024-02-15 00:00:00\", \"endTime\": \"2024-02-28 23:59:59\", \"status\": 1, \"noticeCategory\": 1}")
    public AjaxJson findPageByMultiCondition(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            Page<SysNotice> appEvalPage = sysNoticeService.findPageByMultiCondition(jsonObject);
            ajaxJson.setData(appEvalPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    /**
     * 根据用户账号获取公告
     * @param sysNotice
     * @return
     */
    @PostMapping("/getNoticeByUserNo")
    public AjaxJson getNoticeByUserNo(@RequestBody SysNotice sysNotice) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            List<SysNotice> appEvalPage = sysNoticeService.getNoticeByUserNo(sysNotice);
            ajaxJson.setData(appEvalPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存", notes = "保存")
    public AjaxJson save(@RequestBody SysNotice sysNotice) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            SysNotice sysNoticeNew = sysNoticeService.save(sysNotice);
            ajaxJson.setData(sysNoticeNew);
            ajaxJson.setMessage("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/saveAll")
    @ApiOperation(value = "批量保存", notes = "批量保存")
    public AjaxJson saveAll(@RequestBody List<SysNotice> appEvals) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            sysNoticeService.saveAll(appEvals);
            ajaxJson.setData("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "删除")
    public AjaxJson delete(@RequestBody SysNotice sysNotice) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            sysNoticeService.delete(sysNotice);
            ajaxJson.setData("删除成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    /**
     * 保存公告已阅读的用户
     * @param sysNotice
     * @return
     */
    @PostMapping("/saveNoticeByUser")
    public AjaxJson saveNoticeByUser(@RequestBody SysNotice sysNotice) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            sysNoticeService.saveNoticeByUser(sysNotice);
            ajaxJson.setMessage("保存成功");
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }

    /**
     * 分页查询所有历史公告
     * @param sysNotice
     * @return
     */
    @PostMapping("/getNoticeByPage")
    public AjaxJson getNoticeByPage(@RequestBody SysNotice sysNotice) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            IPage<SysNotice> sysNoticeIPage = sysNoticeService.getNoticeByPage(sysNotice);
            ajaxJson.setData(sysNoticeIPage);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setSuccess(false);
            ajaxJson.setMessage(e.toString());
        }
        return ajaxJson;
    }
}
