package iet.ustb.sf.service.impl;

import iet.ustb.sf.mapper.DictionaryDtlMapper;
import iet.ustb.sf.vo.CustomExceptionVo;
import iet.ustb.sf.vo.domain.BasicDataConfig;
import iet.ustb.sf.vo.domain.DictionaryDtl;
import iet.ustb.sf.service.BasicDataConfigService;
import iet.ustb.sf.service.DictionaryDtlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * Created by t<PERSON><PERSON><PERSON><PERSON><PERSON> on 16/6/4.
 */
@Service
public class BasicDataConfigServiceImpl implements BasicDataConfigService {

//    @Autowired
//    private DictionaryDtlService dictionaryDtlService;

    @Autowired
    private DictionaryDtlMapper dictionaryDtlMapper;


    @Override
    public BasicDataConfig findBDCbyType(String type) {
        BasicDataConfig  basicDataConfig =new BasicDataConfig();
//        String SERVICE_STR2 = "[";
//        List<DictionaryDtl> list = dictionaryDtlService.findByDictId("e38d0f03-3755-42ba-a17a-d078f01cfab3");
//        for (DictionaryDtl di : list) {
//            SERVICE_STR2 = SERVICE_STR2+ ","+di.getValue()   ;
//        }
//        SERVICE_STR2 = SERVICE_STR2 + "]";
//        basicDataConfig.setContent(SERVICE_STR2.replaceFirst(",", ""));
//        basicDataConfig.setFlag("json");
//        basicDataConfig.setName("服务地址配置");
//        basicDataConfig.setType(type);
        return basicDataConfig;
    }

    /**
     * 查询数据字典信息
     * @param basicDataConfig
     * @return
     */
    @Override
    public List<DictionaryDtl> getBasicDataInfo(BasicDataConfig basicDataConfig) {
        List<DictionaryDtl> list = dictionaryDtlMapper.getBasicDataInfo(basicDataConfig);
        return list;
    }
}
