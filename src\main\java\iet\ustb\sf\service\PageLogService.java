package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.PageLog;
import iet.ustb.sf.vo.eo.AccessNumEO;
import iet.ustb.sf.vo.eo.PageAccessNumEO;
import iet.ustb.sf.vo.eo.UserAccessNumEO;
import iet.ustb.sf.vo.eo.UserDetAccessNumEO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

public interface PageLogService {

    /**
     * 查找所有
     *
     * @return {@link List }<{@link PageLog }>
     * <AUTHOR>
     * @create 2022-09-21
     */
    List<PageLog> findAll();

    /**
     * 按多条件查找全部
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link PageLog }>
     * <AUTHOR>
     * @create 2022-11-02
     */
    List<PageLog> findAllByMultiCondition(JSONObject jsonObject);

    /**
     * 按多条件查找
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link PageLog }>
     * <AUTHOR>
     * @create 2022-09-21
     */
    Page<PageLog> findByMultiCondition(JSONObject jsonObject);

    /**
     * 按登录时间查找不同组织/人员/应用访问次数
     *
     * @param jsonObj json对象
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-03
     */
    List<AccessNumEO> findAccessNumByLoginTime(JSONObject jsonObj) throws Exception;

    /**
     * 导出用户访问excel
     *
     * @param jsonObject json对象
     * @param response   回答
     * <AUTHOR>
     * @create 2022-11-08
     * @return
     */
    void exportUserExcel(JSONObject jsonObject, HttpServletResponse response) throws Exception;

    /**
     * 导出用户详细访问excel
     *
     * @param jsonObject json对象
     * @param response   回答
     * <AUTHOR>
     * @create 2023-04-08
     */
    void exportUserDetExcel(JSONObject jsonObject, HttpServletResponse response) throws Exception;

    /**
     * 导出页面访问excel
     *
     * @param jsonObject json对象
     * @param response   回答
     * <AUTHOR>
     * @create 2023-04-07
     */
    void exportPageExcel(JSONObject jsonObject, HttpServletResponse response) throws Exception;

    /**
     * 导出word
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-08
     */
    JSONObject exportWord(JSONObject jsonObject) throws ParseException;

    /**
     * 保存
     *
     * @param pageLog 页面日志
     * @param request
     * <AUTHOR>
     * @create 2022-09-21
     */
    void save(PageLog pageLog, HttpServletRequest request);

    /**
     * 查找用户页面访问量列表
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @create 2023-03-24
     */
    Page<UserAccessNumEO> findUserPageAccessList(JSONObject jsonObject);

    /**
     * 查找用户页面详细访问列表
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link UserDetAccessNumEO }>
     * <AUTHOR>
     * @create 2023-04-08
     */
    List<UserDetAccessNumEO> findUserDetPageAccessList(JSONObject jsonObject);

    /**
     * 按用户编号查找页面访问列表
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-03-27
     */
    Page<Map<String, Object>> findPageAccessListByUserNo(JSONObject jsonObject);

    /**
     * 查找每个页面访问列表
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link PageAccessNumEO }>
     * <AUTHOR>
     * @create 2023-04-07
     */
    Page<PageAccessNumEO> findEachPageAccessList(JSONObject jsonObject);

    /**
     * 获取页面访问量看板
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2023-03-24
     */
    JSONObject getPageLogLookBoard(JSONObject jsonObject);

    /**
     * 按组织获取页面访问量看板
     *
     * @param jsonObject json对象
     * @return {@link JSONObject }
     * <AUTHOR>
     * @create 2023-03-24
     */
    JSONObject getPageLogLookBoardByOrg(JSONObject jsonObject);

    /**
     * 获取领导访问记录排行
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-08-09
     */
    Page<Map<String, Object>> getLeaderAccess(JSONObject jsonObject);

    /**
     * 一炼钢厂设备看板访问排行
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-12-04
     */
    List<Map<String, Object>> getFirstSteelMakingAccess(JSONObject jsonObject);

    /**
     * 按资源id查找用户页面访问列表
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-03-27
     */
    Page<Map<String, Object>> findUserPageAccessListByResourceId(JSONObject jsonObject);

    /**
     * 获取owner主档
     *
     * @param json json语言
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-04-06
     */
    List<Map<String, Object>> getMainHandleUser(JSONObject json);

    /**
     * 获取owner明细档
     *
     * @param jsonObject json对象
     * @return {@link Page }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @create 2023-04-06
     */
    Page<Map<String, Object>> getDetailHandleUser(JSONObject jsonObject);

    List<AccessNumEO> findGroupAppByLoginTime(JSONObject jsonObject);

    /**
     * 导出excel
     *
     * @param jsonObject json对象
     * @param response   回答
     * <AUTHOR>
     * @create 2022-11-08
     * @return
     */
    void exportExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException;

    /**
     * 导出领导和单位访问统计
     *
     * @param jsonObject json对象
     * @param response   回答
     * <AUTHOR>
     * @create 2023-08-25
     */
    void exportLeaderAndOrgExcel(JSONObject jsonObject, HttpServletResponse response) throws IOException;

    /**
     * 导出word
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2022-11-08
     */
    JSONObject exportWord2(JSONObject jsonObject);

    /**
     * 按登录时间查找不同组织/人员/应用访问次数
     *
     * @param jsonObject json对象
     * @return {@link List }<{@link AccessNumEO }>
     * <AUTHOR>
     * @create 2023-11-22
     */
    List<AccessNumEO> findAccessNumByLoginTime2(JSONObject jsonObject);
}
