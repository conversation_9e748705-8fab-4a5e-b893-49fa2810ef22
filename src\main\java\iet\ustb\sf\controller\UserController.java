package iet.ustb.sf.controller;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.User;
import iet.ustb.sf.service.UserService;
import iet.ustb.sf.utils.UserThreadUtil;
import iet.ustb.sf.vo.AjaxJson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.controller
 * @title: DuUserController
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/1010:50
 */
@RestController
@RequestMapping("/user")
@Api(value = "人员管理", tags = "人员管理")
public class UserController {


    @Autowired
    UserService userService;

    //新增
    @ResponseBody
    @PostMapping("/doCreateUser")
    @ApiOperation(value = "新增", notes = "新增,{\n" +
            "            \"academicDeg\": \"10\",\n" +
            "            \"birthDay\": \"1990/8/8 0:00:00\",\n" +
            "            \"cerType\": \"A\",\n" +
            "            \"createDateTime\": \"2022-06-15 09:00:00\",\n" +
            "            \"createUserNo\": \"admin\",\n" +
            "            \"createdBy\": \"admin\",\n" +
            "            \"createdDate\": null,\n" +
            "            \"email\": \"\",\n" +
            "            \"empCategory\": \"3\",\n" +
            "            \"folk\": \"\",\n" +
            "            \"id\": \"000068d3-6417-4db9-80aa-e21333a52462\",\n" +
            "            \"idNum\": \"321324199008084274\",\n" +
            "            \"jobNo\": \"\",\n" +
            "            \"lastModifiedBy\": null,\n" +
            "            \"lastModifiedDate\": null,\n" +
            "            \"loginId\": \"021179\",\n" +
            "            \"mobPhone\": \"13218890502\",\n" +
            "            \"nationality\": \"\",\n" +
            "            \"new\": false,\n" +
            "            \"operStus\": \"U\",\n" +
            "            \"orgCode\": \"30cda270-7b78-4e74-a228-b35d9e126320\",\n" +
            "            \"payCompId\": \"\",\n" +
            "            \"politicsName\": \"\",\n" +
            "            \"remarks\": \"\",\n" +
            "            \"repeatField\": \"321324199008084274许连军\",\n" +
            "            \"sex\": \"1\",\n" +
            "            \"status\": \"0\",\n" +
            "            \"tel\": \"\",\n" +
            "            \"userName\": \"许连军\",\n" +
            "            \"userNo\": \"021179\",\n" +
            "            \"wechatNo\": \"\",\n" +
            "            \"workStatus\": \"\"\n" +
            "        }", produces = "application/json")
    public AjaxJson doCreateUser(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(userService.doCreateUser(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    //修改
    @ResponseBody
    @PostMapping("/doUpdaterUser")
    @ApiOperation(value = "修改", notes = "修改,{\n" +
            "            \"academicDeg\": \"10\",\n" +
            "            \"birthDay\": \"1990/8/8 0:00:00\",\n" +
            "            \"cerType\": \"A\",\n" +
            "            \"createDateTime\": \"2022-06-15 09:00:00\",\n" +
            "            \"createUserNo\": \"admin\",\n" +
            "            \"createdBy\": \"admin\",\n" +
            "            \"createdDate\": null,\n" +
            "            \"email\": \"\",\n" +
            "            \"empCategory\": \"3\",\n" +
            "            \"folk\": \"\",\n" +
            "            \"id\": \"000068d3-6417-4db9-80aa-e21333a52462\",\n" +
            "            \"idNum\": \"321324199008084274\",\n" +
            "            \"jobNo\": \"\",\n" +
            "            \"lastModifiedBy\": null,\n" +
            "            \"lastModifiedDate\": null,\n" +
            "            \"loginId\": \"021179\",\n" +
            "            \"mobPhone\": \"13218890502\",\n" +
            "            \"nationality\": \"\",\n" +
            "            \"new\": false,\n" +
            "            \"operStus\": \"U\",\n" +
            "            \"orgCode\": \"30cda270-7b78-4e74-a228-b35d9e126320\",\n" +
            "            \"payCompId\": \"\",\n" +
            "            \"politicsName\": \"\",\n" +
            "            \"remarks\": \"\",\n" +
            "            \"repeatField\": \"321324199008084274许连军\",\n" +
            "            \"sex\": \"1\",\n" +
            "            \"status\": \"0\",\n" +
            "            \"tel\": \"\",\n" +
            "            \"userName\": \"许连军\",\n" +
            "            \"userNo\": \"021179\",\n" +
            "            \"wechatNo\": \"\",\n" +
            "            \"workStatus\": \"\"\n" +
            "        }", produces = "application/json")
    public AjaxJson doUpdaterUser(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(userService.doUpdaterUser(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    //删除
    @ResponseBody
    @PostMapping("/doDeleteUser")
    @ApiOperation(value = "删除(逻辑删除)", notes = "删除(逻辑删除),{\"id\":\"000068d3-6417-4db9-80aa-e21333a52462\"}", produces = "application/json")
    public AjaxJson doDeleteUser(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            userService.doDeleteUser(jsonObject);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    //查询
    @ResponseBody
    @PostMapping("/findAllUser")
    @ApiOperation(value = "获取所有用户", notes = "获取所有用户,{\n" +
            "    \"pageSize\":10,\n" +
            "    \"pageIndex\":1,\n" +
            "    \"orgCode\":[\"X42050011\" , \"X63200006\"],\n" +
            "    \"userNo\":\"\",\n" +
            "    \"userName\":\"\",\n" +
            "    \"sex\":\"\",\n" +
            "    \"empCategory\":\"\"\n" +
            "}", produces = "application/json")
    public AjaxJson findAllUser(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(userService.findAllUser(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/findOneUserByID")
    @ApiOperation(value = "查询单个用户ByID", notes = "查询单个用户ByID,{\"id\":\"000068d3-6417-4db9-80aa-e21333a52462\"}", produces = "application/json")
    public AjaxJson findOneUserByID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(userService.findOneUserByID(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/findOneUserByUserNo")
    @ApiOperation(value = "查询单个用户ByUserNo", notes = "查询单个用户ByUserNo,{\"userNo\":\"021179\"}", produces = "application/json")
    public AjaxJson findOneUserByUserNo(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(userService.findOneUserByUserNo(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/filterUserSelected")
    @ApiOperation(value = "查询单个用户", notes = "查询单个用户,{\"roleID\":\"000068d3-6417-4db9-80aa-e21333a52462\"}", produces = "application/json")
    public AjaxJson filterUserSelected(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(userService.filterUserSelected(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/relateRole")
    @ApiOperation(value = "关联角色", notes = "关联角色,{\n" +
            "    \"id\": \"11111152-9ac6-48e9-a965-0c220ac92148\",\n" +
            "    \"addIDs\": [\n" +
            "        \"2066b7cf-6230-4ca4-a14a-1a02dd9fad20\"\n" +
            "    ],\n" +
            "    \"removeIDs\": [\n" +
            "        \"384a8727-4a6f-4106-af3e-f30c0458c611\"\n" +
            "    ]\n" +
            "}", produces = "application/json")
    public AjaxJson relateRole(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(userService.relateRole(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/findUserAndOrgByUserNo")
    @ApiOperation(value = "根据用户编号查询用户和组织详细信息", notes = "{\"userNo\":\"018930\"}", produces = "application/json")
    public AjaxJson findUserAndOrgByUserNo(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(userService.findUserAndOrgByUserNo(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/findUsersByOrgCode")
    @ApiOperation(value = "根据组织编号查询该组织下所有用户信息", notes = "{\"orgCode\":\"X32090000\"}", produces = "application/json")
    public AjaxJson findUsersByOrgCode(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(userService.findUsersByOrgCode(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }


    @ResponseBody
    @PostMapping("/findUserInSameOrgAndRole")
    @ApiOperation(value = "根据当前登录用户，寻找该组织与角色下所有用户", notes = "{\"userNo\":\"007132\"}", produces = "application/json")
    public AjaxJson findUserInSameOrgAndRole(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(userService.findUserInSameOrgAndRole(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.getMessage());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/updatePwd")
    @ApiOperation(value = "更新密码", notes = "入参：{\"userNo\":\"023958\",\"password\":\"111111\"}", produces = "application/json")
    public AjaxJson updatePwd(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();

        try {
            User user = userService.findOneUserByUserNo(jsonObject);
            Assert.notNull(user, "用户不存在");

            String password = jsonObject.getString("password");
            user.setPassword(password);
            user.setIsModifiedPwd("Y");// 已修改密码

            ajaxJson.setData(userService.doSaveUser(user));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.toString());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/getCurrUser")
    @ApiOperation(value = "获取当前用户", notes = "")
    public AjaxJson getCurrUser() {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            User user = UserThreadUtil.getUser();
            ajaxJson.setData(user);
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.toString());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }

    @ResponseBody
    @PostMapping("/findUserListByRoleIDs")
    @ApiOperation(value = "根据角色ID获取用户信息(对外)", notes = "" +
            "{\n" +
            "    \"roleIDs\": [\n" +
            "        \"000c90a7-8092-49a5-a032-e457949f7c8f\",\n" +
            "        \"008f3e5d-53c4-435c-bf7b-169bb5620a7c\"\n" +
            "    ]\n" +
            "}")
    public AjaxJson findUserListByRoleID(@RequestBody JSONObject jsonObject) {
        AjaxJson ajaxJson = new AjaxJson();
        try {
            ajaxJson.setData(userService.findUserListByRoleIDs(jsonObject));
            ajaxJson.setSuccess(true);
        } catch (Exception e) {
            ajaxJson.setMessage(e.toString());
            ajaxJson.setSuccess(false);
        }
        return ajaxJson;
    }
}
