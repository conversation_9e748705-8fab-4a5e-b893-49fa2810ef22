package iet.ustb.sf.vo.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;

/**
 * 进度管理
 *
 * <AUTHOR>
 * @create 2023-01-03
 * @see BaseEntity
 */
@Data
@Entity
@Table(name = "PROGRESS_MGT")
@ApiModel(value = "进度管理")
public class ProgressMgt extends BaseEntity {

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    private Integer module;
    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private String parentId;
    /**
     * 页面开发
     */
    @ApiModelProperty(value = "页面开发")
    private Integer pageDev;
    /**
     * 后端开发
     */
    @ApiModelProperty(value = "后端开发")
    private Integer backendDev;
    /**
     * 数据接入
     */
    @ApiModelProperty(value = "数据接入")
    private Integer dataAccess;
    /**
     * 功能上线
     */
    @ApiModelProperty(value = "功能上线")
    private Integer functionOnline;
    /**
     * 投入运行
     */
    @ApiModelProperty(value = "投入运行")
    private Integer putIntoOperation;
    /**
     * 交付验收
     */
    @ApiModelProperty(value = "交付验收")
    private Integer deliveryAcceptance;
    /**
     * 综合评分
     */
    @ApiModelProperty(value = "综合评分")
    private Integer comprehensiveScore;
    /**
     * 甘特数据
     */
    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "ganttData", columnDefinition = "BLOB")
    @ApiModelProperty(value = "甘特数据")
    private String ganttData;

}
