//package iet.ustb.sf.schedule;
//
//import com.alibaba.fastjson.JSONObject;
//import iet.ustb.sf.dao.BasicDataConfigDao;
//import iet.ustb.sf.domain.BasicDataConfig;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//import org.springframework.web.client.RestTemplate;
//
///**
// * @Author: Dr.Monster
// * @Title: BasicDataConfigTask
// * @Date: 23/12/27 09:0434
// * @Slogan: The never-compromising angry youth
// * @Remark:$
// */
//
//@Component
//@EnableScheduling
//@SpringBootTest
//public class BasicDataConfigTask {
//
//
//    @Autowired
//    private BasicDataConfigDao basicDataConfigDao;
///login/loginPageAuthVerify
//    @Autowired
//    RestTemplate restTemplate;
//
//    @Scheduled(cron = "0 30 00 * * ?")
//    public void syncMaster() {
//        String url = "http://172.25.63.116:9700/basicDataConfig/findBasicDataConfigByType.form";
//        JSONObject param = new JSONObject();
//        param.put("type" , "serviceInfo");
//        BasicDataConfig result = restTemplate.postForEntity(url, param, BasicDataConfig.class).getBody();
//        basicDataConfigDao.save(result);
//    }
//
//
//}
