package iet.ustb.sf.vo.eo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户访问次数排行
 *
 * <AUTHOR>
 * @create 2022-11-03
 * @see Serializable
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAccessNumEO implements Serializable {

    /**
     * 工号
     */
    @Excel(name = "工号")
    private String userNo;
    /**
     * 名称
     */
    @Excel(name = "员工名称")
    private String userName;
    /**
     * 部门编码
     */
    @Excel(name = "部门编号")
    private String orgCode;
    /**
     * 部门名称
     */
    @Excel(name = "部门名称", width = 50)
    private String orgAllName;
    /**
     * 访问次数
     */
    @Excel(name = "访问次数")
    private Long accessNum;
}
