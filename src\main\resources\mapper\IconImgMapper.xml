<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="iet.ustb.sf.mapper.IconImgMapper">

    <resultMap type="iet.ustb.sf.vo.domain.IconImg" id="IconImgMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createDateTime" column="createdatetime" jdbcType="TIMESTAMP"/>
        <result property="createUserNo" column="createuserno" jdbcType="VARCHAR"/>
        <result property="updateDateTime" column="updatedatetime" jdbcType="TIMESTAMP"/>
        <result property="updateUserNo" column="updateuserno" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="resource" column="resource" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="iconType" column="icontype" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into resource.icon_img(createdatetime, createuserno, updatedatetime, updateuserno, name, resource, icontype, file_name)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.createDateTime}, #{entity.createUserNo}, #{entity.updateDateTime}, #{entity.updateUserNo}, #{entity.name}, #{entity.resource}, #{entity.icontype}, #{entity.fileName})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into resource.icon_img(createdatetime, createuserno, updatedatetime, updateuserno, name, resource, icontype, file_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.createDateTime}, #{entity.createUserNo}, #{entity.updateDateTime}, #{entity.updateUserNo}, #{entity.name}, #{entity.resource}, #{entity.iconType}, #{entity.fileName})
        </foreach>
        on duplicate key update
createdatetime = values(createdatetime) , createuserno = values(createuserno) , updatedatetime = values(updatedatetime) , updateuserno = values(updateuserno) , name = values(name) , resource = values(resource) , icontype = values(icontype)  file_name = values(file_name)   </insert>

    <select id="getIconImgInfo" resultType="iet.ustb.sf.vo.domain.IconImg">
        select
            *
        from resource.icon_img
        <where>
            <if test="iconImg.id != null and iconImg.id != ''">
                and id = #{iconImg.id}
            </if>
            <if test="iconImg.iconType != null">
                and icontype = #{iconImg.iconType}
            </if>
            <if test="iconImg.fileName != null and iconImg.fileName != ''">
                and file_name like CONCAT('%', #{iconImg.fileName},'%')
            </if>

        </where>
        order by createdatetime desc

    </select>
</mapper>

