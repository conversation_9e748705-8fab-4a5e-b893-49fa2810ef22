package iet.ustb.sf.controller;

import iet.ustb.sf.service.MasterDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Calendar;

@RestController
@RequestMapping("/masterData")
@Api(value = "主数据管理", tags = "主数据管理")
public class MasterDataController {

    @Autowired
    private MasterDataService masterDataService;

    @PostMapping("/syncMaster")
    private void syncMaster(boolean isNowSyncFlag) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

        Calendar calendar = Calendar.getInstance();// 获取当前日期
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        calendar.set(Calendar.MINUTE, 00);
        calendar.set(Calendar.SECOND, 00);

        // 当请求中isNowSyncFlag参数未传值,默认为false
        // 立即同步：同步当天；非立即同步：同步前一天
        if (!isNowSyncFlag) {
            calendar.add(Calendar.DAY_OF_MONTH, -1);
        }
        String beginDate = sdf.format(calendar.getTime());

        // 一天的结束时间 23:59:59
        calendar.set(Calendar.HOUR_OF_DAY,23);
        calendar.set(Calendar.MINUTE,59);
        calendar.set(Calendar.SECOND,59);
        calendar.set(Calendar.MILLISECOND,999);
        String endDate = sdf.format(calendar.getTime());

        masterDataService.syncMaster(beginDate, endDate);
    }

    @PostMapping("/syncMasterByPeriod")
    @ApiOperation(value = "查询次级指标", notes = "入参：{\"beginDate\":\"2022/08/26 00:00:00\"}")
    public void syncMasterByPeriod(String beginDate, String endDate) {
        masterDataService.syncMaster(beginDate, endDate);
    }

}
