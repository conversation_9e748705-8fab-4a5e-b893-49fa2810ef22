package iet.ustb.sf.utils;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.channels.FileChannel;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.devicePrecision.utils
 * @title: FileUtil
 * @projectName iet-iom-service
 * @description: TODO
 * @date 2022/3/2916:33
 */
public class FileUtil {
    /**
     * resource文件下的文件
     */
    public static File getFileByResourceName(String fileName, String fileType) {
        Resource resource = new ClassPathResource(fileName + fileType);

        String filePath = FileUtil.class.getClassLoader().getResource(fileName + fileType).getFile();
        System.out.println("Path:" + filePath);

        String jarPath = System.getProperty("java.class.path");
        System.out.println("JarPath:" + jarPath);

        String pathPrefix = "file:/iet-iom-service-0.0.1.jar!/BOOT-INF/classes!/";
        File file = new File(pathPrefix + fileName + fileType);
        try {
            InputStream inputStream = resource.getInputStream();
            inputStreamToFile(inputStream, file);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file;
    }


    /**
     * 第一个是jar包的位置，第二个是要读取的文件在jar包内的路径
     *
     * @return
     */
    public static JarEntry getJarFile(String jarPath, String fileInJarPath) {
        JarFile jarFile = null;
        try {
            jarFile = new JarFile(jarPath);
            JarEntry jarEntry = jarFile.getJarEntry(fileInJarPath);
            return jarEntry;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public static void main(String[] args) {
        String fileName = "标准维护导入模板";
        String fileType = ".xlsx";
        File file = getFileByResourceName(fileName, fileType);
        copyFile(file);
    }

    /**
     * 拷贝文件
     *
     * @param oldFile
     * @return
     */
    public static File copyFile(File oldFile) {
        File newFile = new File(oldFile.getPath().replace(".", "_" + System.currentTimeMillis() + "."));
        try {
            if (newFile.createNewFile()) {
                FileChannel inputChannel = null;
                FileChannel outputChannel = null;
                try {
                    inputChannel = new FileInputStream(oldFile).getChannel();
                    outputChannel = new FileOutputStream(newFile).getChannel();
                    outputChannel.transferFrom(inputChannel, 0, inputChannel.size());
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                } finally {
                    try {
                        inputChannel.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    try {
                        outputChannel.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return newFile;
    }


    public static void deleteFileLater(File file) {
        try {
            ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(6);
            executor.schedule(new Runnable() {
                @Override
                public void run() {
                    delteTempFile(file);
                    System.out.println("file deleted......");
                }
            }, 30, TimeUnit.SECONDS);
        } catch (Exception e) {

        }
    }

    /**
     * MultipartFile 转 File
     *
     * @param file
     * @throws Exception
     */
    public static File multipartFileToFile(MultipartFile file) {

        File toFile = null;
        if (file.equals("") || file.getSize() <= 0) {
            file = null;
        } else {
            InputStream ins = null;
            try {
                ins = file.getInputStream();
            } catch (IOException e) {
                e.printStackTrace();
            }
            toFile = new File(file.getOriginalFilename());
            inputStreamToFile(ins, toFile);
            try {
                ins.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return toFile;
    }

    //获取流文件
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除本地临时文件
     *
     * @param file
     */
    public static void delteTempFile(File file) {
        if (file != null) {
            File del = new File(file.toURI());
            del.delete();
        }
    }


    private static String readFile(String fileName) {
        String ret = "";
        try {
            ret = readJarFile(fileName);
        } catch (IOException e) {
        }
        return ret;
    }

    /**
     * 读取jar包中的资源文件
     *
     * @param fileName 文件名
     * @return 文件内容
     * @throws IOException 读取错误
     */
    private static String readJarFile(String fileName) throws IOException {
        BufferedReader in = new BufferedReader(
                new InputStreamReader(FileUtil.class.getClassLoader().getResourceAsStream(fileName)));
        StringBuilder buffer = new StringBuilder();
        String line;
        while ((line = in.readLine()) != null) {
            buffer.append(line);
        }
        return buffer.toString();
    }


    public static File writeFileToJar(String fileName, String fileType) {
        return null;
    }

}