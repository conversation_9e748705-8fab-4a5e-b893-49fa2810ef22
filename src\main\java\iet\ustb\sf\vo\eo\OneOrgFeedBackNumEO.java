package iet.ustb.sf.vo.eo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 厂处问题反馈次数
 *
 * <AUTHOR>
 * @create 2022-11-03
 * @see Serializable
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OneOrgFeedBackNumEO implements Serializable {

    /**
     * 厂处组织编号
     */
    private String oneOrgCode;
    /**
     * 姓名
     */
    private String oneOrgName;
    /**
     * 反馈次数
     */
    private Integer num;
}
