package iet.ustb.sf.service;

import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.vo.domain.WarningEvent;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: Dr.Monster
 * @Slogan: The never-compromising angry youth.
 * @PackageName: iet.ustb.sf.service
 * @Date: 2022/08/10/14:17
 * @Description:
 */
public interface WarningEventService {
    void saveAlertInfo(JSONObject jsonObject);

    void updateAlertInfo(JSONObject jsonObject);

    List<WarningEvent> findAlertsByServiceName(JSONObject jsonObject);

    List<WarningEvent> findAlertsByAlertTypeAndServiceName(JSONObject jsonObject);

    List<WarningEvent> findAlertsByAlertLevelAndServiceName(JSONObject jsonObject);

    Page<WarningEvent> findAlertInfoByMultipleChoices(JSONObject jsonObject);

    void deleteAlert();

    public void saveOfflineMessage(JSONObject jsonObject);

    public List<WarningEvent> getOfflineWaringEvent(JSONObject jsonObject);

    Map<String , Object> getUndoneRateByUserID(JSONObject jsonObject);

    List<Map<String , String>> findModuleInfoList();

    List<String> findAreaNameList();

    List<String> findDeviceNameList();

    List<String> findPointNameList();
}
