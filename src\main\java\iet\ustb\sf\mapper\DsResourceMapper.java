package iet.ustb.sf.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import iet.ustb.sf.vo.domain.Resource;
import org.apache.ibatis.annotations.Param;

/**
 * (DsResource)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-07 09:25:19
 */
public interface DsResourceMapper extends BaseMapper<Resource> {

    /**
    * 批量新增数据（MyBatis原生foreach方法）
    *
    * @param entities List<DsResource> 实例对象列表
    * @return 影响行数
    */
    int insertBatch(@Param("entities") List<Resource> entities);

    /**
    * 批量新增或按主键更新数据（MyBatis原生foreach方法）
    *
    * @param entities List<DsResource> 实例对象列表
    * @return 影响行数
    * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
    */
    int insertOrUpdateBatch(@Param("entities") List<Resource> entities);

    /**
     * 根据菜单信息查询相关所有菜单数据
     * @param resource
     * @return
     */
    List<Resource> getResourceInfo(Resource resource);

    /**
     * 获取当前最大一级菜单编号
     * @return
     */
    String getMaxTopLevelCode();

    /**
     * 获取当前最大子菜单编号
     * @param parentCode
     * @return
     */
    String getMaxSubLevelCode(String parentCode);

    /**
     * 根据用户信息获取对应菜单信息
     * @param resource
     * @return
     */
    List<Resource> findResource(Resource resource);

    /**
     * 根据角色ID查询所有授权的菜单信息
     * @param roleID
     * @return
     */
    List<Resource> filterResourceSelected(@Param("roleID") String roleID);

    /**
     *
     * @param parentId
     * @return
     */
    List<Resource> getChildResources(@Param("parentId") String parentId);

}

