package iet.ustb.sf.vo.domain;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 用户岗级中间表
 */
@Data
@Entity
@Table(name = "DU_USER_POST_LEVEL")
public class UserPostLevel extends BaseEntity {

    /**
     * 用户id
     */
    @Column(length = 64, nullable = false)
    private String userId;

    /**
     * 组织编号
     */
    @Column(length = 64, nullable = false)
    private String orgCode;

    /**
     * 默认组织编号
     */
    @Column(length = 64, nullable = false)
    private String defaultOrgCode;

    /**
     * 岗级编号
     */
    @Column(length = 64, nullable = false)
    private String postLevelCode;

    /**
     * 状态：0-启用,1-禁用
     */
    @Column(length = 64, nullable = false)
    private String status;

    /**
     * 操作标识：N 新增,U修改,D删除
     */
    @Column(length = 64)
    private String operStus;

    /**
     * 判重项目值域
     */
    private String repeatField;

}
