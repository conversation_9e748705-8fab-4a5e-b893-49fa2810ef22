package iet.ustb.sf.config;

import io.minio.MinioClient;
import io.minio.ObjectStat;
import io.minio.PutObjectOptions;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Component
public class MinioConfig implements InitializingBean {

    @Autowired
    private NacosConfig nacosConfig;

    private MinioClient minioClient;

    @Override
    public void afterPropertiesSet() throws Exception {
        Assert.hasText(nacosConfig.getMinioUrl(), "Minio url 为空");
        Assert.hasText(nacosConfig.getMinioAccessKey(), "Minio accessKey为空");
        Assert.hasText(nacosConfig.getMinioSecretKey(), "Minio secretKey为空");
        this.minioClient = new MinioClient(nacosConfig.getMinioHost(), nacosConfig.getMinioAccessKey(), nacosConfig.getMinioSecretKey());
    }

    /**
     * 文件上传
     * @param multipartFile
     * @return
     * @throws Exception
     */
    public String putObject(MultipartFile multipartFile) throws Exception {
        // bucket 不存在，创建
        if (!minioClient.bucketExists(nacosConfig.getMinioBucket())) {
            minioClient.makeBucket(nacosConfig.getMinioBucket());
        }
        try (InputStream inputStream = multipartFile.getInputStream()) {
            // 上传文件的名称
            String fileName = multipartFile.getOriginalFilename();
            String[] split = fileName.split("\\.");
            if (split.length > 1) {
                fileName = split[0] + "_" + System.currentTimeMillis() + "." + split[1];
            } else {
                fileName = fileName + System.currentTimeMillis();
            }
            // PutObjectOptions，上传配置(文件大小，内存中文件分片大小)
            PutObjectOptions putObjectOptions = new PutObjectOptions(multipartFile.getSize(), PutObjectOptions.MIN_MULTIPART_SIZE);
            // 文件的ContentType
            putObjectOptions.setContentType(multipartFile.getContentType());
            minioClient.putObject(nacosConfig.getMinioBucket(), fileName, inputStream, putObjectOptions);
            // 返回访问路径
            return nacosConfig.getMinioUrl() + UriUtils.encode(fileName, StandardCharsets.UTF_8);
        }
    }

    /**
     * 文件下载
     * @param fileName
     * @param response
     */
    public void download(String fileName, HttpServletResponse response){
        // 从链接中得到文件名
        InputStream inputStream;
        try {
            MinioClient minioClient = new MinioClient(nacosConfig.getMinioHost(), nacosConfig.getMinioAccessKey(), nacosConfig.getMinioSecretKey());
            ObjectStat stat = minioClient.statObject(nacosConfig.getMinioBucket(), fileName);
            inputStream = minioClient.getObject(nacosConfig.getMinioBucket(), fileName);
            response.setContentType(stat.contentType());
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            IOUtils.copy(inputStream, response.getOutputStream());
            inputStream.close();
        } catch (Exception e){
            e.printStackTrace();
            System.out.println("有异常：" + e);
        }
    }
    /**
     * 检查存储桶是否存在
     * @param bucketName
     * @return
     * @throws Exception
     */
    public boolean bucketExists(String bucketName) throws Exception {
        boolean flag = minioClient.bucketExists(bucketName);
        if (flag) {
            return true;
        }
        return false;
    }

    /**
     * 文件访问路径
     * @param bucketName 存储桶名称
     * @param objectName 存储桶里的对象名称
     * @return
     * @throws Exception
     */
    public String getObjectUrl(String bucketName, String objectName) throws Exception {
        boolean flag = bucketExists(bucketName);
        String url = "";
        if (flag) {
            url = minioClient.getObjectUrl(bucketName, objectName);
        }
        return url;
    }

    /**
     * 文件删除
     * @param fileName 文件名
     * @throws Exception
     */
    public void deleteObject(String fileName) throws Exception {
        try {
            // 删除文件
            minioClient.removeObject(nacosConfig.getMinioBucket(), nacosConfig.getMinioBucket() + "/" + fileName);
            System.out.println("文件 " + fileName + " 删除成功");
        } catch (Exception e) {
            System.out.println("删除文件时出错：" + e);
            throw new Exception("删除文件失败", e);
        }
    }
}
