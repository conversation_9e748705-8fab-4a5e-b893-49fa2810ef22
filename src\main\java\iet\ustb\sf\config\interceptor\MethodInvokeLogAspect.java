package iet.ustb.sf.config.interceptor;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;


import iet.ustb.sf.config.interceptor.annotation.AutoLog;
import iet.ustb.sf.service.impl.PortMonitoringInfoServiceImpl;
import iet.ustb.sf.utils.DateUtils;
import iet.ustb.sf.utils.JwtUtil;
import iet.ustb.sf.utils.RedisUtil;
import iet.ustb.sf.vo.PortMonitoringInfo;
import iet.ustb.sf.utils.constant.EnumConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 方法调用日志监控类
 * <AUTHOR>
 */
@Component
@Aspect
public class MethodInvokeLogAspect {

    private final  static  Logger LOGGER = LoggerFactory.getLogger(MethodInvokeLogAspect.class);
    
    @Autowired
    private PortMonitoringInfoServiceImpl portMonitoringInfoServiceImpl;

    private static RedisUtil redisUtil;

    /**
     * 1.普通 2.调用 3.被调 4.自定义
     */
    public final static Integer[] PORT_TYPE = new Integer[]{1, 2, 3, 4};

    /**
     * 1.正常 2.异常 3.超时
     */
    public final static Integer[] PORT_STATUS = new Integer[]{1, 2, 3};

    public static final Map<Integer, String> PORT_TYPE_MAP;

    public static final Map<Integer, String> PORT_STATUS_MAP;

    public static final String LOG_MESSAGE_REDIS_KEY = "log_message_redis_key";

    static {
        PORT_TYPE_MAP = new HashMap<>();
        PORT_TYPE_MAP.put(1, "普通");
        PORT_TYPE_MAP.put(2, "调用");
        PORT_TYPE_MAP.put(3, "被调");
        PORT_TYPE_MAP.put(4, "自定义");
        PORT_STATUS_MAP = new HashMap<>();
        PORT_STATUS_MAP.put(1, "正常");
        PORT_STATUS_MAP.put(2, "异常");
        PORT_STATUS_MAP.put(3, "超时");
    }

    /**
     * 整个项目切点,所有调用方法的都被织入advice（监控整个项目的异常方法，输出异常方法日志信息）
     */
    @Pointcut("execution(* iet.ustb.sf.service.impl..*.*(..))")
    public void allPointcut() {
    }

    @Pointcut("@annotation(iet.ustb.sf.config.interceptor.annotation.AutoLog)")
    public void logPointCut() {
    }


    /**
     * 所有方法执行作为切点, 捕捉这些方法异常
     * @param ex 此处将ex的类型声明为Throwable,意味着对目标方法抛出的异常不加限制
     */
    @AfterThrowing(throwing = "ex", pointcut = "allPointcut()")
    public void facadeLogExceptionHandle(JoinPoint joinPoint, Throwable ex){
        generateLog(joinPoint, null, ex, null, PORT_TYPE[0]);
    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        //响应相关的
        Object result = null;
        long startTime = System.currentTimeMillis();
        result = point.proceed();
        // 记录请求日志
        generateLog(point, result, null, Math.toIntExact(System.currentTimeMillis() - startTime), PORT_TYPE[0]);

        return result;
    }

    /**
     * 记录异常log入库
     * @param joinPoint 切点
     * @param result 响应结果集
     * @param exception 异常信息
     * @param useTime 耗时(ms)
     * @param portType 接口类别
     */
    public void generateLog(JoinPoint joinPoint, Object result, Object exception, Integer useTime, Integer portType) {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            //获取请求ip端口与请求地址
            String ipAddr = getRemoteHost(request);
            int serverPort = request.getServerPort();
            String requestUrl = request.getRequestURL().toString();

            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Method method = methodSignature.getMethod();
            //获取类名和方法名
            String className = method.getDeclaringClass().getSimpleName();
            String methodName = method.getName();

            Object[] args = joinPoint.getArgs();
            String responseDataType = methodSignature.getReturnType().getName();
            String requestParams = toJsonData(args);

            StringBuilder requestParamTypes = new StringBuilder("[");
            if (null != args) {
                for (int i = 0; i < args.length; i++) {
                    if(args.length-1 != i){
                        requestParamTypes.append(args[i].getClass()).append(", ");
                    }else {
                        requestParamTypes.append(args[i].getClass());
                    }
                }
            }
            requestParamTypes.append("]");

            ApiOperation annotation = AnnotationUtils.findAnnotation(method, ApiOperation.class);
            String methodNameCn = "";
            if (annotation != null) {
                methodNameCn = annotation.value();
            }

            String rspData = toJsonData(result);
            String errorData = toJsonData(exception);

            PortMonitoringInfo logVO = new PortMonitoringInfo();

            //设置主键
            String id = UUID.randomUUID().toString().replace("-", "").toUpperCase();
            logVO.setId(id);

            //设置请求id、请求地址、请求参数类型和请求参数
            logVO.setIp(ipAddr + ":" + serverPort);
            logVO.setPortUrl(requestUrl);
            logVO.setRequestDataType(requestParamTypes.toString());
            logVO.setRequestData(requestParams);

            //设置响应数据类型、响应数据大小和响应数据
            logVO.setResponseDataType(responseDataType);
            logVO.setResponseDataSize(String.valueOf(rspData.length()));
            logVO.setResponseData(rspData);

            //设置类名、方法名和接口中文名称
            logVO.setPortCategory(className);
            logVO.setPortMethod(methodName);
            logVO.setPortName(methodNameCn);

            //设置异常日志信息和调用的当前时间
            logVO.setErrorMessage(errorData);
            logVO.setCallTime(DateUtils.getCurrDate("yyyy-MM-dd HH:mm:ss"));

            //设置接口状态
            if(exception != null){
                //异常
                logVO.setPortStatus(PORT_STATUS[1]);
            }else {
                //设置接口耗时(ms)
                logVO.setPortTime(String.valueOf(useTime));
                if(useTime > EnumConstant.EXPIRY_TIME_MINUTE * 5){
                    //超时
                    logVO.setPortStatus(PORT_STATUS[2]);
                }else {
                    //正常
                    logVO.setPortStatus(PORT_STATUS[0]);
                }
            }
            //设置接口类别
            logVO.setPortType(portType);
            Api api = AnnotationUtils.findAnnotation(method.getDeclaringClass(), Api.class);
            if(api != null){
                String[] classFlags = api.tags();
                if(classFlags != null){
                    if(PORT_TYPE_MAP.get(PORT_TYPE[EnumConstant.ONE]).equals(classFlags[EnumConstant.ZERO])){
                        logVO.setPortType(PORT_TYPE[EnumConstant.ONE]);
                    }
                    if(PORT_TYPE_MAP.get(PORT_TYPE[EnumConstant.TWO]).equals(classFlags[EnumConstant.ZERO])){
                        logVO.setPortType(PORT_TYPE[EnumConstant.TWO]);
                    }
                }
            }
            portMonitoringInfoServiceImpl.insert(logVO);

        } catch (Exception e) {
            // do nothing
        }
    }

    /**
     * 返回数据
     *
     * @param retVal
     * @return
     */
    private String toJsonData(Object retVal) {
        if (null == retVal) {
            return "";
        }
        return JSON.toJSONString(retVal);
    }

    /**
     * 获取目标主机的ip
     *
     * @param request
     * @return
     */
    private String getRemoteHost(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || EnumConstant.UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || EnumConstant.UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || EnumConstant.UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return "0:0:0:0:0:0:0:1".equals(ip) ? "127.0.0.1" : ip;
    }
}
