package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface UserDao extends JpaSpecificationExecutor<User>, JpaRepository<User, String> {

    @Query(value = "select * from DU_USER where userNo = ?1 and status = '0' and operStus <> 'D'", nativeQuery = true)
    User findOneUserByUserNo(String userNo);

    @Query(value = "SELECT " +
            " id, " +
            " userNo, " +
            " userName, " +
            " academicDeg, " +
            " birthDay, " +
            " cerType, " +
            " email, " +
            " empCategory, " +
            " folk, " +
            " idNum, " +
            " jobNo, " +
            " loginId, " +
            " mobPhone, " +
            " nationality, " +
            " operStus, " +
            " orgCode, " +
            " payCompId, " +
            " politicsName, " +
            " remarks, " +
            " sex, " +
            " tel, " +
            " wechatNo, " +
            " workStatus, " +
            " empCategory, " +
            " `status`, " +
            " repeatField, " +
            " createDateTime, " +
            " createUserNo, " +
            " updateDateTime, " +
            " updateUserNo, " +
            " MDMZGZD_NM, " +
            " contAddress, " +
            " postCode, " +
            " postLevelCode, " +
            " regAddress, " +
            " startWorkTime, " +
            " urgent, " +
            " urgentCall, " +
            " userCode, " +
            " 1 isSelected  " +
            "FROM " +
            " du_user " +
            " JOIN ds_role_userlist ON userList_id = id  " +
            "WHERE " +
            " Role_id = ?1 UNION " +
            "SELECT " +
            " id, " +
            " userNo, " +
            " userName, " +
            " academicDeg, " +
            " birthDay, " +
            " cerType, " +
            " email, " +
            " empCategory, " +
            " folk, " +
            " idNum, " +
            " jobNo, " +
            " loginId, " +
            " mobPhone, " +
            " nationality, " +
            " operStus, " +
            " orgCode, " +
            " payCompId, " +
            " politicsName, " +
            " remarks, " +
            " sex, " +
            " tel, " +
            " wechatNo, " +
            " workStatus, " +
            " empCategory, " +
            " `status`, " +
            " repeatField, " +
            " createDateTime, " +
            " createUserNo, " +
            " updateDateTime, " +
            " updateUserNo, " +
            " MDMZGZD_NM, " +
            " contAddress, " +
            " postCode, " +
            " postLevelCode, " +
            " regAddress, " +
            " startWorkTime, " +
            " urgent, " +
            " urgentCall, " +
            " userCode, " +
            " 0 isSelected  " +
            "FROM " +
            " du_user  " +
            "WHERE " +
            " id NOT IN ( " +
            " SELECT " +
            "  id  " +
            " FROM " +
            "  du_user " +
            "  JOIN ds_role_userlist ON userList_id = id  " +
            " WHERE " +
            " Role_id = ?1  " +
            " )", nativeQuery = true)
    Page<User> filterUserSelected(String roleID, @Nullable Specification<User> spec, Pageable pageable);

    @Query(value = "select a.* from du_user a left join du_org b on a.orgCode=b.orgCode where b.orgCode=?1", nativeQuery = true)
    List<User> findUsersByOrgCode(@Nullable String orgCode);

    @Query(value = "select a.* from du_user a left join du_org b on a.orgCode=b.orgCode where b.orgCode in ?1", nativeQuery = true)
    List<User> findUsersByOrgCodes(@Nullable List<String> orgCode);

    @Query(value = "select distinct du.*" +
            " from du_user du" +
            "         join ds_role dr" +
            "         join ds_role_userlist dru" +
            "              on dr.id = dru.Role_id and du.id = dru.userList_id" +
            " where du.orgCode = (select orgCode from du_user where userNo = ?1)", nativeQuery = true)
    List<User> findUserInSameOrgAndRole(String userNo);

    /**
     * 查找所有用户
     *
     * @param orgCodeList 组织代码列表
     * @param userNo      用户编号
     * @param userName    用户名
     * @param sex         性别
     * @param empCategory emp类别
     * @return {@link Page }<{@link User }>
     * <AUTHOR>
     * @create 2022-09-24
     */
    @Query(value = "select b.orgAllName spareField, a.*"
            + " from du_user a"
            + "          left join du_org b on a.orgCode = b.orgCode"
            + " where 1=1 "
            + "   and (coalesce (?1 , null) is null or a.orgCode IN (?1))"
            + "   and if(?2 is not null && ?2 != '', a.userNo = ?2, 1=1) "
            + "   and if(?3 is not null && ?3 != '', a.userName = ?3, 1=1) "
            + "   and if(?4 is not null && ?4 != '', a.sex = ?4, 1=1) "
            + "   and if(?5 is not null && ?5 != '', a.empCategory = ?5, 1=1)",nativeQuery = true)
    Page<User> findAllUser(List<String> orgCodeList, String userNo,
                           String userName, String sex, String empCategory,
                           Pageable pageable);

    /**
     * 按用户编号查找用户组织基本信息
     *
     * @param userNos 用户
     * @return {@link List }<{@link Map }<{@link String }, {@link String }>>
     * <AUTHOR>
     * @create 2023-04-12
     */
    @Query(value = "select a.userNo, a.userName, b.orgCode, b.orgAllName"
            + " from du_user a"
            + "          left join du_org b on a.orgCode = b.orgCode"
            + " where a.userNo in ?1"
            + "   and a.status = '0'"
            + "   and a.operStus <> 'D'", nativeQuery = true)
    List<Map<String, String>> findUserOrgByUserNos(Set<String> userNos);


    @Query(value = "select * from du_user where du_user.userno in ?1", nativeQuery = true)
    List<User> findUsersByUserNos(Set<String> userNos);


}
