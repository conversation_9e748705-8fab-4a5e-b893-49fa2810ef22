package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.Resource;
import iet.ustb.sf.vo.AvailableAndIcon;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @PackageName: iet.ustb.sf.dao
 * @title: ResourceDao
 * @projectName iet-resources-service
 * @description: TODO
 * @date 2022/6/1010:47
 */
public interface ResourceDao extends JpaSpecificationExecutor<Resource>, JpaRepository<Resource, String> {

    @Query(value = "SELECT rd.*\n" +
            "FROM (SELECT * FROM ds_resource WHERE parentId IS NOT NULL) rd,\n" +
            "     (SELECT @pid \\:= ?1) pd\n" +
            "WHERE FIND_IN_SET(parentId, @pid) > 0\n" , nativeQuery = true)
    List<Resource> findResourceByParentID(String parentID);


    @Query(value = "SELECT rd.*\n" +
            "FROM (SELECT * FROM ds_resource WHERE parentId IS NOT NULL) rd,\n" +
            "     (SELECT @pid \\:= ?1) pd\n" +
            "WHERE FIND_IN_SET(parentId, @pid) > 0\n" +
            "and status = 1\n" , nativeQuery = true)
    List<Resource> findAvaResourceByParentID(String parentID);



    @Query(value = "SELECT\n" +
            "\tDATA.*\n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\t@ids AS _ids,\n" +
            "\t\t( SELECT @ids\\:=GROUP_CONCAT( id ) FROM ds_resource WHERE FIND_IN_SET( parentId, @ids ) ) AS cids\n" +
            "\tFROM\n" +
            "\t\tds_resource,\n" +
            "\t\t( SELECT @ids\\:=?1) b\n" +
            "\tWHERE\n" +
            "\t\t@ids IS NOT NULL\n" +
            "\t) ID,\n" +
            "\tds_resource DATA\n" +
            "WHERE\n" +
            "\tFIND_IN_SET( DATA.id, ID._ids ) and status = 1", nativeQuery = true)
    List<Resource> findAvaResourceByParentIDWithParent(String parentID);


    @Query(value = "SELECT t2.*\n" +
            "FROM (\n" +
            "         SELECT @pid                                                                                             AS _id,\n" +
            "                (SELECT @pid \\:= ds_resource.parentId\n" +
            "                 FROM ds_resource\n" +
            "                 WHERE id = _id\n" +
            "                   and ds_resource.status = 1)                                                                   AS parent_id\n" +
            "         FROM (SELECT @pid \\:= (select parentId from ds_resource where id = ?1)) vars,\n" +
            "              ds_resource\n" +
            "         WHERE @pid <> ''\n" +
            "     ) t1\n" +
            "         JOIN ds_resource t2\n" +
            "              ON t1._id = t2.Id", nativeQuery = true)
    List<Resource> findAvaParentResourceByIDWithSelf(String rscID);


    @Query(value = "SELECT @pid                           AS pid,\n" +
            "                (SELECT @pid \\:= ds_resource.parentId\n" +
            "                 FROM ds_resource\n" +
            "                 WHERE id = pid\n" +
            "                   and pid is not null\n" +
            "                   and ds_resource.status = 1) AS parentId\n" +
            "         FROM (SELECT\n" +
            "                   @pid \\:= (select parentId from ds_resource where id = ?1)) vars,\n" +
            "              ds_resource\n" +
            "         WHERE @pid <> ''", nativeQuery = true)
    List<Map<String , String>> findAvaParentResourceByIDWithSelfMap(String rscID);


    @Query(value = "select * from DS_RESOURCE  where status = 1", nativeQuery = true)
    List<Resource> findAllAvailable();

    @Query(value = "select distinct dr.id ,\n" +
            "dr.code as code\n" +
            ",dr.icon as icon\n" +
            ",dr.isshow as isShow\n" +
            ",dr.name as name\n" +
            ",dr.parentid as  parentId\n" +
            ",dr.servicename as  serviceName\n" +
            ",dr.sort as sort\n" +
            ",dr.status as status\n" +
            ",dr.type as type\n" +
            ",dr.url as url\n" +
            ",dr.isselected as isSelected\n" +
            ",dr.ip as ip\n" +
            ",dr.port as port\n" +
            ",dr.pluginsize as pluginSize\n" +
            ",dr.icontype as iconType\n" +
            ",dr.deskicon as deskIcon\n" +
            ",dr.handledeveloper as handleDeveloper\n" +
            ",dr.handleuser as handleUser\n" +
            ",dr.is_external_url as isExternalUrl\n" +
            ",img.name as imgIconName, CAST(img.resource AS CHAR)  as imgIconResource  \n" +
            " from DS_RESOURCE dr left JOIN icon_img img on dr.icon =img.id  where status = 1", nativeQuery = true)
    List<Object[]> findAllAvailableWithIcon();

    @Query(value = "select * from DS_RESOURCE a left JOIN icon_img b on a.icon =b.id  where status = 1", nativeQuery = true)
    List<Resource> findAllAvailableAndIcon();


    @Query(value = "select * from DS_RESOURCE where status = 1 and type = ?1", nativeQuery = true)
    List<AvailableAndIcon> findAllAvailableByType(String type);

    @Query(value = "select * from ds_resource\n" +
            "where id = (\n" +
            "select parentId from ds_resource where id = ?1)\n" +
            "and status = 1", nativeQuery = true)
    Resource findAvailableParentByRscID(String rcsID);


    @Modifying
    @Query(value = "delete from ds_resource where id in (?1)", nativeQuery = true)
    void deleteResourcesByRcsIDs(List<String> rcsIDs);

    @Query(value = "select distinct(handleUser)"
            + " from Resource"
            + " where status = 1 "
            + "  and handleUser is not null"
            + "  and handleUser <> ''")
    List<String> findAllHandleUser();

    @Query(value = "select handleUser, count(1) num"
            + " from ds_resource"
            + " where status = 1 "
            + "  and handleUser is not null"
            + "  and handleUser <> ''"
            + "  and if(?1 is not null && ?1 != '', handleUser like %?1% , 1=1)"
            + " group by handleUser", nativeQuery = true)
    List<Map<String, Object>> findAllHandleUserNum(String userNo);


    @Query(value = "select approveUser as userNo, id\n" +
            "from ds_resource\n" +
            "where id in ?1\n" +
            "group by userNo , id" , nativeQuery = true)
    List<Map<String , String>> findApplyPersonByRcsIDs(List<String> rcsIDs);

}
