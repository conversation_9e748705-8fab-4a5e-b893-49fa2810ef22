package iet.ustb.sf.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import iet.ustb.sf.dao.CoordinateMatterDao;
import iet.ustb.sf.vo.domain.CoordinateMatter;
import iet.ustb.sf.vo.eo.CoordinateMatterEO;
import iet.ustb.sf.service.CoordinateMatterService;
import iet.ustb.sf.service.FeedBackService;
import iet.ustb.sf.utils.DateUtils;
import iet.ustb.sf.utils.ToolsUtil;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.Cleanup;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 协调事宜serviceImpl
 *
 * <AUTHOR>
 * @create 2022-12-16
 * @see FeedBackService
 */
@Service
public class CoordinateMatterServiceImpl implements CoordinateMatterService {
    @Autowired
    private CoordinateMatterDao coordinateMatterDao;

    @Override
    public List<CoordinateMatter> findAll() {
        return coordinateMatterDao.findAll();
    }

    @Override
    public Page<CoordinateMatter> findPageByMultiCondition(JSONObject jsonObject) {
        Pageable pageable = ToolsUtil.initPage(jsonObject);
        return coordinateMatterDao.findAll(createSpecs(jsonObject), pageable);
    }

    private Specification<CoordinateMatter> createSpecs(JSONObject json) {
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            String matter = json.getString("matter");// 协调事项
            String matterType = json.getString("matterType");// 事项类型 ：1-协调事项 2-会议要求 3-遗留事项
            String presenter = json.getString("presenter");// 提出人
            String handleUnit = json.getString("handleUnit");// 责任单位
            String handlePerson = json.getString("handlePerson");// 责任人
            String handleStatus = json.getString("handleStatus");// 处理状态：1-是 2-否 3-进行中
            String startCreateDate = json.getString("startCreateDate");// 开始创建时间
            String endCreateDate = json.getString("endCreateDate");// 结束创建时间

            if (StringUtils.isNotBlank(matter)) {
                list.add(cb.like(root.get("matter"), "%" + matter + "%"));
            }
            if (StringUtils.isNotBlank(matterType)) {
                list.add(cb.equal(root.get("matterType"), matterType));
            }
            if (StringUtils.isNotBlank(presenter)) {
                list.add(cb.like(root.get("presenter"), "%" + presenter + "%"));
            }
            if (StringUtils.isNotBlank(handleUnit)) {
                list.add(cb.like(root.get("handleUnit"), "%" + handleUnit + "%"));
            }
            if (StringUtils.isNotBlank(handlePerson)) {
                list.add(cb.like(root.get("handlePerson"), "%" + handlePerson + "%"));
            }
            if (StringUtils.isNotBlank(handleStatus)) {
                list.add(cb.equal(root.get("handleStatus"), handleStatus));
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (StringUtils.isNotBlank(startCreateDate)) {
                list.add(cb.greaterThanOrEqualTo(root.get("taskDate"),
                        DateUtils.str2Date(startCreateDate + " 00:00:00", sdf)));
            }
            if (StringUtils.isNotBlank(endCreateDate)) {
                list.add(cb.lessThanOrEqualTo(root.get("taskDate"),
                        DateUtils.str2Date(endCreateDate + " 23:59:59", sdf)));
            }
            query.where(list.toArray(new Predicate[list.size()]));

            List<Order> orderList = new ArrayList<>();
            orderList.add(cb.asc(root.get("priority")));
            orderList.add(cb.desc(root.get("taskDate")));
            orderList.add(cb.desc(root.get("createDateTime")));
            query.orderBy(orderList);

//            query.multiselect(root.get("matterType"), cb.count(root.get("id")));
//            query.groupBy(root.get("matterType"));// 分组
            return query.getRestriction();
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CoordinateMatter save(CoordinateMatter coordinateMatter) {
        return coordinateMatterDao.save(coordinateMatter);
    }

    @Override
    public void deleteByIds(JSONObject jsonObject) {
        List<String> idList = new ArrayList<>();
        JSONArray idJsonArray = jsonObject.getJSONArray("ids");
        for (int i = 0; i < idJsonArray.size(); i++) {
            idList.add(idJsonArray.getString(i));
        }
        coordinateMatterDao.deleteAllById(idList);
    }

    @Override
    public void exportExcel(JSONObject jsonObject, HttpServletResponse response) throws Exception {
        {
            ExportParams exportParams = new ExportParams();
            // 设置sheet得名称
            exportParams.setTitle("协调事宜");
            // 设置sheet表头名称
            exportParams.setSheetName("协调事宜");
            // 查找所有
            List<CoordinateMatter> coordinateMatterList = coordinateMatterDao.findAll(createSpecs(jsonObject));
            List<CoordinateMatterEO> eoList = new ArrayList<>();
            for (CoordinateMatter coordinateMatter : coordinateMatterList) {
                CoordinateMatterEO coordinateMatterEO = new CoordinateMatterEO();
                coordinateMatterEO.setMatter(coordinateMatter.getMatter());
                eoList.add(coordinateMatterEO);
            }
            // 执行方法
            @Cleanup Workbook workBook = ExcelExportUtil.exportExcel(exportParams, CoordinateMatterEO.class, eoList);
            //设置编码格式
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            //设置内容类型
            response.setContentType("application/octet-stream");
            //设置头及文件命名。
            response.setHeader("Content-Disposition", "attachment;filename="
                    + URLEncoder.encode("协调事宜.xls", StandardCharsets.UTF_8.name()));
            //写出流
            @Cleanup ServletOutputStream outputStream = response.getOutputStream();
            workBook.write(outputStream);
        }
    }

    @Override
    public void topping(JSONObject jsonObject) {
        String id = jsonObject.getString("id");
        Boolean flag = jsonObject.getBoolean("flag");

        Assert.hasText(id, "id cannot be empty");
        Assert.notNull(flag, "flag cannot be null");

        CoordinateMatter coordinateMatter = coordinateMatterDao.findById(id).orElse(null);

        Assert.notNull(coordinateMatter, "根据id未查到数据");

        if (flag) {// 置顶
            Integer maxPriority = coordinateMatterDao.findMaxPriority();
            maxPriority = maxPriority != null ? maxPriority : 0;
            coordinateMatter.setPriority(maxPriority + 1);
        } else {// 取消置顶
            coordinateMatter.setPriority(null);
        }
        coordinateMatterDao.save(coordinateMatter);
    }

    @Override
    public String findGroupByMatterType(JSONObject jsonObject) {
        List<CoordinateMatter> matterList = coordinateMatterDao.findAll(createSpecs(jsonObject));
        StringBuilder sb = new StringBuilder();
        Map<Integer, Long> map = matterList.stream().collect(Collectors.groupingBy(CoordinateMatter::getMatterType, Collectors.counting()));
        for(Map.Entry<Integer, Long> entry : map.entrySet()) {
            if (entry.getKey() == 1) {
                sb.append("协调事项:" + entry.getValue() + "项 ");
            }
            if (entry.getKey() == 2) {
                sb.append("会议要求:" + entry.getValue() + "项 ");
            }
            if (entry.getKey() == 3) {
                sb.append("遗留事宜:" + entry.getValue() + "项 ");
            }
            if (entry.getKey() == 4) {
                sb.append("风险事项:" + entry.getValue() + "项 ");
            }
        }
        return sb.toString();
    }

}
