package iet.ustb.sf.dao;

import iet.ustb.sf.vo.domain.Comment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface CommentDao extends JpaSpecificationExecutor<Comment>, JpaRepository<Comment, String> {

    /**
     * 按问题反馈id查找
     *
     * @param feedBackId 反馈id
     * @return {@link List }<{@link Comment }>
     * <AUTHOR>
     * @create 2022-11-03
     */
    @Query(value = "from Comment where feedBackId = ?1")
    List<Comment> findByFeedBackId(String feedBackId);
}
